<?php

namespace App\Entity;

use App\Behavior\Timezone;
use App\Repository\TaskUserRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=TaskUserRepository::class)
 */
class TaskUser
{
    use Blamable;
    use Timestampable;
    use Timezone;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
    * @Groups({"detail"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="taskUsers")
     * @Groups({"detail"})
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity=TaskCourse::class, inversedBy="taskUsers")
     */
    private $task;

    /**
     * @ORM\OneToMany(targetEntity=HistoryDeliveryTask::class, mappedBy="taskUser")
     * @Groups({"detail"})
     */
    private $historyDeliveryTasks;

    public function __construct()
    {
     
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
        $this->historyDeliveryTasks = new ArrayCollection();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getTask(): ?TaskCourse
    {
        return $this->task;
    }

    public function setTask(?TaskCourse $task): self
    {
        $this->task = $task;

        return $this;
    }

    /**
     * @return Collection<int, HistoryDeliveryTask>
     */
    public function getHistoryDeliveryTasks(): Collection
    {
        return $this->historyDeliveryTasks;
    }

    public function addHistoryDeliveryTask(HistoryDeliveryTask $historyDeliveryTask): self
    {
        if (!$this->historyDeliveryTasks->contains($historyDeliveryTask)) {
            $this->historyDeliveryTasks[] = $historyDeliveryTask;
            $historyDeliveryTask->setTaskUser($this);
        }

        return $this;
    }

    public function removeHistoryDeliveryTask(HistoryDeliveryTask $historyDeliveryTask): self
    {
        if ($this->historyDeliveryTasks->removeElement($historyDeliveryTask)) {
            // set the owning side to null (unless already changed)
            if ($historyDeliveryTask->getTaskUser() === $this) {
                $historyDeliveryTask->setTaskUser(null);
            }
        }

        return $this;
    }
}
