<?php

namespace App\Entity;

use App\Repository\ChallengeDuelRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;


/**
 * @ORM\HasLifecycleCallbacks()
 * @ORM\Entity(repositoryClass=ChallengeDuelRepository::class)
 */
class ChallengeDuel
{
    use AtAndBy;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"challengeduel","duelQuestions","challenge_user_statistics","challenge_duel_details","challenge_info"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Challenge::class, inversedBy="challengeDuels")
     * @Groups({"challengeduel","duelQuestions","challenge_duel_details"})
     */
    private $challenge;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="challengeDuels")
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $user1;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="challengeDuels")
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $user2;

    /**
     * @ORM\Column(type="integer", nullable=true)
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $winner;

    /**
     * @ORM\OneToMany(targetEntity=ChallengeDuelQuestions::class, mappedBy="duel")
     */
    private $challengeDuelQuestions;

    /**
     * @ORM\ManyToOne(targetEntity=Bots::class, inversedBy="challengeDuels")
     * @Groups({"challenge_user_statistics","challenge_duel_details"})
     */
    private $assigned_bot;

    public function __construct()
    {
        $this->challengeDuelQuestions = new ArrayCollection();
    }

    public function __toString(): string
    {
        return "";
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChallenge(): ?Challenge
    {
        return $this->challenge;
    }

    public function setChallenge(?Challenge $challenge): self
    {
        $this->challenge = $challenge;

        return $this;
    }

    public function getUser1(): ?User
    {
        return $this->user1;
    }

    public function setUser1(?User $user1): self
    {
        $this->user1 = $user1;

        return $this;
    }

    public function getUser2(): ?User
    {
        return $this->user2;
    }

    public function setUser2(?User $user2): self
    {
        $this->user2 = $user2;

        return $this;
    }

    public function getWinner(): ?int
    {
        return $this->winner;
    }

    public function setWinner(?int $winner): self
    {
        $this->winner = $winner;

        return $this;
    }

    /**
     * @return Collection|ChallengeDuelQuestions[]
     */
    public function getChallengeDuelQuestions(): Collection
    {
        return $this->challengeDuelQuestions;
    }

    public function addChallengeDuelQuestion(ChallengeDuelQuestions $challengeDuelQuestion): self
    {
        if (!$this->challengeDuelQuestions->contains($challengeDuelQuestion)) {
            $this->challengeDuelQuestions[] = $challengeDuelQuestion;
            $challengeDuelQuestion->setDuel($this);
        }

        return $this;
    }

    public function removeChallengeDuelQuestion(ChallengeDuelQuestions $challengeDuelQuestion): self
    {
        if ($this->challengeDuelQuestions->removeElement($challengeDuelQuestion)) {
            // set the owning side to null (unless already changed)
            if ($challengeDuelQuestion->getDuel() === $this) {
                $challengeDuelQuestion->setDuel(null);
            }
        }

        return $this;
    }

    public function getAssignedBot(): ?Bots
    {
        return $this->assigned_bot;
    }

    public function setAssignedBot(?Bots $assigned_bot): self
    {
        $this->assigned_bot = $assigned_bot;

        return $this;
    }

}
