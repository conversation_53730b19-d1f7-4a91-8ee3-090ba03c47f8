<?php

namespace App\Admin\Traits;

use Doctrine\Common\Annotations\AnnotationReader;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

trait SerializerTrait
{
    public array $normalizers = [];

    protected function sendResponse($response, $context = []): Response
    {
        return new Response($this->serialize($response, $context), $response['status'], ['content-type' => 'application/json', 'Access-Control-Allow-Origin' => '*']);
    }


    public function serialize($response, $context = [])
    {
        if (empty($context)) {
            $normalizer = new ObjectNormalizer();
        } else {
            $classMetadataFactory       = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
            $metadataAwareNameConverter = new MetadataAwareNameConverter($classMetadataFactory);
            $normalizer                 = new ObjectNormalizer($classMetadataFactory, $metadataAwareNameConverter);
        }



        $normalizers = array_merge($this->getNormalizers($normalizer), [new DateTimeNormalizer($this->getFormatDate()), $normalizer]);
        $serializer = new Serializer($normalizers, [new JsonEncoder()]);

        return $serializer->serialize($response, 'json', $context);
    }


    public function normalize($object, $context = [])
    {
        if (empty($context)) {
            $normalizer = new ObjectNormalizer();
        } else {
            $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
            $metadataAwareNameConverter = new MetadataAwareNameConverter($classMetadataFactory);
            $normalizer = new ObjectNormalizer($classMetadataFactory, $metadataAwareNameConverter);
        }
        $serializer = new Serializer([new DateTimeNormalizer($this->getFormatDate()), $normalizer], [new JsonEncoder()]);
        return $serializer->normalize($object, null, $context);
    }


    public function getNormalizers($objectNormalizer): array
    {
        $normalizers = [];
        foreach ($this->normalizers as $normalizer) {
            $object = null;
            if (is_string($normalizer)) {
                $object = new $normalizer($objectNormalizer, $this->container, $this->translator);
            } elseif (is_object($normalizer) and $normalizer instanceof NormalizerInterface) {
                $object = $normalizer;
            }

            $normalizers[] = $object;
        }

        return $normalizers;
    }

    private function getFormatDate()
    {

        //TODO: implement in the dates iso 8601
        //   return ['datetime_format' => 'c'];

        $locale = $this->getUser() && $this->getUser()->getLocale() ? $this->getUser()->getLocale() : 'es';

        return $locale === 'en' ?  ['datetime_format' => 'Y-m-d H:i:s'] : ['datetime_format' => 'd-m-Y H:i:s'];
    }
}
