<?php

declare(strict_types=1);

namespace App\Admin\Traits;

use Symfony\Component\Intl\Locales;

trait LanguagesTrait
{
    protected function getLanguages()
    {
        $locales = Locales::getNames(null);
        $languages = [];

        // comprobar que existe una variable
        if (isset($this->settings)) {
            foreach ($this->settings->get('app.languages') as $language) {
                if (isset($locales[$language])) {
                    $name = ucfirst(Locales::getName($language, $language));
                    $languages[$name] = $language;
                }
            }
        } else {
            foreach ($this->getParameter('app.languages') as $language) {
                if (isset($locales[$language])) {
                    $name = ucfirst(Locales::getName($language, $language));
                    $languages[$name] = $language;
                }
            }
        }

        return $languages;
    }

    protected function getLanguagesAdmin()
    {
        $locales = Locales::getNames(null);
        $languagesAdmin = [];

        foreach ($this->getParameter('app.languages.admin') as $language) {
            if (isset($locales[$language])) {
                $name = ucfirst(Locales::getName($language, $language));
                $languagesAdmin[$name] = $language;
            }
        }

        return $languagesAdmin;
    }
}
