<?php

namespace App\Admin\Filter;


use App\Entity\Course;
use App\Form\Type\Admin\Filter\CourseLocaleFilterType;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Filter\FilterInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FilterDataDto;
use EasyCorp\Bundle\EasyAdminBundle\Filter\FilterTrait;

class CourseLocaleFilter implements FilterInterface
{
    use FilterTrait;

    public static function new (string $propertyName, $label = null): self
    {
        return (new self())
            ->setFilterFqcn(__CLASS__)
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(CourseLocaleFilterType::class);
    }


    public function apply (QueryBuilder $queryBuilder, FilterDataDto $filterDataDto, ?FieldDto $fieldDto, EntityDto $entityDto): void
    {
        $em = $queryBuilder->getEntityManager();

        $subquery = $em->createQueryBuilder()
            ->select('IDENTITY(ct.translation)')
            ->from(Course::class, 'ct')
            ->andWhere('ct.locale = :locale')
            ->getDQL();

        if ($filterDataDto->getComparison() == '=')
        {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq($filterDataDto->getEntityAlias() . '.locale', ':locale'),
                    $queryBuilder->expr()->in($filterDataDto->getEntityAlias() . '.id', $subquery),
                ))
                ->setParameter('locale', $filterDataDto->getValue());
        }
        else if ($filterDataDto->getComparison() == '!=')
        {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->neq($filterDataDto->getEntityAlias() . '.locale', ':locale'),
                    $queryBuilder->expr()->notIn($filterDataDto->getEntityAlias() . '.id', $subquery),
                ))
                ->setParameter('locale', $filterDataDto->getValue());
        }
    }
}
