<?php

namespace App\Twig;

use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class AppExtension extends AbstractExtension
{
    private TranslatorInterface $translator;
    private Security $security;

    public function __construct(TranslatorInterface $translator, Security $security)
    {
        $this->translator = $translator;
        $this->security   = $security;
    }

    public function getFilters()
    {
        return [
            new TwigFilter('niceTime', [$this, 'niceTime']),
            new TwigFilter('reportTimeFormat', [$this, 'reportTimeFormat']),
            new TwigFilter('propertySum', [$this, 'propertySum']),
        ];
    }


    public function niceTime(?int $seconds)
    {
        if (is_null($seconds) or $seconds === 0) {
            return '-';
        }

        $hours = floor($seconds / 3600);
        $mins  = floor(($seconds - $hours * 3600) / 60);

        $return = [];
        if ($hours > 0) {
            $return[] = $hours . ' ' . ($hours == 1
                ? $this->translator->trans('course.stats.hour', [], 'messages', $this->security->getUser()->getLocale())
                : $this->translator->trans('course.stats.hours', [], 'messages', $this->security->getUser()->getLocale())
            );
        }
        if ($mins > 0) {
            $return[] = $mins . ' ' . ($mins == 1
                ? $this->translator->trans('course.stats.minute', [], 'messages', $this->security->getUser()->getLocale())
                : $this->translator->trans('course.stats.minutes', [], 'messages', $this->security->getUser()->getLocale())
            );
        }

        if (empty($return)) {
            $return[] = $seconds . ' ' . ($seconds == 1
                ? $this->translator->trans('course.stats.second', [], 'messages', $this->security->getUser()->getLocale())
                : $this->translator->trans('course.stats.seconds', [], 'messages', $this->security->getUser()->getLocale())
            );
        }

        return implode(' ', $return);
    }

    public function reportTimeFormat(?int $seconds){
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds %= 60;

        return sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
    }


    public function propertySum($collection, $property)
    {
        $sum    = 0;
        $method = 'get' . ucfirst($property);

        foreach ($collection as $item) {
            if (method_exists($item, $method)) {
                $sum += call_user_func([$item, $method]);
            }
        }

        return $sum;
    }
}
