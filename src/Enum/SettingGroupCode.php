<?php

declare(strict_types=1);

namespace App\Enum;

final class SettingGroupCode
{
    public const PLATFORM = 'PLATFORM';
    public const ANNOUNCEMENTS = 'ANNOUNCEMENTS';
    public const COURSES = 'COURSES';
    public const VIMEO_GENERAL = 'VIMEO_GENERAL';
    public const VIMEO_CLIENT = 'VIMEO_CLIENT';
    public const EMAIL = 'EMAIL';
    public const MODULES = 'MODULES';
    public const EXPORTS = 'EXPORTS';
    public const VIRTUAL_CLASSROOM = 'VIRTUAL_CLASSROOM';
    public const LIBRARIES = 'LIBRARIES';
    public const CHALLENGES = 'CHALLENGES';
    public const USER = 'USER';
    public const STATS = 'STATS';
    public const APIV1 = 'APIV1';
    public const FUNDAE = 'FUNDAE';
    public const HELP = 'HELP';
    public const SURVEY = 'SURVEY';
    public const INTEGRATIONS = 'INTEGRATIONS';
    public const SAML = 'SAML';
}
