<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Campus;

use App\V2\Application\Command\CreatePurchasePaymentCommand;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Purchase\Payment\PaymentTransformer;
use App\V2\Infrastructure\Request\Exception\RequestAttributeExtractorException;
use App\V2\Infrastructure\Request\RequestAttributeExtractor;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostCreatePurchasePaymentController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws RequestAttributeExtractorException
     * @throws InvalidUuidException
     */
    public function __invoke(string $purchaseId, Request $request): Response
    {
        UuidValidator::validateUuid($purchaseId);
        $user = RequestAttributeExtractor::extractUser($request);

        $payment = $this->execute(
            new CreatePurchasePaymentCommand(
                purchaseId: new Uuid($purchaseId),
                userId: new Id($user->getId()),
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                data: PaymentTransformer::toArray($payment),
            )
                ->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
