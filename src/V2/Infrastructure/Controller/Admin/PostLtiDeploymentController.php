<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PostLtiDeploymentCommand;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiDeploymentValidator;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostLtiDeploymentController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUuidException
     */
    public function __invoke(Request $request, string $registrationId): Response
    {
        UuidValidator::validateUuid($registrationId);

        $data = json_decode($request->getContent(), true);

        LtiDeploymentValidator::validatePostLtiDeployment($data);

        $this->execute(
            new PostLtiDeploymentCommand(
                registrationId: new Uuid($registrationId),
                name: $data['name'],
                deploymentId: $data['deployment_id'],
            )
        );

        return new JsonResponse(
            data: [],
            status: Response::HTTP_CREATED
        );
    }
}
