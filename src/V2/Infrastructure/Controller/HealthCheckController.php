<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller;

use App\V2\Application\Query\HealthCheck;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class HealthCheckController extends QueryBusAccessor
{
    public function __invoke(Request $request): Response
    {
        $this->ask(new HealthCheck());

        return new JsonResponse(
            status: Response::HTTP_NO_CONTENT,
        );
    }
}
