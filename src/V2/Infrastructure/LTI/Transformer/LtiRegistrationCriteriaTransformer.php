<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI\Transformer;

use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class LtiRegistrationCriteriaTransformer
{
    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public static function fromArray(array $data): LtiRegistrationCriteria
    {
        $criteria = LtiRegistrationCriteria::createEmpty();
        if (isset($data['id'])) {
            $criteria->filterById(new Uuid($data['id']));
        }

        if (isset($data['name'])) {
            $criteria->filterBySearchString($data['name']);
        }

        if (isset($data['client_id'])) {
            $criteria->filterByClientId($data['client_id']);
        }

        return $criteria;
    }
}
