<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI\Transformer;

use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiTool;

class LtiRegistrationTransformer
{
    public static function toArray(LtiRegistration $registration): array
    {
        return array_merge(
            [
                'id' => $registration->getId()->value(),
                'name' => $registration->getName(),
                'client_id' => $registration->getClientId(),
            ],
            $registration->getTool() ? ['tool' => self::fromToolToArray($registration->getTool())] : [],
            $registration->getPlatform() ? ['platform' => self::fromPlatformToArray($registration->getPlatform())] : [],
            $registration->getDeployments() ? [
                'deployments' => self::fromDeploymentCollectionToArray($registration->getDeployments()),
            ] : [],
        );
    }

    public static function fromCollectionToArray(LtiRegistrationCollection $collection): array
    {
        return array_map(
            fn (LtiRegistration $registration) => self::toArray($registration),
            $collection->all()
        );
    }

    private static function fromToolToArray(LtiTool $tool): array
    {
        return [
            'id' => $tool->getId()->value(),
            'name' => $tool->getName(),
            'audience' => $tool->getAudience(),
            'oidc_initiation_url' => $tool->getOidcInitiationUrl()->value(),
            'launch_url' => $tool->getLaunchUrl()->value(),
            'deep_linking_url' => $tool->getDeeplinkingUrl()->value(),
            'jwks_url' => $tool->getJwksUrl()->value(),
        ];
    }

    private static function fromPlatformToArray(LtiPlatform $platform): array
    {
        return [
            'id' => $platform->getId()->value(),
            'name' => $platform->getName(),
            'audience' => $platform->getAudience(),
            'oidc_authentication_url' => $platform->getOidcAuthenticationUrl()->value(),
            'oauth2_access_token_url' => $platform->getOauth2AccessTokenUrl()->value(),
            'jwks_url' => $platform->getJwksUrl()->value(),
        ];
    }

    private static function fromDeploymentCollectionToArray(LtiDeploymentCollection $collection): array
    {
        return array_map(
            fn (LtiDeployment $deployment) => [
                'id' => $deployment->getId()->value(),
                'name' => $deployment->getName(),
                'deployment_id' => $deployment->getDeploymentId(),
            ],
            $collection->all(),
        );
    }
}
