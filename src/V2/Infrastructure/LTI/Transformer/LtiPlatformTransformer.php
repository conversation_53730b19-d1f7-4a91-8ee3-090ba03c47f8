<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI\Transformer;

use App\V2\Domain\LTI\LtiPlatform;
use OAT\Library\Lti1p3Core\Platform\Platform as CorePlatform;
use OAT\Library\Lti1p3Core\Platform\PlatformInterface;

class LtiPlatformTransformer
{
    public static function fromLtiPlatformToCorePlatform(LtiPlatform $ltiPlatform): PlatformInterface
    {
        return new CorePlatform(
            identifier: $ltiPlatform->getId()->value(),
            name: $ltiPlatform->getName(),
            audience: $ltiPlatform->getAudience(),
            oidcAuthenticationUrl: $ltiPlatform->getOidcAuthenticationUrl()?->value(),
            oAuth2AccessTokenUrl: $ltiPlatform->getOauth2AccessTokenUrl()?->value(),
        );
    }
}
