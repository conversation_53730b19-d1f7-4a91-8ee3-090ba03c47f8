<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiUnauthorizedException;
use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use OAT\Library\Lti1p3Core\Registration\RegistrationInterface;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResult;
use OAT\Library\Lti1p3Core\Security\User\Result\UserAuthenticationResultInterface;
use OAT\Library\Lti1p3Core\Security\User\UserAuthenticatorInterface;
use OAT\Library\Lti1p3Core\User\UserIdentity;

final readonly class UserAuthenticator implements UserAuthenticatorInterface
{
    public function __construct(
        private UserRepository $userRepository,
        private LtiKeyProvider $ltiKeyProvider,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws LtiUnauthorizedException
     * @throws CriteriaException
     */
    public function authenticate(
        RegistrationInterface $registration,
        string $loginHint
    ): UserAuthenticationResultInterface {
        [$userInfo, $signature] = explode('.', $loginHint);

        $userInfo = base64_decode($userInfo);
        if (!$this->ltiKeyProvider->verify($userInfo, base64_decode($signature))) {
            throw new LtiUnauthorizedException();
        }

        $userInfo = json_decode($userInfo, true);

        try {
            $user = $this->userRepository->findOneBy(
                UserCriteria::createById(new Id($userInfo['id']))
            );
        } catch (UserNotFoundException) {
            throw new LtiUnauthorizedException();
        }

        return new UserAuthenticationResult(
            success: true,
            userIdentity: new UserIdentity(
                identifier: (string) $user->getId(),
                email: $user->getEmail(),
                givenName: $user->getFirstName(),
                familyName: $user->getLastName(),
                locale: $user->getLocaleCampus(),
            )
        );
    }
}
