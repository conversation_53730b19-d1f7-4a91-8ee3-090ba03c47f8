<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Announcement\Session;

use App\V2\Application\Query\GetVirtualMeetings;
use App\V2\Domain\Bus\QueryBus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use App\V2\Domain\VirtualMeeting\PaginatedVirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingTransformer;

/**
 * Class VirtualMeetingPayloadHydrator.
 *
 * Service to hydrate announcement group session response data
 * with virtual meeting information.
 *
 * Supports legacy code announcements response data structure.
 */
readonly class AnnouncementGroupSessionResponseVirtualMeetingDataHydrator
{
    public function __construct(
        private QueryBus $queryBus,
    ) {
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function hydrate(array $groupsResponseData): array
    {
        $virtualMeetingIds = [];

        foreach ($groupsResponseData as $group) {
            foreach ($group['sessions'] as $session) {
                if (null !== $session['virtual_meeting_id']) {
                    $virtualMeetingIds[] = $session['virtual_meeting_id'];
                }
            }
        }

        $virtualMeetingIds = array_unique($virtualMeetingIds);

        if (empty($virtualMeetingIds)) {
            return $groupsResponseData;
        }

        $virtualMeetingIdCollection = new UuidCollection(
            array_map(
                fn ($id) => new Uuid($id),
                $virtualMeetingIds,
            )
        );

        /** @var PaginatedVirtualMeetingCollection $paginatedVirtualMeetingCollection */
        $paginatedVirtualMeetingCollection = $this->queryBus->ask(
            new GetVirtualMeetings(
                criteria: VirtualMeetingCriteria::createByIds($virtualMeetingIdCollection)
            )
        );

        if (0 === $paginatedVirtualMeetingCollection->getTotalItems()) {
            return $groupsResponseData;
        }

        /** @var VirtualMeetingCollection $virtualMeetings */
        $virtualMeetings = $paginatedVirtualMeetingCollection->getCollection();
        $virtualMeetingsById = $virtualMeetings->allIndexedById();

        foreach ($groupsResponseData as $groupIndex => $group) {
            foreach ($group['sessions'] as $sessionIndex => $session) {
                if (null !== $session['virtual_meeting_id']) {
                    if (isset($virtualMeetingsById[$session['virtual_meeting_id']])) {
                        $groupsResponseData[$groupIndex]['sessions'][$sessionIndex]['virtual_meeting']
                            = VirtualMeetingTransformer::fromVirtualMeetingToArray(
                                virtualMeeting: $virtualMeetingsById[$session['virtual_meeting_id']]
                            );
                    }
                }
            }
        }

        return $groupsResponseData;
    }
}
