<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\User;

use App\Entity\User;
use App\V2\Domain\User\PaginatedUserCollection;
use App\V2\Domain\User\UserCollection;

class UserTransformer
{
    /**
     * Transform a PaginatedUserCollection to an array for API response.
     */
    public static function fromPaginatedCollectionToArray(PaginatedUserCollection $paginatedCollection): array
    {
        return [
            'users' => self::fromCollectionToArray($paginatedCollection->getCollection()),
            'total' => $paginatedCollection->getTotalItems(),
        ];
    }

    /**
     * Transform a UserCollection to an array of user data.
     */
    public static function fromCollectionToArray(UserCollection $collection): array
    {
        $result = [];

        foreach ($collection->all() as $user) {
            $result[] = self::fromUserToArrayForListing($user);
        }

        return $result;
    }

    /**
     * Transform a User entity to an array with specific fields for listing.
     */
    public static function fromUserToArrayForListing(User $user): array
    {
        return [
            'id' => $user->getId(),
            'avatar' => $user->getAvatar(),
            'email' => $user->getEmail(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
            'roles' => $user->getRoles(),
            'is_active' => $user->getIsActive(),
            'points' => $user->getPoints(),
        ];
    }
}
