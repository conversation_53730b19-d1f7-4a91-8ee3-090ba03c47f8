<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;

class PaymentTransformer
{
    public static function toArray(Payment $payment): array
    {
        return [
            'id' => $payment->getId()->value(),
            'purchase_id' => $payment->getPurchaseId()->value(),
            'payment_intent' => $payment->getPaymentIntent(),
            'amount' => $payment->getAmount()->value(),
            'currency_code' => CurrencyCodeTransformer::toString($payment->getAmount()->currency()->code()),
            'status' => PaymentStatusTransformer::toString($payment->getStatus()),
        ];
    }
}
