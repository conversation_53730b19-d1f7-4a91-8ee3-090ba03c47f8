<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use Stripe\Exception\ApiErrorException;

readonly class StripePaymentManager implements PaymentManagerInterface
{
    public function __construct(
        private string $stripeSecretKey,
        private PaymentRepository $paymentRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws ApiErrorException
     * @throws PaymentRepositoryException
     */
    public function createPayment(Purchase $purchase): Payment
    {
        \Stripe\Stripe::setApiKey($this->stripeSecretKey);

        $paymentIntentConfig = [
            'payment_method_types' => ['card'],
            'amount' => $purchase->getAmount()->value(),
            'currency' => CurrencyCodeTransformer::toString($purchase->getAmount()->currency()->code()),
            'metadata' => ['integration_check' => 'accept_a_payment'],
        ];

        $paymentIntent = \Stripe\PaymentIntent::create($paymentIntentConfig);

        $payment = new Payment(
            id: $this->uuidGenerator->generate(),
            purchaseId: $purchase->getId(),
            userId: $purchase->getUserId(),
            paymentIntent: $paymentIntent->id,
            amount: $purchase->getAmount(),
            status: PaymentStatus::Pending,
        );

        $this->paymentRepository->put($payment);

        return $payment;
    }
}
