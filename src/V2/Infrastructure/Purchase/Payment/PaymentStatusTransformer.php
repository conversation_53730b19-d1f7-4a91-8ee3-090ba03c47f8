<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\V2\Domain\Purchase\Payment\PaymentStatus;

class PaymentStatusTransformer
{
    public static function toString(PaymentStatus $status): string
    {
        return match ($status) {
            PaymentStatus::Pending => 'pending',
            PaymentStatus::Paid => 'paid',
            PaymentStatus::Failed => 'failed',
        };
    }

    public static function fromString(string $status): PaymentStatus
    {
        return match ($status) {
            'pending' => PaymentStatus::Pending,
            'paid' => PaymentStatus::Paid,
            'failed' => PaymentStatus::Failed,
        };
    }
}
