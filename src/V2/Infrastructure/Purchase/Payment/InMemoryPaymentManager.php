<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class InMemoryPaymentManager implements PaymentManagerInterface
{
    public function __construct(
        private PaymentRepository $paymentRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws PaymentRepositoryException
     */
    public function createPayment(Purchase $purchase): Payment
    {
        $payment = new Payment(
            id: $this->uuidGenerator->generate(),
            purchaseId: $purchase->getId(),
            userId: $purchase->getUserId(),
            paymentIntent: 'pi_test_' . uniqid(), // Fake payment intent ID for testing
            amount: $purchase->getAmount(),
            status: PaymentStatus::Pending,
        );

        $this->paymentRepository->put($payment);

        return $payment;
    }
}
