<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\Entity\User;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseBillingData;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseItem;
use App\V2\Domain\Purchase\PurchaseItemCollection;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;

class PurchaseTransformer
{
    public static function fromPurchaseToArray(Purchase $purchase): array
    {
        return array_merge(
            [
                'id' => $purchase->getId()->value(),
                'user_id' => $purchase->getUserId()->value(),
                'status' => PurchaseStatusTransformer::toString($purchase->getStatus()),
                'amount' => $purchase->getAmount()->value(),
                'currency_code' => CurrencyCodeTransformer::toString($purchase->getAmount()->currency()->code()),
                'tax_rate' => $purchase->getTaxRate()->value(),
                'tax_amount' => $purchase->getTaxAmount()->value(),
                'created_at' => $purchase->getCreatedAt()->format('Y-m-d H:i:s'),
            ],
            $purchase->getPurchaseItems() ? [
                'items' => self::fromPurchaseItemCollectionToArray($purchase->getPurchaseItems()),
            ] : [],
            $purchase->getUser() ? [
                'user' => self::fromUserToArray($purchase->getUser()),
            ] : []
        );
    }

    public static function fromPurchaseItemCollectionToArray(PurchaseItemCollection $collection): array
    {
        return array_map(
            fn (PurchaseItem $purchaseItem) => self::fromPurchaseItemToArray($purchaseItem),
            $collection->all()
        );
    }

    public static function fromPurchaseCollectionToArray(PurchaseCollection $collection): array
    {
        return array_map(
            fn (Purchase $purchase) => self::fromPurchaseToArray($purchase),
            $collection->all()
        );
    }

    private static function fromPurchaseItemToArray(PurchaseItem $purchaseItem): array
    {
        return array_merge(
            [
                'id' => $purchaseItem->getId()->value(),

                'price_amount' => $purchaseItem->getPrice()->value(),
                'price_currency' => CurrencyCodeTransformer::toString($purchaseItem->getPrice()->currency()->code()),
            ],
            $purchaseItem->getPurchasableItem() ? [
                'purchasable_item' => PurchasableItemTransformer::fromPurchasableItemToArray(
                    $purchaseItem->getPurchasableItem()
                ),
            ] : []
        );
    }

    private static function fromUserToArray(User $user): array
    {
        return [
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'first_name' => $user->getFirstName(),
            'last_name' => $user->getLastName(),
        ];
    }

    public static function fromPayloadToPurchaseBillingData(array $payload): PurchaseBillingData
    {
        return new PurchaseBillingData(
            tin: $payload['tin'],
            firstName: $payload['first_name'],
            lastName: $payload['last_name'],
            address: $payload['address'],
            postalCode: $payload['postal_code'],
            city: $payload['city'],
            country: $payload['country'],
            metadata: $payload['metadata'] ?? [],
        );
    }
}
