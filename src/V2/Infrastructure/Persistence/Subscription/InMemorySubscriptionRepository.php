<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Subscription;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Subscription;
use App\V2\Domain\Subscription\SubscriptionCollection;
use App\V2\Domain\Subscription\SubscriptionCriteria;
use App\V2\Domain\Subscription\SubscriptionRepository;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemorySubscriptionRepository implements SubscriptionRepository
{
    private SubscriptionCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new SubscriptionCollection([]);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function put(Subscription $subscription): void
    {
        $subscriptions = $this->collection->allIndexedById();
        $subscriptions[$subscription->getId()->value()] = clone $subscription;
        $this->collection->replace($subscriptions);
    }

    /**
     * @throws SubscriptionNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(SubscriptionCriteria $criteria): Subscription
    {
        $subscriptions = $this->filterByCriteria($criteria);

        if ($subscriptions->isEmpty()) {
            throw new SubscriptionNotFoundException();
        }

        return $subscriptions->first();
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function findBy(SubscriptionCriteria $criteria): SubscriptionCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function delete(Subscription $subscription): void
    {
        $subscriptions = $this->collection->allIndexedById();
        unset($subscriptions[$subscription->getId()->value()]);
        $this->collection->replace($subscriptions);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(SubscriptionCriteria $criteria): SubscriptionCollection
    {
        $collection = $this->collection->filter(
            fn (Subscription $subscription) => (
                null === $criteria->getName()
                    || $subscription->getName() === $criteria->getName()
            ) && (
                null === $criteria->getSearch()
                    || str_contains($subscription->getName(), $criteria->getSearch())
                    || str_contains($subscription->getDescription(), $criteria->getSearch())
            )
        );

        /** @var SubscriptionCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (Subscription $subscription) => new Subscription(
                id: $subscription->getId(),
                name: $subscription->getName(),
                description: $subscription->getDescription(),
                createdAt: $subscription->getCreatedAt(),
                updatedAt: $subscription->getUpdatedAt(),
                deletedAt: $subscription->getDeletedAt(),
            )
        );
    }
}
