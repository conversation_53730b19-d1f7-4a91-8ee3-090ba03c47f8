<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Criteria\CriteriaWithId;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\ORM\QueryBuilder;

class DoctrineCommonCriteriaBuilder
{
    public static function filterByCommonCriteria(
        Criteria $criteria,
        QueryBuilder $queryBuilder,
        bool $onlyFilter = false,
    ): void {
        if ($criteria instanceof CriteriaWithId) {
            self::filterByCommonCriteriaId($criteria, $queryBuilder);
        }

        if ($onlyFilter) {
            return;
        }

        if ($criteria->getPagination()) {
            $queryBuilder->setFirstResult($criteria->getPagination()->offset());
            $queryBuilder->setMaxResults($criteria->getPagination()->limit());
        }

        // Apply sorting if set
        $sortBy = $criteria->getSortBy();
        if (null !== $sortBy) {
            foreach ($sortBy->all() as $sortByItem) {
                $queryBuilder->addOrderBy(
                    self::getRootAlias($queryBuilder) . '.' . $sortByItem->getField()->value(),
                    DoctrineSortTransformer::fromSortDirection($sortByItem->getDirection())
                );
            }
        }
    }

    private static function filterByCommonCriteriaId(CriteriaWithId $criteria, QueryBuilder $queryBuilder): void
    {
        if ($criteria->getId()) {
            $queryBuilder->andWhere(self::getRootAlias($queryBuilder) . '.id = :id');
            $queryBuilder->setParameter('id', $criteria->getId()->value());
        }

        if ($criteria->getIds() && !$criteria->getIds()->isEmpty()) {
            $queryBuilder->andWhere(self::getRootAlias($queryBuilder) . '.id in (:ids)');
            $queryBuilder->setParameter('ids', $criteria->getIds()->all(), ArrayParameterType::STRING);
        }
    }

    private static function getRootAlias(QueryBuilder $qb): ?string
    {
        $fromParts = $qb->getDQLPart('from');

        if (!empty($fromParts)) {
            return $fromParts[0]->getAlias();
        }

        return null;
    }
}
