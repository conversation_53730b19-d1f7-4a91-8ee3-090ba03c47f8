<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentCollection;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use App\V2\Infrastructure\Purchase\Payment\PaymentStatusTransformer;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALPaymentRepository implements PaymentRepository
{
    public function __construct(
        private Connection $connection,
        private string $paymentTableName,
    ) {
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws DBALException
     */
    #[\Override]
    public function put(Payment $payment): void
    {
        try {
            $this->findOneBy(
                PaymentCriteria::createById($payment->getId())
            );

            $this->update($payment);
        } catch (PaymentNotFoundException) {
            $this->insert($payment);
        }
    }

    /**
     * @throws PaymentRepositoryException
     */
    private function insert(Payment $payment): void
    {
        try {
            $this->connection->insert(
                table: $this->paymentTableName,
                data: $this->fromPaymentToArray($payment),
            );
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentRepositoryException
     */
    private function update(Payment $payment): void
    {
        try {
            $data = $this->fromPaymentToArray($payment);
            unset($data['id']);

            $this->connection->update(
                table: $this->paymentTableName,
                data: $data,
                criteria: ['id' => $payment->getId()->value()],
            );
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentRepositoryException
     * @throws PaymentNotFoundException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findOneBy(PaymentCriteria $criteria): Payment
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new PaymentNotFoundException();
            }

            return $this->fromArrayToPayment($result);
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentRepositoryException
     * @throws CollectionException
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findBy(PaymentCriteria $criteria): PaymentCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new PaymentCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToPayment($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[\Override]
    public function countBy(PaymentCriteria $criteria): int
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->select('COUNT(*) as count')
                ->executeQuery()
                ->fetchAssociative();

            return (int) $result['count'];
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[\Override]
    public function delete(Payment $payment): void
    {
        try {
            $payment->markAsDeleted();

            $this->update($payment);
        } catch (DBALException $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    private function getQueryBuilderByCriteria(PaymentCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->paymentTableName, 't')
            ->andWhere('deleted_at IS NULL');

        if ($criteria->getPurchaseId()) {
            $qb->andWhere('purchase_id = :purchase_id')
                ->setParameter('purchase_id', $criteria->getPurchaseId()->value());
        }

        if ($criteria->getUserId()) {
            $qb->andWhere('user_id = :user_id')
                ->setParameter('user_id', $criteria->getUserId()->value());
        }

        if ($criteria->getPaymentIntent()) {
            $qb->andWhere('payment_intent = :payment_intent')
                ->setParameter('payment_intent', $criteria->getPaymentIntent());
        }

        if ($criteria->getStatus()) {
            $qb->andWhere('status = :status')
                ->setParameter('status', PaymentStatusTransformer::toString($criteria->getStatus()));
        }

        if ($criteria->getMinAmount()) {
            $qb->andWhere('amount >= :min_amount')
                ->setParameter('min_amount', $criteria->getMinAmount()->value());
        }

        if ($criteria->getMaxAmount()) {
            $qb->andWhere('amount <= :max_amount')
                ->setParameter('max_amount', $criteria->getMaxAmount()->value());
        }

        if ($criteria->getStartDate()) {
            $qb->andWhere('created_at >= :start_date')
                ->setParameter('start_date', $criteria->getStartDate()->format('Y-m-d H:i:s'));
        }

        if ($criteria->getEndDate()) {
            $qb->andWhere('created_at <= :end_date')
                ->setParameter('end_date', $criteria->getEndDate()->format('Y-m-d H:i:s'));
        }

        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb, true);

        return $qb;
    }

    private function fromPaymentToArray(Payment $payment): array
    {
        return [
            'id' => $payment->getId()->value(),
            'purchase_id' => $payment->getPurchaseId()->value(),
            'user_id' => $payment->getUserId()->value(),
            'payment_intent' => $payment->getPaymentIntent(),
            'amount' => $payment->getAmount()->value(),
            'currency_code' => CurrencyCodeTransformer::toString($payment->getAmount()->currency()->code()),
            'status' => PaymentStatusTransformer::toString($payment->getStatus()),
            'created_at' => DBALDateTimeFormatter::format($payment->getCreatedAt()),
            'updated_at' => DBALDateTimeFormatter::format($payment->getUpdatedAt()),
            'deleted_at' => DBALDateTimeFormatter::format($payment->getDeletedAt()),
        ];
    }

    /**
     * @throws InvalidCurrencyCodeException
     * @throws InvalidUuidException
     */
    private function fromArrayToPayment(array $values): Payment
    {
        return new Payment(
            id: new Uuid($values['id']),
            purchaseId: new Uuid($values['purchase_id']),
            userId: new Id((int) $values['user_id']),
            paymentIntent: $values['payment_intent'],
            amount: Money::create(
                amount: (int) $values['amount'],
                currency: new Currency(CurrencyCodeTransformer::fromString($values['currency_code']))
            ),
            status: PaymentStatusTransformer::fromString($values['status']),
            createdAt: DBALDateTimeFormatter::parse($values['created_at']),
            updatedAt: DBALDateTimeFormatter::parse($values['updated_at']),
            deletedAt: DBALDateTimeFormatter::parse($values['deleted_at']),
        );
    }
}
