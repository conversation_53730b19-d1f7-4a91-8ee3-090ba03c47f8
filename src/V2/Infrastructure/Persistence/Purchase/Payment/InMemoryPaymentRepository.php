<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentCollection;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryPaymentRepository implements PaymentRepository
{
    private PaymentCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new PaymentCollection([]);
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[\Override]
    public function put(Payment $payment): void
    {
        try {
            $payments = $this->collection->allIndexedById();
            $payments[$payment->getId()->value()] = clone $payment;
            $this->collection->replace($payments);
        } catch (\Throwable $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws PaymentNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(PaymentCriteria $criteria): Payment
    {
        $payments = $this->filterByCriteria($criteria);

        if ($payments->isEmpty()) {
            throw new PaymentNotFoundException();
        }

        return $payments->first();
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[\Override]
    public function findBy(PaymentCriteria $criteria): PaymentCollection
    {
        try {
            return $this->filterByCriteria($criteria);
        } catch (\Throwable $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function countBy(PaymentCriteria $criteria): int
    {
        try {
            return $this->filterByCriteria($criteria)->count();
        } catch (\Throwable $e) {
            return 0;
        }
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[\Override]
    public function delete(Payment $payment): void
    {
        try {
            $payments = $this->collection->allIndexedById();
            unset($payments[$payment->getId()->value()]);
            $this->collection->replace($payments);
        } catch (\Throwable $e) {
            throw PaymentRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(PaymentCriteria $criteria): PaymentCollection
    {
        $collection = $this->collection->filter(
            fn (Payment $payment) => (
                null === $criteria->getPurchaseId()
                    || $payment->getPurchaseId()->equals($criteria->getPurchaseId())
            ) && (
                null === $criteria->getUserId()
                    || $payment->getUserId()->equals($criteria->getUserId())
            ) && (
                null === $criteria->getPaymentIntent()
                    || $payment->getPaymentIntent() === $criteria->getPaymentIntent()
            ) && (
                null === $criteria->getStatus()
                    || $payment->getStatus() === $criteria->getStatus()
            ) && (
                null === $criteria->getMinAmount()
                    || $payment->getAmount()->greaterThanOrEqual($criteria->getMinAmount())
            ) && (
                null === $criteria->getMaxAmount()
                    || $payment->getAmount()->lessThanOrEqual($criteria->getMaxAmount())
            ) && (
                null === $criteria->getStartDate()
                    || $payment->getCreatedAt()->getTimestamp() >= $criteria->getStartDate()->getTimestamp()
            ) && (
                null === $criteria->getEndDate()
                    || $payment->getCreatedAt()->getTimestamp() <= $criteria->getEndDate()->getTimestamp()
            )
        );

        /** @var PaymentCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (Payment $payment) => new Payment(
                id: $payment->getId(),
                purchaseId: $payment->getPurchaseId(),
                userId: $payment->getUserId(),
                paymentIntent: $payment->getPaymentIntent(),
                amount: $payment->getAmount(),
                status: $payment->getStatus(),
                createdAt: $payment->getCreatedAt(),
                updatedAt: $payment->getUpdatedAt(),
                deletedAt: $payment->getDeletedAt(),
            )
        );
    }
}
