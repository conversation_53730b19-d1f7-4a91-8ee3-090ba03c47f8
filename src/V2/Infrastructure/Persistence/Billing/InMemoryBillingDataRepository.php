<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Billing;

use App\V2\Domain\Billing\BillingData;
use App\V2\Domain\Billing\BillingDataCollection;
use App\V2\Domain\Billing\BillingDataCriteria;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryBillingDataRepository implements BillingDataRepository
{
    private BillingDataCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new BillingDataCollection([]);
    }

    /**
     * @throws CollectionException
     */
    #[\Override]
    public function put(BillingData $billingData): void
    {
        $billingDataItems = $this->collection->allIndexedById();
        $billingDataItems[$billingData->getId()->value()] = clone $billingData;
        $this->collection->replace($billingDataItems);
    }

    /**
     * @throws BillingDataNotFoundException
     * @throws CollectionException
     */
    #[\Override]
    public function findOneBy(BillingDataCriteria $criteria): ?BillingData
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new BillingDataNotFoundException();
        }

        return $result->first();
    }

    /**
     * @throws BillingDataRepositoryException
     */
    #[\Override]
    public function findBy(BillingDataCriteria $criteria): BillingDataCollection
    {
        try {
            return $this->filterByCriteria($criteria);
        } catch (\Throwable $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws BillingDataRepositoryException
     */
    #[\Override]
    public function delete(BillingData $billingData): void
    {
        try {
            $billingDataItems = $this->collection->allIndexedById();

            if (!isset($billingDataItems[$billingData->getId()->value()])) {
                throw new BillingDataNotFoundException();
            }

            unset($billingDataItems[$billingData->getId()->value()]);
            $this->collection->replace($billingDataItems);
        } catch (BillingDataNotFoundException $e) {
            throw $e;
        } catch (\Throwable $e) {
            throw BillingDataRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(BillingDataCriteria $criteria): BillingDataCollection
    {
        /** @var BillingDataCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $this->collection);

        // Filter by userId
        if (null !== $criteria->getUserId()) {
            $collection = $collection->filter(
                fn (BillingData $billingData): bool => $billingData->getUserId()->equals($criteria->getUserId())
            );
        }

        // Filter by tin
        if (null !== $criteria->getTin()) {
            $collection = $collection->filter(
                fn (BillingData $billingData): bool => $billingData->getTin() === $criteria->getTin()
            );
        }

        return $collection->map(
            fn (BillingData $billingData) => new BillingData(
                id: $billingData->getId(),
                userId: $billingData->getUserId(),
                tin: $billingData->getTin(),
                firstName: $billingData->getFirstName(),
                lastName: $billingData->getLastName(),
                address: $billingData->getAddress(),
                postalCode: $billingData->getPostalCode(),
                city: $billingData->getCity(),
                country: $billingData->getCountry(),
                metadata: $billingData->getMetadata(),
            )
        );
    }
}
