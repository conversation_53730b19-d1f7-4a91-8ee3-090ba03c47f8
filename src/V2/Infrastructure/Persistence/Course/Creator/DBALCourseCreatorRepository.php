<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course\Creator;

use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\CourseCreatorRepositoryException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityManagerInterface;

class DBALCourseCreatorRepository implements CourseCreatorRepository
{
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $em,
        private readonly string $courseCreatorTableName,
    ) {
        $this->connection = $em->getConnection();
    }

    #[\Override]
    public function insert(CourseCreator $courseCreator): void
    {
        try {
            $this->findOneBy(
                CourseCreatorCriteria::createEmpty()
                    ->filterByUserId($courseCreator->getUserId())
                    ->filterByCourseId($courseCreator->getCourseId())
            );

            throw CourseCreatorRepositoryException::duplicateCourseCreator($courseCreator);
        } catch (CourseCreatorNotFoundException) {
        }

        try {
            $this->connection
                ->insert(
                    table: $this->courseCreatorTableName,
                    data: $this->fromCourseCreatorToArray($courseCreator),
                );
        } catch (DBALException $e) {
            throw CourseCreatorRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(CourseCreatorCriteria $criteria): CourseCreator
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new CourseCreatorNotFoundException();
            }

            return $this->fromArrayToCourseCreator($result);
        } catch (DBALException $e) {
            throw CourseCreatorRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(CourseCreatorCriteria $criteria): CourseCreatorCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new CourseCreatorCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToCourseCreator($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw CourseCreatorRepositoryException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(CourseCreator $courseCreator): void
    {
        try {
            $this->connection
                ->delete(
                    table: $this->courseCreatorTableName,
                    criteria: [
                        'user_id' => $courseCreator->getUserId()->value(),
                        'course_id' => $courseCreator->getCourseId()->value(),
                    ]
                );
        } catch (DBALException $e) {
            throw CourseCreatorRepositoryException::fromPrevious($e);
        }
    }

    private function fromCourseCreatorToArray(CourseCreator $courseCreator): array
    {
        return [
            'user_id' => $courseCreator->getUserId()->value(),
            'course_id' => $courseCreator->getCourseId()->value(),
        ];
    }

    private function fromArrayToCourseCreator(array $values): CourseCreator
    {
        return new CourseCreator(
            userId: new Id((int) $values['user_id']),
            courseId: new Id((int) $values['course_id']),
        );
    }

    private function getQueryBuilderByCriteria(CourseCreatorCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->courseCreatorTableName, 't');

        if (null !== $criteria->getUserId()) {
            $queryBuilder->andWhere('t.user_id = :userId')
                ->setParameter('userId', $criteria->getUserId()->value());
        }

        if (null !== $criteria->getCourseId()) {
            $queryBuilder->andWhere('t.course_id = :courseId')
                ->setParameter('courseId', $criteria->getCourseId()->value());
        }

        return $queryBuilder;
    }
}
