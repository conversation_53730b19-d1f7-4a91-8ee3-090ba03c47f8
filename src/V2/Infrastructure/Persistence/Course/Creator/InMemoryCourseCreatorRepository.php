<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course\Creator;

use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\CourseCreatorRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryCourseCreatorRepository implements CourseCreatorRepository
{
    private CourseCreatorCollection $courseCreators;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->courseCreators = new CourseCreatorCollection([]);
    }

    #[\Override]
    public function insert(CourseCreator $courseCreator): void
    {
        try {
            $this->findOneBy(
                CourseCreatorCriteria::createEmpty()
                    ->filterByUserId($courseCreator->getUserId())
                    ->filterByCourseId($courseCreator->getCourseId()),
            );

            throw CourseCreatorRepositoryException::duplicateCourseCreator($courseCreator);
        } catch (CourseCreatorNotFoundException) {
            $this->courseCreators->append(clone $courseCreator);
        }
    }

    #[\Override]
    public function findOneBy(CourseCreatorCriteria $criteria): CourseCreator
    {
        $creators = $this->filterByCriteria($criteria);

        if ($creators->isEmpty()) {
            throw new CourseCreatorNotFoundException();
        }

        return $creators->first();
    }

    #[\Override]
    public function findBy(CourseCreatorCriteria $criteria): CourseCreatorCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(CourseCreator $courseCreator): void
    {
        $this->courseCreators = $this->courseCreators->filter(
            fn (CourseCreator $creator) => $creator->getUserId() !== $courseCreator->getUserId()
                || $creator->getCourseId() !== $courseCreator->getCourseId()
        );
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(CourseCreatorCriteria $criteria): CourseCreatorCollection
    {
        $creators = $this->courseCreators->filter(
            fn (CourseCreator $courseCreator) => (
                null === $criteria->getCourseId()
                || $courseCreator->getCourseId()->equals($criteria->getCourseId())
            ) && (
                null === $criteria->getUserId()
                || $courseCreator->getUserId()->equals($criteria->getUserId())
            )
        );

        return $creators->map(
            fn (CourseCreator $courseCreator) => new CourseCreator(
                userId: $courseCreator->getUserId(),
                courseId: $courseCreator->getCourseId(),
            )
        );
    }
}
