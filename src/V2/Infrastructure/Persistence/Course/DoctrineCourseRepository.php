<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course;

use App\Entity\Course;
use App\Repository\CourseRepository as LegacyCourseRepository;
use App\V2\Domain\Course\CourseCollection;
use App\V2\Domain\Course\CourseCriteria;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Infrastructure\Persistence\DoctrineCommonCriteriaBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;

readonly class DoctrineCourseRepository implements CourseRepository
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    private function getRepository(): EntityRepository|LegacyCourseRepository
    {
        return $this->entityManager->getRepository(Course::class);
    }

    /**
     * @throws InfrastructureException
     */
    public function put(Course $course): void
    {
        try {
            $this->entityManager->persist($course);
            $this->entityManager->flush();
        } catch (\Exception $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    /**
     * @throws InfrastructureException
     * @throws CourseNotFoundException
     */
    public function findOneBy(CourseCriteria $criteria): Course
    {
        try {
            return $this->createQueryBuilder($criteria)
                ->getQuery()
                ->getSingleResult();
        } catch (NoResultException) {
            throw new CourseNotFoundException();
        } catch (NonUniqueResultException $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    /**
     * @throws CollectionException
     */
    public function findBy(CourseCriteria $criteria): CourseCollection
    {
        $courses = $this
            ->createQueryBuilder($criteria)
            ->getQuery()
            ->getResult();

        return new CourseCollection($courses);
    }

    /**
     * @throws InfrastructureException
     */
    public function countBy(CourseCriteria $criteria): int
    {
        $queryBuilder = $this->createQueryBuilder(
            criteria: $criteria,
            onlyFilter: true
        );

        try {
            return (int) $queryBuilder
                ->select('COUNT(u.id)')
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    private function createQueryBuilder(CourseCriteria $criteria, bool $onlyFilter = false): QueryBuilder
    {
        $queryBuilder = $this->getRepository()->createQueryBuilder('c');

        if (null !== $criteria->getCreatedById()) {
            $queryBuilder
                ->andWhere('c.createdBy = :createdBy')
                ->setParameter('createdBy', $criteria->getCreatedById()->value());
        }

        DoctrineCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
            onlyFilter: $onlyFilter
        );

        return $queryBuilder;
    }
}
