<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Listener;

use App\V2\Application\Event\LtiRegistrationCreatedEvent;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Url\InvalidUrlException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

readonly class LtiRegistrationCreatedEventListener implements EventSubscriberInterface
{
    public function __construct(
        private LtiPlatformRepository $ltiPlatformRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            LtiRegistrationCreatedEvent::class => 'onRegistrationCreated',
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUrlException
     */
    public function onRegistrationCreated(LtiRegistrationCreatedEvent $event): void
    {
        $this->createPlatform($event);
    }

    /**
     * @throws InvalidUrlException
     * @throws InfrastructureException
     */
    private function createPlatform(LtiRegistrationCreatedEvent $event): void
    {
        $registration = $event->getRegistration();
        $schemeAndHttpHost = $event->getSchemeAndHttpHost();

        $platform = new LtiPlatform(
            id: $this->uuidGenerator->generate(),
            registrationId: $registration->getId(),
            name: 'EasyLearning',
            audience: $schemeAndHttpHost->value(),
            oidcAuthenticationUrl: new Url(\sprintf('%s/lti1p3/oidc/authentication', $event->getSchemeAndHttpHost())),
            oauth2AccessTokenUrl: new Url(
                \sprintf('%s/lti1p3/auth/EasyLearning-Sig/token', $event->getSchemeAndHttpHost())
            ),
            jwksUrl: new Url(\sprintf('%s/lti1p3/.well-known/jwks/EasyLearning.json', $event->getSchemeAndHttpHost())),
        );

        $this->ltiPlatformRepository->put($platform);
    }
}
