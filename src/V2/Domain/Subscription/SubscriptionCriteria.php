<?php

declare(strict_types=1);

namespace App\V2\Domain\Subscription;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;

class SubscriptionCriteria extends CriteriaWithUuid
{
    private ?string $name = null;
    private ?string $search = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->name
            && null === $this->search;
    }

    public function filterByName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function filterBySearch(string $search): self
    {
        $this->search = $search;

        return $this;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }
}
