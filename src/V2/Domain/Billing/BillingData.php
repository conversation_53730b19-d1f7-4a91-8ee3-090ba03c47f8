<?php

declare(strict_types=1);

namespace App\V2\Domain\Billing;

use App\V2\Domain\Shared\Entity\EntityWithId;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends EntityWithId<Uuid>
 */
class BillingData extends EntityWithId
{
    public function __construct(
        Uuid $id,
        public readonly Id $userId,
        public readonly string $tin,
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly string $address,
        public readonly string $postalCode,
        public readonly string $city,
        public readonly string $country,
        public readonly array $metadata,
    ) {
        parent::__construct($id);
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getTin(): string
    {
        return $this->tin;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }
}
