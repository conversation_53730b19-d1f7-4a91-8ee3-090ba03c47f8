<?php

declare(strict_types=1);

namespace App\V2\Domain\Announcement\Manager;

use App\V2\Domain\Shared\Entity\Entity;
use App\V2\Domain\Shared\Id\Id;

class AnnouncementManager implements Entity
{
    private ?Manager $manager = null;

    public function __construct(
        private readonly Id $userId,
        private readonly Id $announcementId,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getAnnouncementId(): Id
    {
        return $this->announcementId;
    }

    public function getManager(): ?Manager
    {
        return $this->manager;
    }

    public function setManager(Manager $manager): void
    {
        $this->manager = $manager;
    }

    public function __toString(): string
    {
        return "{$this->userId}:{$this->announcementId}";
    }
}
