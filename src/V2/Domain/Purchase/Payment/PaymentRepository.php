<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentCollection;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;

interface PaymentRepository
{
    /**
     * @throws PaymentRepositoryException
     */
    public function put(Payment $payment): void;

    /**
     * @throws PaymentNotFoundException
     */
    public function findOneBy(PaymentCriteria $criteria): Payment;

    /**
     * @throws PaymentRepositoryException
     */
    public function findBy(PaymentCriteria $criteria): PaymentCollection;

    public function countBy(PaymentCriteria $criteria): int;

    /**
     * @throws PaymentRepositoryException
     */
    public function delete(Payment $payment): void;
}
