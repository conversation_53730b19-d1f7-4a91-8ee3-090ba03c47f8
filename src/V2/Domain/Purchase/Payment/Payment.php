<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Payment;

use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Entity\LifeCycleEntity;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

/**
 * @extends LifeCycleEntity<Uuid>
 */
class Payment extends LifeCycleEntity
{
    public function __construct(
        Uuid $id,
        private readonly Uuid $purchaseId,
        private readonly Id $userId,
        private readonly string $paymentIntent,
        private readonly Money $amount,
        private readonly PaymentStatus $status,
        \DateTimeImmutable $createdAt = new \DateTimeImmutable(),
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ) {
        parent::__construct(
            id: $id,
            createdAt: $createdAt,
            updatedAt: $updatedAt,
            deletedAt: $deletedAt,
        );
    }

    public function getPurchaseId(): Uuid
    {
        return $this->purchaseId;
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getPaymentIntent(): string
    {
        return $this->paymentIntent;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function getStatus(): PaymentStatus
    {
        return $this->status;
    }
}
