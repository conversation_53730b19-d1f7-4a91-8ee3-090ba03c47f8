<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Payment;

use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

class PaymentCriteria extends CriteriaWithUuid
{
    private ?Uuid $purchaseId = null;
    private ?Id $userId = null;
    private ?string $paymentIntent = null;
    private ?PaymentStatus $status = null;
    private ?Money $minAmount = null;
    private ?Money $maxAmount = null;
    private ?\DateTimeImmutable $startDate = null;
    private ?\DateTimeImmutable $endDate = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->purchaseId
            && null === $this->userId
            && null === $this->paymentIntent
            && null === $this->status
            && null === $this->minAmount
            && null === $this->maxAmount
            && null === $this->startDate
            && null === $this->endDate;
    }

    public function filterByPurchaseId(Uuid $purchaseId): self
    {
        $this->purchaseId = $purchaseId;

        return $this;
    }

    public function getPurchaseId(): ?Uuid
    {
        return $this->purchaseId;
    }

    public function filterByUserId(Id $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function filterByPaymentIntent(string $paymentIntent): self
    {
        $this->paymentIntent = $paymentIntent;

        return $this;
    }

    public function getPaymentIntent(): ?string
    {
        return $this->paymentIntent;
    }

    public function filterByStatus(PaymentStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStatus(): ?PaymentStatus
    {
        return $this->status;
    }

    public function filterByMinAmount(Money $minAmount): self
    {
        $this->minAmount = $minAmount;

        return $this;
    }

    public function getMinAmount(): ?Money
    {
        return $this->minAmount;
    }

    public function filterByMaxAmount(Money $maxAmount): self
    {
        $this->maxAmount = $maxAmount;

        return $this;
    }

    public function getMaxAmount(): ?Money
    {
        return $this->maxAmount;
    }

    public function filterByStartDate(\DateTimeImmutable $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getStartDate(): ?\DateTimeImmutable
    {
        return $this->startDate;
    }

    public function filterByEndDate(\DateTimeImmutable $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeImmutable
    {
        return $this->endDate;
    }
}
