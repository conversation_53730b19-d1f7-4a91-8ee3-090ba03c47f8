<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class CreatePurchasePaymentCommand implements Command
{
    public function __construct(
        private Uuid $purchaseId,
        private Id $userId,
    ) {
    }

    public function getPurchaseId(): Uuid
    {
        return $this->purchaseId;
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }
}
