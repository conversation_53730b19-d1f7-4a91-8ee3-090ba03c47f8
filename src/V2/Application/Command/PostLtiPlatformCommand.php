<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class PostLtiPlatformCommand implements Command
{
    public function __construct(
        private Uuid $registrationId,
        private string $name,
        private string $audience,
        private Url $oidcAuthenticationUrl,
        private Url $oauth2AccessTokenUrl,
        private Url $jwksUrl,
    ) {
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAudience(): string
    {
        return $this->audience;
    }

    public function getOidcAuthenticationUrl(): Url
    {
        return $this->oidcAuthenticationUrl;
    }

    public function getOauth2AccessTokenUrl(): Url
    {
        return $this->oauth2AccessTokenUrl;
    }

    public function getJwksUrl(): Url
    {
        return $this->jwksUrl;
    }
}
