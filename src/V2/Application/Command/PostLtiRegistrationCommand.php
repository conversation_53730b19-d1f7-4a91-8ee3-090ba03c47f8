<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Url\Url;

readonly class PostLtiRegistrationCommand implements Command
{
    public function __construct(
        private string $name,
        private string $clientId,
        private Url $schemeAndHttpHost,
    ) {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getSchemeAndHttpHost(): Url
    {
        return $this->schemeAndHttpHost;
    }
}
