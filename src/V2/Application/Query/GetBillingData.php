<?php

declare(strict_types=1);

namespace App\V2\Application\Query;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\Shared\Id\Id;

readonly class GetBillingData implements Query
{
    public function __construct(
        private Id $userId,
        private User $requestedBy,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getRequestedBy(): User
    {
        return $this->requestedBy;
    }
}
