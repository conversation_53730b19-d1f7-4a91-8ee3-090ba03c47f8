<?php

declare(strict_types=1);

namespace App\V2\Application\DTO\User;

readonly class UserListItemDTO
{
    /**
     * @param array<string> $roles
     */
    public function __construct(
        private int $id,
        private ?string $avatar,
        private string $email,
        private string $firstName,
        private string $lastName,
        private array $roles,
        private bool $isActive,
        private ?int $points,
        private bool $editable = false,
        private bool $deletable = false,
        private bool $allowLoginAs = false
    ) {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    /**
     * @return array<string>
     */
    public function getRoles(): array
    {
        return $this->roles;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function getPoints(): ?int
    {
        return $this->points;
    }

    public function isEditable(): bool
    {
        return $this->editable;
    }

    public function isDeletable(): bool
    {
        return $this->deletable;
    }

    public function isAllowLoginAs(): bool
    {
        return $this->allowLoginAs;
    }
}
