<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\CreatePurchasePaymentCommand;
use App\V2\Domain\Purchase\Exception\PurchaseNotAllowedException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\Exception\PurchaseStatusException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Criteria\CriteriaException;

readonly class CreatePurchasePaymentCommandHandler
{
    public function __construct(
        private PurchaseRepository $purchaseRepository,
        private PaymentManagerInterface $paymentManager,
    ) {
    }

    /**
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws PurchaseNotAllowedException
     * @throws PurchaseStatusException
     * @throws CriteriaException
     */
    public function handle(CreatePurchasePaymentCommand $command): Payment
    {
        $purchase = $this->purchaseRepository->findOneBy(
            PurchaseCriteria::createById($command->getPurchaseId())
        );

        $this->validatePurchaseOwnership($purchase, $command);
        $this->validatePurchaseStatus($purchase);

        return $this->paymentManager->createPayment($purchase);
    }

    /**
     * @throws PurchaseNotAllowedException
     */
    private function validatePurchaseOwnership(Purchase $purchase, CreatePurchasePaymentCommand $command): void
    {
        if (!$purchase->getUserId()->equals($command->getUserId())) {
            throw new PurchaseNotAllowedException();
        }
    }

    /**
     * @throws PurchaseStatusException
     */
    private function validatePurchaseStatus(Purchase $purchase): void
    {
        if (PurchaseStatus::Pending !== $purchase->getStatus()) {
            throw new PurchaseStatusException();
        }
    }
}
