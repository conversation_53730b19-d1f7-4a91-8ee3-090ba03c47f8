<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\Entity\Course;
use App\Entity\User;
use App\V2\Application\Command\DeleteCourseCreatorCommand;
use App\V2\Domain\Course\CourseCriteria;
use App\V2\Domain\Course\CourseRepository;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\Exceptions\CreatorNotAuthorizedException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class DeleteCourseCreatorCommandHandler
{
    public function __construct(
        private UserRepository $userRepository,
        private CourseCreatorRepository $courseCreatorRepository,
        private CourseRepository $courseRepository,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws CourseNotFoundException
     * @throws CriteriaException
     * @throws CourseCreatorNotFoundException
     * @throws CreatorNotAuthorizedException
     * @throws UserNotFoundException
     */
    public function handle(DeleteCourseCreatorCommand $command): void
    {
        $user = $this->userRepository->findOneBy(
            UserCriteria::createById($command->getUserId())
        );

        $course = $this->courseRepository->findOneBy(
            CourseCriteria::createById($command->getCourseId())
        );

        $this->checkPermissions($command->getRequestUser(), $course);

        $courseCreator = $this->courseCreatorRepository->findOneBy(
            CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(new Id($course->getId()))
                ->filterByUserId(new Id($user->getId()))
        );

        $this->courseCreatorRepository->delete($courseCreator);
    }

    /**
     * @throws CreatorNotAuthorizedException
     */
    private function checkPermissions(UserInterface $requestUser, Course $course): void
    {
        if (
            !\in_array(User::ROLE_ADMIN, $requestUser->getRoles())
            && \in_array(User::ROLE_CREATOR, $requestUser->getRoles(), true)
            && ($course->getCreatedBy() && $course->getCreatedBy()->getId() !== $requestUser->getId())
        ) {
            throw CreatorNotAuthorizedException::userNotAuthorized($requestUser, $course);
        }
    }
}
