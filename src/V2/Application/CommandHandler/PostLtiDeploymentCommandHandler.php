<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\PostLtiDeploymentCommand;
use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\PostLtiDeploymentException;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class PostLtiDeploymentCommandHandler
{
    public function __construct(
        private LtiRegistrationRepository $ltiRegistrationRepository,
        private LtiDeploymentRepository $ltiDeploymentRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws LtiRegistrationNotFoundException
     * @throws InfrastructureException
     * @throws PostLtiDeploymentException
     * @throws CriteriaException
     */
    public function handle(PostLtiDeploymentCommand $command): void
    {
        $registration = $this->ltiRegistrationRepository->findOneBy(
            LtiRegistrationCriteria::createById($command->getRegistrationId()),
        );

        $deployment = new LtiDeployment(
            id: $this->uuidGenerator->generate(),
            registrationId: $registration->getId(),
            name: $command->getName(),
            deploymentId: $command->getDeploymentId(),
        );

        try {
            $this->ltiDeploymentRepository->put($deployment);
        } catch (LtiException $e) {
            throw PostLtiDeploymentException::fromPrevious($e);
        }
    }
}
