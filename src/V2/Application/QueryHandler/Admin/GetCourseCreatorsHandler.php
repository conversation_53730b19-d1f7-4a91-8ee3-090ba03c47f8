<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler\Admin;

use App\Repository\CourseRepository as LegacyCourseRepository;
use App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection;
use App\V2\Application\Query\Admin\GetCourseCreators;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorHydrationCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\Exceptions\GetCourseCreatorsException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;

readonly class GetCourseCreatorsHandler
{
    public function __construct(
        private CourseCreatorRepository $courseCreatorRepository,
        private LegacyCourseRepository $legacyCourseRepository,
        private CourseCreatorHydratorCollection $hydratorCollection,
    ) {
    }

    /**
     * @throws InfrastructureException
     * @throws GetCourseCreatorsException
     * @throws CourseNotFoundException
     */
    public function handle(GetCourseCreators $query): CourseCreatorCollection
    {
        $criteria = $query->getCriteria();

        if (null === $criteria->getCourseId()) {
            throw new CourseNotFoundException();
        }

        $course = $this->legacyCourseRepository->findOneBy(['id' => $criteria->getCourseId()->value()]);
        if (null === $course) {
            throw new CourseNotFoundException();
        }

        $courseCreatorCollection = $this->courseCreatorRepository
            ->findBy($criteria);

        $hydrationCriteria = CourseCreatorHydrationCriteria::createEmpty();

        if ($query->needsCreators()) {
            $hydrationCriteria->withUser();
        }

        if (!$hydrationCriteria->isEmpty()) {
            try {
                $this->hydratorCollection->hydrate(
                    collection: $courseCreatorCollection,
                    criteria: $hydrationCriteria,
                );
            } catch (HydratorException $e) {
                throw GetCourseCreatorsException::fromPrevious($e);
            }
        }

        return $courseCreatorCollection;
    }
}
