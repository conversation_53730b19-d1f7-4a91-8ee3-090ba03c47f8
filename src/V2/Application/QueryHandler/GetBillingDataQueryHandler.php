<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Query\GetBillingData;
use App\V2\Domain\Billing\BillingData;
use App\V2\Domain\Billing\BillingDataCriteria;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Billing\Exception\GetBillingDataQueryHandlerException;

readonly class GetBillingDataQueryHandler
{
    public function __construct(
        private BillingDataRepository $billingDataRepository
    ) {
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     */
    public function handle(GetBillingData $query): ?BillingData
    {
        if (
            !$query->getRequestedBy()->isAdmin()
            && $query->getRequestedBy()->getId() !== $query->getUserId()->value()
        ) {
            throw GetBillingDataQueryHandlerException::userNotAllowed();
        }

        $criteria = BillingDataCriteria::createEmpty()
            ->filterByUserId($query->getUserId());

        return $this->billingDataRepository->findOneBy($criteria);
    }
}
