<?php

namespace App\Entity;


use App\Repository\CategorizeOptionsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use DateTime;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Behavior\Imageable;
use Symfony\Component\Serializer\Annotation\Groups;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=CategorizeOptionsRepository::class)
 * @Vich\Uploadable()
 */
class CategorizeOptions
{
	use Blamable;
	use Timestampable;
	use Imageable;

	/**
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 * @ORM\Column(type="integer")
	 * @Groups({"categorizeOptions"})
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="categorizeOptions")
	 */
	private $chapter;

	/**
	 * @Vich\UploadableField(mapping="categorize_options", fileNameProperty="image")
	 */
	private $imageFile;

	/**
	 * @ORM\Column(type="string", length=255, nullable=true)
	 * @Groups({"categorizeOptions"})
	 */
	private $name;

	/**
	 * @ORM\OneToMany(targetEntity=CategorizeAnswers::class, mappedBy="options", cascade={"persist", "remove"})
	 */
	private $categorizeAnswers;


	public function __construct()
	{
		$this->createdAt = new DateTime();
		$this->updatedAt = new DateTime();
		$this->categorizeAnswers = new ArrayCollection();
	}

	public function __toString()
	{
		return $this->name;
	}


    public function __clone ()
    {
        $this->id = null;

        // clone image
        if ($this->getUploadsFolder()) {
            $this->cloneImage();
        }

//        $answers = $this->categorizeAnswers;
//        $this->categorizeAnswers = new ArrayCollection();
//        foreach ($answers as $answer) {
//            /**
//             * @var CategorizeAnswers $newAnswer
//             */
//            $newAnswer = clone $answer;
//            $newAnswer
//                ->setOptions($this)
//                ->setCategorize($this->getCategorize());
//
//            $this->addCategorizeAnswer($newAnswer);
//        }


    }


	public function getId(): ?int
	{
		return $this->id;
	}

	public function getChapter(): ?Chapter
	{
		return $this->chapter;
	}

	public function setChapter(?Chapter $chapter): self
	{
		$this->chapter = $chapter;

		return $this;
	}

	public function getName(): ?string
	{
		return $this->name  != null ? $this->name : 'category';
	}

	public function setName(?string $name): self
	{
		$this->name = $name ?? 'category';

		return $this;
	}

	/**
	 * @return Collection<int, CategorizeAnswers>
	 */
	public function getCategorizeAnswers(): Collection
	{
		return $this->categorizeAnswers;
	}

	public function addCategorizeAnswer(CategorizeAnswers $categorizeAnswer): self
	{
		if (!$this->categorizeAnswers->contains($categorizeAnswer)) {
			$this->categorizeAnswers[] = $categorizeAnswer;
			$categorizeAnswer->setOptions($this);
		}

		return $this;
	}

	public function removeCategorizeAnswer(CategorizeAnswers $categorizeAnswer): self
	{
		if ($this->categorizeAnswers->removeElement($categorizeAnswer)) {
			// set the owning side to null (unless already changed)
			if ($categorizeAnswer->getOptions() === $this) {
				$categorizeAnswer->setOptions(null);
			}
		}

		return $this;
	}

	/**
	 * @Groups({"categorizeOptions"})
	 * @return bool
	 */
	public function isEresable()
	{
		/* 	if ($this->categorizeAnswers->isEmpty()) {
			return true;
		} else {
			foreach ($this->categorizeAnswers as $answer) {
				if (!$answer->isCorrect()) {
					return true;
				}
			}
		} */

		return true;
	}
}
