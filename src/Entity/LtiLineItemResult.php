<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\LtiLineItemResultRepository;
use Doctrine\ORM\Mapping as ORM;
use OAT\Library\Lti1p3Ags\Model\Result\Result;
use OAT\Library\Lti1p3Ags\Model\Result\ResultInterface;

/**
 * @ORM\Entity(repositoryClass=LtiLineItemResultRepository::class)
 */
class LtiLineItemResult
{
    /**
     * Local id.
     *
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * URL uniquely identifying the result record. [id].
     *
     * @ORM\Column(type="text")
     */
    private $identifier;

    /**
     * URL identifying the Line Item to which this result belongs. Must be the same as the line item id and the value of the lineitem claim when included in the LTI message.
     *
     * @ORM\Column(type="text", nullable=false)
     */
    private $scoreOf;

    /**
     * The userId contains the LTI user ID identifying the recipient of the Result (usually a learner). The userId MUST be present.
     *
     * @ORM\Column(type="string", length=40)
     */
    private $userId;

    /**
     * The current score for this user. The value must be a numeric value. If no value exists, this attribute may be omitted, or have an explicit null value.
     *
     * @ORM\Column(type="float")
     */
    private $resultScore;

    /**
     * The 'resultMaximum' value MUST be a positive number (with 0 considered a negative number); if no value is specified, then a default maximum value of 1 must be used.
     *
     * @ORM\Column(type="float")
     */
    private $resultMaximum = 1.0;

    /**
     * The scoringUserId contains the LTI user ID identifying the provider of the Result (usually an instructor). If no value exists, this attribute may be omitted (if the Platform does not support this property, or if the item's result was provided by machine-scoring, for example).
     *
     * @ORM\Column(type="string", length=40, nullable=true)
     */
    private $scoringUserId;

    /**
     * The current value for the comment. The value must be a string. If no value exists, this attribute may be omitted, blank or have an explicit null value.
     *
     * @ORM\Column(type="text", nullable=true)
     */
    private $comment;

    /**
     * @ORM\Column(type="json")
     */
    private array $additionalProperties = [];

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getIdentifier(): ?string
    {
        return $this->identifier;
    }

    public function setIdentifier(string $identifier): self
    {
        $this->identifier = $identifier;

        return $this;
    }

    public function getScoreOf(): string
    {
        return $this->scoreOf;
    }

    public function setScoreOf(string $scoreOf): self
    {
        $this->scoreOf = $scoreOf;

        return $this;
    }

    public function getUserId(): string
    {
        return $this->userId;
    }

    public function setUserId(string $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getResultScore(): ?float
    {
        return $this->resultScore;
    }

    public function setResultScore(float $resultScore): self
    {
        $this->resultScore = $resultScore;

        return $this;
    }

    public function getResultMaximum(): ?float
    {
        return $this->resultMaximum;
    }

    public function setResultMaximum(float $resultMaximum): self
    {
        $this->resultMaximum = $resultMaximum;

        return $this;
    }

    public function getScoringUserId(): ?string
    {
        return $this->scoringUserId;
    }

    public function setScoringUserId(?string $scoringUserId): self
    {
        $this->scoringUserId = $scoringUserId;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getAdditionalProperties(): array
    {
        return $this->additionalProperties;
    }

    public function setAdditionalProperties(array $additionalProperties): self
    {
        $this->additionalProperties = $additionalProperties;

        return $this;
    }

    public function getResult(): ResultInterface
    {
        return new Result(
            $this->getUserId(),
            $this->getScoreOf(),
            $this->getIdentifier(),
            $this->getResultScore(),
            $this->getResultMaximum(),
            $this->getComment(),
            $this->getAdditionalProperties()
        );
    }
}
