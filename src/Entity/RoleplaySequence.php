<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\RoleplaySequenceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=RoleplaySequenceRepository::class)
 *
 * @ORM\Table(name="roleplay_sequence")
 */
class RoleplaySequence
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"project:detail", "project:visor", "sequence:create"})
     */
    private ?int $id = null;

    /**
     * @ORM\ManyToOne(targetEntity=RoleplayProject::class, inversedBy="sequences", cascade={"persist"} )
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private ?RoleplayProject $project = null;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"project:detail", "project:visor", "sequence:create"})
     */
    private string $color = '#00ffdd';

    /**
     * @ORM\Column(name="`order`", type="integer")
     *
     * @Groups({"project:detail", "project:visor", "sequence:create"})
     */
    private ?int $order = null;

    /**
     * @ORM\OneToMany(targetEntity=RoleplayScene::class, mappedBy="sequence", cascade={"persist"} )
     *
     * @Groups({"project:detail", "project:visor", "sequence:create"})
     */
    private $scenes;

    public function __construct()
    {
        $this->scenes = new ArrayCollection();
    }

    public function __clone()
    {
        $this->id = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?RoleplayProject
    {
        return $this->project;
    }

    public function setProject(?RoleplayProject $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;

        return $this;
    }

    /**
     * @return Collection<int, RoleplayScene>
     */
    public function getScenes(): Collection
    {
        return $this->scenes;
    }

    public function addScene(RoleplayScene $scene): self
    {
        if (!$this->scenes->contains($scene)) {
            $this->scenes[] = $scene;
            $scene->setSequence($this);
        }

        return $this;
    }

    public function removeScene(RoleplayScene $scene): self
    {
        if ($this->scenes->removeElement($scene)) {
            // set the owning side to null (unless already changed)
            if ($scene->getSequence() === $this) {
                $scene->setSequence(null);
            }
        }

        return $this;
    }

    public function isValid(): bool
    {
        if (0 === $this->getScenes()->count()) {
            return false;
        }

        foreach ($this->getScenes() as $scene) {
            if (!$scene->isValid()) {
                return false;
            }
        }

        return true;
    }

    public function initializeScenes()
    {
        $this->scenes = new ArrayCollection();
    }
}
