<?php

namespace App\Entity;

use App\Repository\NotificationRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\SerializedName;

/**
 * @ORM\Entity(repositoryClass=NotificationRepository::class)
 */
class Notification
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list", "detail"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="notifications")
     * @ORM\JoinColumn(nullable=false)
     *
     */
    private $user;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $message;

    /**
     * @ORM\Column(type="datetime")
     * @Groups({"list", "detail"})
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Groups({"list", "detail"})
     */
    private $readAt;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $data = [];


    /**
     * @Groups({"list", "detail"})
     * @SerializedName("message")
     */
    private $translatedMessage;


    public function getId (): ?int
    {
        return $this->id;
    }


    public function getUser (): ?User
    {
        return $this->user;
    }


    public function setUser (?User $user): self
    {
        $this->user = $user;

        return $this;
    }


    public function getMessage (): ?string
    {
        return $this->message;
    }


    public function setMessage (?string $message): self
    {
        $this->message = $message;

        return $this;
    }


    public function getCreatedAt (): ?\DateTimeInterface
    {
        return $this->createdAt;
    }


    public function setCreatedAt (\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }


    public function getReadAt (): ?\DateTimeInterface
    {
        return $this->readAt;
    }


    public function setReadAt (?\DateTimeInterface $readAt): self
    {
        $this->readAt = $readAt;

        return $this;
    }


    public function isRead ()
    {
        return $this->getReadAt() !== null;
    }


    public function markAsRead ()
    {
        $this->setReadAt(new \DateTime());
    }


    public function getData (): ?array
    {
        return $this->data;
    }


    public function setData (?array $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getTranslatedMessage ()
    {
        return $this->translatedMessage;
    }


    public function setTranslatedMessage ($translatedMessage): self
    {
        $this->translatedMessage = $translatedMessage;
        return $this;
    }
}
