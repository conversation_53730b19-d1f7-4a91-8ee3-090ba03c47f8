<?php

namespace App\Entity;

use App\Behavior\Timezone;
use App\Repository\UserHistoryDownloadDiplomaRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=UserHistoryDownloadDiplomaRepository::class)
 */
class UserHistoryDownloadDiploma
{
    use Blamable, Timestampable, Timezone;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="userHistoryDownloadDiplomas")
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="userHistoryDownloadDiplomas")
     */
    private $announcement;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getAnnouncement(): ?announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }
}
