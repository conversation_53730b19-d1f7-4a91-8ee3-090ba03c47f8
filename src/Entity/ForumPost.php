<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Entity\EmailNotification;
use Gedmo\Mapping\Annotation as Gedmo;
use App\Repository\ForumPostRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\MaxDepth;
use Symfony\Component\Serializer\Annotation\SerializedName;

/**
 * @ORM\Entity(repositoryClass=ForumPostRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class ForumPost
{
    use AtAndBy;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"forum", "thread","forumChanelList"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="forumPosts")
     * @ORM\JoinColumn(nullable=false)
     * @Groups({"forum", "thread"})
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity=ForumPost::class, inversedBy="children")
     */
    private $parent;

    /**
     * @ORM\OneToMany(targetEntity=ForumPost::class, mappedBy="parent")
     * @Groups({"forum", "thread"})
     */
    private $children;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="forumPosts")
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="forumPosts")
     */
    private $announcement;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"forum", "thread"})
     */
    private $title;

    /**
     * @ORM\Column(type="text")
     * @Groups({"forum", "thread"})
     */
    private $message;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Groups({"forum", "thread"})
     */
    private $lastResponseAt;

    /**
     * @ORM\ManyToOne(targetEntity=ForumPost::class, inversedBy="response")
     * @MaxDepth (2)
     * @Groups({"thread"})
     */
    private $response;

    /**
     * @ORM\OneToMany(targetEntity=ForumLikes::class, mappedBy="forumPost")
     * @Groups({"thread"})
     */
    private $forumLikes;

    /**
     * @ORM\OneToMany(targetEntity=ForumReport::class, mappedBy="forumPost")
     */
    private $forumReports;

    /**
     * @ORM\OneToMany(targetEntity=EmailNotification::class, mappedBy="forumPost")
     */
    private $emailNotificationForum;

    public function __construct ()
    {
        $this->children = new ArrayCollection();
     //   $this->response = new ArrayCollection();
     $this->forumLikes = new ArrayCollection();
     $this->forumReports = new ArrayCollection();
    }


    public function getId (): ?int
    {
        return $this->id;
    }


    public function getUser (): ?User
    {
        return $this->user;
    }


    public function setUser (?User $user): self
    {
        $this->user = $user;

        return $this;
    }


    public function getParent (): ?self
    {
        return $this->parent;
    }


    public function setParent (?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }


    /**
     * @return Collection|self[]
     */
    public function getChildren (): Collection
    {
        return $this->children;
    }


    public function addChild (self $child): self
    {
        if (!$this->children->contains($child))
        {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }


    public function removeChild (self $child): self
    {
        if ($this->children->contains($child))
        {
            $this->children->removeElement($child);
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this)
            {
                $child->setParent(null);
            }
        }

        return $this;
    }


    public function getCourse (): ?Course
    {
        return $this->course;
    }


    public function setCourse (?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getAnnouncement (): ?Announcement
    {
        return $this->announcement;
    }


    public function setAnnouncement (?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }


    public function getTitle (): ?string
    {
        return $this->title;
    }


    public function setTitle (?string $title): self
    {
        $this->title = $title;

        return $this;
    }


    public function getMessage (): ?string
    {
        return $this->message;
    }


    public function setMessage (string $message): self
    {
        $this->message = $message;

        return $this;
    }


//    public function getThreadIndex (): ?int
//    {
//        return $this->threadIndex;
//    }
//
//
//    public function setThreadIndex (int $threadIndex): self
//    {
//        $this->threadIndex = $threadIndex;
//
//        return $this;
//    }


    public function getLastResponseAt (): ?\DateTimeInterface
    {
        return $this->lastResponseAt;
    }


    public function setLastResponseAt (\DateTimeInterface $lastResponseAt): self
    {
        $this->lastResponseAt = $lastResponseAt;

        return $this;
    }


    /**
     * @Groups({"forum"})
     * @SerializedName ("createdAt")
     */
    public function getCreated ()
    {
        return $this->getCreatedAt();
    }


    public function __toString ()
    {
        if ($this->getParent())
        {
            return $this->getParent()->getTitle();
        }

        return $this->getTitle();
    }


    public function getThread()
    {
        if ($this->getParent())
        {
            return $this->getParent()->getTitle();
        }

        return $this->getTitle();
    }

    public function getResponse(): ?self
    {
        return $this->response;
    }

    public function setResponse(?self $response): self
    {
        $this->response = $response;

        return $this;
    }

    public function addResponse(?self $response): self
    {
        if (!$this->response->contains($response)) {
            $this->response[] = $response;
            $response->setResponse($this);
        }

        return $this;
    }

    public function removeResponse(self $response): self
    {
        if ($this->response->removeElement($response)) {
            // set the owning side to null (unless already changed)
            if ($response->getResponse() === $this) {
                $response->setResponse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ForumLikes[]
     */
    public function getForumLikes(): Collection
    {
        return $this->forumLikes;
    }

    public function addForumLike(ForumLikes $forumLike): self
    {
        if (!$this->forumLikes->contains($forumLike)) {
            $this->forumLikes[] = $forumLike;
            $forumLike->setForumPost($this);
        }

        return $this;
    }

    public function removeForumLike(ForumLikes $forumLike): self
    {
        if ($this->forumLikes->removeElement($forumLike)) {
            // set the owning side to null (unless already changed)
            if ($forumLike->getForumPost() === $this) {
                $forumLike->setForumPost(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|ForumReport[]
     */
    public function getForumReports(): Collection
    {
        return $this->forumReports;
    }

    public function addForumReport(ForumReport $forumReport): self
    {
        if (!$this->forumReports->contains($forumReport)) {
            $this->forumReports[] = $forumReport;
            $forumReport->setForumPost($this);
        }

        return $this;
    }

    public function removeForumReport(ForumReport $forumReport): self
    {
        if ($this->forumReports->removeElement($forumReport)) {
            // set the owning side to null (unless already changed)
            if ($forumReport->getForumPost() === $this) {
                $forumReport->setForumPost(null);
            }
        }

        return $this;
    }

    /**
     * Get the value of emailNotificationForum
     */
    public function getEmailNotificationForum()
    {
        return $this->emailNotificationForum;
    }

    /**
     * Set the value of emailNotificationForum
     *
     * @return  self
     */
    public function setEmailNotificationForum($emailNotificationForum)
    {
        $this->emailNotificationForum = $emailNotificationForum;

        return $this;
    }
}
