<?php

namespace App\Entity;

use App\Repository\MessageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=MessageRepository::class)
 */
class Message
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list", "detail"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"list", "detail"})
     */
    private $subject;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="messagesSent")
     * @ORM\JoinColumn(nullable=false)
     * @Groups({"list", "detail"})
     */
    private $sender;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="messagesReceived")
     * @ORM\JoinColumn(nullable=false)
     * @Groups({"list", "detail"})
     */
    private $recipient;

    /**
     * @ORM\Column(type="text")
     * @Groups({"list", "detail"})
     */
    private $body;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     * @Groups({"list", "detail"})
     */
    private $openAt;

    /**
     * @ORM\ManyToOne(targetEntity=Message::class, inversedBy="replies")
     * @Groups({"list", "detail"})
     */
    private $replyTo;

    /**
     * @ORM\OneToMany(targetEntity=Message::class, mappedBy="replyTo")
     * @Groups({"detail"})
     */
    private $replies;

    /**
     * @ORM\Column(type="datetime")
     * @Groups({"list", "detail"})
     */
    private $sentAt;

    /**
     * @ORM\OneToMany(targetEntity=MessageAttachment::class, mappedBy="message")
     * @Groups({"list", "detail"})
     */
    private $messageAttachments;

    public function __construct()
    {
        $this->replies            = new ArrayCollection();
        $this->messageAttachments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }

    public function getSender(): ?User
    {
        return $this->sender;
    }

    public function setSender(?User $sender): self
    {
        $this->sender = $sender;

        return $this;
    }

    public function getRecipient(): ?User
    {
        return $this->recipient;
    }

    public function setRecipient(?User $recipient): self
    {
        $this->recipient = $recipient;

        return $this;
    }

    public function getBody(): ?string
    {
        return $this->body;
    }

    public function setBody(string $body): self
    {
        $this->body = $body;

        return $this;
    }

    public function getOpenAt(): ?\DateTimeInterface
    {
        return $this->openAt;
    }

    public function setOpenAt(?\DateTimeInterface $openAt): self
    {
        $this->openAt = $openAt;

        return $this;
    }

    public function getReplyTo(): ?self
    {
        return $this->replyTo;
    }

    public function setReplyTo(?self $replyTo): self
    {
        $this->replyTo = $replyTo;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getReplies(): Collection
    {
        return $this->replies;
    }

    public function addReply(self $reply): self
    {
        if (!$this->replies->contains($reply)) {
            $this->replies[] = $reply;
            $reply->setReplyTo($this);
        }

        return $this;
    }

    public function removeReply(self $reply): self
    {
        if ($this->replies->removeElement($reply)) {
            // set the owning side to null (unless already changed)
            if ($reply->getReplyTo() === $this) {
                $reply->setReplyTo(null);
            }
        }

        return $this;
    }

    public function getSentAt(): ?\DateTimeInterface
    {
        return $this->sentAt;
    }

    public function setSentAt(\DateTimeInterface $sentAt): self
    {
        $this->sentAt = $sentAt;

        return $this;
    }

    /**
     * @return Collection|MessageAttachment[]
     */
    public function getMessageAttachments(): Collection
    {
        return $this->messageAttachments;
    }

    public function addMessageAttachment(MessageAttachment $messageAttachment): self
    {
        if (!$this->messageAttachments->contains($messageAttachment)) {
            $this->messageAttachments[] = $messageAttachment;
            $messageAttachment->setMessage($this);
        }

        return $this;
    }

    public function removeMessageAttachment(MessageAttachment $messageAttachment): self
    {
        if ($this->messageAttachments->removeElement($messageAttachment)) {
            // set the owning side to null (unless already changed)
            if ($messageAttachment->getMessage() === $this) {
                $messageAttachment->setMessage(null);
            }
        }

        return $this;
    }


    public function isUserInvolved(User $user)
    {
        return ($this->getSender() === $user or $this->getRecipient() === $user);
    }


    public function isOpen()
    {
        return $this->getOpenAt() !== null;
    }


    public function markAsOpen(): self
    {
        $this->setOpenAt(new \DateTime());
        return $this;
    }
}
