<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\LtiLineItemScoreRepository;
use Doctrine\ORM\Mapping as ORM;
use OAT\Library\Lti1p3Ags\Model\Score\Score;
use OAT\Library\Lti1p3Ags\Model\Score\ScoreInterface;

/**
 * @ORM\Entity(repositoryClass=LtiLineItemScoreRepository::class)
 */
class LtiLineItemScore
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private $userId;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $activityProgress;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $gradingProgress;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private $timestamp;

    /**
     * @ORM\Column(type="float")
     */
    private $scoreGiven;

    /**
     * @ORM\Column(type="float")
     */
    private $scoreMaximum;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $comment;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $scoringUserId;

    /**
     * @ORM\Column(type="string", length=40)
     */
    private $lineItemIdentifier;

    /**
     * @ORM\Column(type="datetime_immutable")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="json")
     */
    private array $additionalProperties = [];

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function setUserId(string $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getActivityProgress(): ?string
    {
        return $this->activityProgress;
    }

    public function setActivityProgress(string $activityProgress): self
    {
        $this->activityProgress = $activityProgress;

        return $this;
    }

    public function getGradingProgress(): ?string
    {
        return $this->gradingProgress;
    }

    public function setGradingProgress(string $gradingProgress): self
    {
        $this->gradingProgress = $gradingProgress;

        return $this;
    }

    public function getTimestamp(): ?string
    {
        return $this->timestamp;
    }

    public function setTimestamp(string $timestamp): self
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    public function getScoreGiven(): ?float
    {
        return $this->scoreGiven;
    }

    public function setScoreGiven(float $scoreGiven): self
    {
        $this->scoreGiven = $scoreGiven;

        return $this;
    }

    public function getScoreMaximum(): ?float
    {
        return $this->scoreMaximum;
    }

    public function setScoreMaximum(float $scoreMaximum): self
    {
        $this->scoreMaximum = $scoreMaximum;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getScoringUserId(): ?string
    {
        return $this->scoringUserId;
    }

    public function setScoringUserId(string $scoringUserId): self
    {
        $this->scoringUserId = $scoringUserId;

        return $this;
    }

    public function getLineItemIdentifier(): ?string
    {
        return $this->lineItemIdentifier;
    }

    public function setLineItemIdentifier(string $lineItemIdentifier): self
    {
        $this->lineItemIdentifier = $lineItemIdentifier;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getAdditionalProperties(): array
    {
        return $this->additionalProperties;
    }

    public function setAdditionalProperties(array $additionalProperties): self
    {
        $this->additionalProperties = $additionalProperties;

        return $this;
    }

    public function getScore(): ScoreInterface
    {
        return new Score(
            $this->userId,
            $this->activityProgress,
            $this->gradingProgress,
            $this->lineItemIdentifier,
            $this->scoreGiven,
            $this->scoreMaximum,
            $this->comment,
            new \DateTimeImmutable($this->timestamp),
            $this->additionalProperties
        );
    }

    public static function generateFromResultInterface(ScoreInterface $param): self
    {
        return (new LtiLineItemScore())
            ->setUserId($param->getUserIdentifier())
            ->setActivityProgress($param->getActivityProgressStatus())
            ->setGradingProgress($param->getGradingProgressStatus())
            ->setLineItemIdentifier($param->getLineItemIdentifier())
            ->setScoreGiven($param->getScoreGiven())
            ->setScoreMaximum($param->getScoreMaximum())
            ->setComment($param->getComment())
            ->setTimestamp($param->getTimestamp()->format('c'))
            ->setAdditionalProperties($param->getAdditionalProperties()->all())
        ;
    }
}
