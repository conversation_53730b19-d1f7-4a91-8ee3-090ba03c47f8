<?php

namespace App\Entity;

use App\Repository\FilterCategoryTranslationRepository;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslationInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslationTrait;
use <PERSON>ymfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=FilterCategoryTranslationRepository::class)
 */
class FilterCategoryTranslation implements TranslationInterface
{
    use TranslationTrait;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * migrations:* @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"library"})
     */
    private $name;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
