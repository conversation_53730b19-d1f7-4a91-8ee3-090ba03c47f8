<?php

namespace App\Entity;

use App\Repository\MaterialDownloadHistoryRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=MaterialDownloadHistoryRepository::class)
 */
class MaterialDownloadHistory
{
    use Blamable;
    use Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="materialDownloadHistories")
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="materialDownloadHistories")
     */
    private $user;

     /**
     * @ORM\ManyToOne(targetEntity=MaterialCourse::class, inversedBy="materialDownloadHistories")
     */
    private $material;
    
    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();        
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getMaterial(): ?MaterialCourse
    {
        return $this->material;
    }

    public function setMaterial(?MaterialCourse $material): self
    {
        $this->material = $material;

        return $this;
    }
}
