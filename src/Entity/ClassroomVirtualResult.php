<?php

namespace App\Entity;

use App\Repository\ClassroomVirtualResultRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ClassroomVirtualResultRepository::class)
 */
class ClassroomVirtualResult
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=Classroomvirtual::class, inversedBy="classroomVirtualResult", cascade={"persist", "remove"})
     */
    private $classroomVirtual;

    /**
     * @ORM\Column(type="text")
     */
    private $result;

    /**
     * @ORM\Column(type="datetime")
     */
    private $createdAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $updateAt;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClassroomVirtual(): ?Classroomvirtual
    {
        return $this->classroomVirtual;
    }

    public function setClassroomVirtual(?Classroomvirtual $classroomVirtual): self
    {
        $this->classroomVirtual = $classroomVirtual;

        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(string $result): self
    {
        $this->result = $result;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdateAt(): ?\DateTimeInterface
    {
        return $this->updateAt;
    }

    public function setUpdateAt(?\DateTimeInterface $updateAt): self
    {
        $this->updateAt = $updateAt;

        return $this;
    }
}
