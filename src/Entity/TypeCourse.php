<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Repository\TypeCourseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=TypeCourseRepository::class)
 */
class TypeCourse implements TranslatableInterface
{
    use TranslatableTrait;

    use Blamable;
    use Timestampable;

    public const TYPE_TELEFORMACION = 1;
    public const TYPE_PRESENCIAL = 2;
    public const TYPE_MIXTO = 3;
    public const TYPE_AULA_VIRTUAL = 4;

    public const TYPE_TELEFORMACION_EXTERN = 5;
    public const TYPE_PRESENCIAL_EXTERN = 6;
    public const TYPE_MIXTO_EXTERN = 7;
    public const TYPE_AULA_VIRTUAL_EXTERN = 8;

    public const CODE_ONLINE = 'online';
    public const CODE_ONSITE = 'on_site';
    public const CODE_MIXED = 'mixed';
    public const CODE_VIRTUAL_CLASSROOM = 'virtual_classroom';

    public const CODE_ONLINE_EXTERN = 'online_extern';
    public const CODE_ONSITE_EXTERN = 'on_site_extern';
    public const CODE_MIXED_EXTERN = 'mixed_extern';
    public const CODE_VIRTUAL_CLASSROOM_EXTERN = 'virtual_classroom_extern';

    public const TYPE_NAME_ONLINE = 'online';
    public const TYPE_NAME_ON_SITE = 'on_site';
    public const TYPE_NAME_MIXED = 'mixed';
    public const TYPE_NAME_VIRTUAL_CLASSROOM = 'virtual_classroom';

    public const ICONS = [
        self::TYPE_TELEFORMACION => 'fa fa-cloud',
        self::TYPE_PRESENCIAL => 'fa fa-users',
        self::TYPE_MIXTO => 'fa fa-desktop',
        self::TYPE_AULA_VIRTUAL => 'fa fa-video-camera',
        self::TYPE_TELEFORMACION_EXTERN => 'fa fa-cloud',
        self::TYPE_PRESENCIAL_EXTERN => 'fa fa-users',
        self::TYPE_MIXTO_EXTERN => 'fa fa-desktop',
        self::TYPE_AULA_VIRTUAL_EXTERN => 'fa fa-video-camera',
    ];

    public const TYPES = [
        self::TYPE_TELEFORMACION => 'online',
        self::TYPE_PRESENCIAL => 'on_site',
        self::TYPE_MIXTO => 'mixed',
        self::TYPE_AULA_VIRTUAL => 'virtual_classroom',
        self::TYPE_TELEFORMACION_EXTERN => 'online_extern',
        self::TYPE_PRESENCIAL_EXTERN => 'on_site_extern',
        self::TYPE_MIXTO_EXTERN => 'mixed_extern',
        self::TYPE_AULA_VIRTUAL_EXTERN => 'virtual_classroom_extern',
    ];

    public const COLORS = [
        self::TYPE_TELEFORMACION => 'var(--color-primary)',
        self::TYPE_PRESENCIAL => 'var(--color-secondary)',
        self::TYPE_MIXTO => 'var(--color-secondary-light)',
        self::TYPE_AULA_VIRTUAL => 'var(--color-secondary-darker)',
        self::TYPE_TELEFORMACION_EXTERN => 'var(--color-primary)',
        self::TYPE_PRESENCIAL_EXTERN => 'var(--color-secondary)',
        self::TYPE_MIXTO_EXTERN => 'var(--color-secondary-light)',
        self::TYPE_AULA_VIRTUAL_EXTERN => 'var(--color-secondary-darker)',
    ];
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list","update-course"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list","update-course"})
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $description;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private $code;

    /**
     * @ORM\OneToMany(targetEntity=TypeCourseAlerts::class, mappedBy="typeCourse", orphanRemoval=true)
     */
    private $typeCourseAlerts;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $denomination;

    /**
     * @ORM\OneToMany(targetEntity=TypeCourseAnnouncementStepCreation::class, mappedBy="typeCourse")
     */
    private $typeCourseAnnouncementStepCreations;

    /**
     * @ORM\ManyToMany(targetEntity=CourseCategory::class, mappedBy="typeCourse")
     */
    private $courseCategories;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->active = 1;
        $this->typeCourseAlerts = new ArrayCollection();
        $this->typeCourseAnnouncementStepCreations = new ArrayCollection();
        $this->courseCategories = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->getName();
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @Groups({"list"})
     *
     * @return string
     */
    public function getIcon()
    {
        return self::ICONS[$this->getId()] ?? self::ICONS[self::TYPE_TELEFORMACION];
    }

    /**
     * @Groups({"list"})
     *
     * @return string
     */
    public function getType()
    {
        return self::TYPES[$this->getId()] ?? self::TYPES[self::TYPE_TELEFORMACION];
    }

    public function getColor()
    {
        return self::COLORS[$this->getId()] ?? self::COLORS[self::TYPE_TELEFORMACION];
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    /**
     * @return Collection<int, TypeCourseAlerts>
     */
    public function getTypeCourseAlerts(): Collection
    {
        return $this->typeCourseAlerts;
    }

    public function addTypeCourseAlert(TypeCourseAlerts $typeCourseAlert): self
    {
        if (!$this->typeCourseAlerts->contains($typeCourseAlert)) {
            $this->typeCourseAlerts[] = $typeCourseAlert;
            $typeCourseAlert->setTypeCourse($this);
        }

        return $this;
    }

    public function removeTypeCourseAlert(TypeCourseAlerts $typeCourseAlert): self
    {
        if ($this->typeCourseAlerts->removeElement($typeCourseAlert)) {
            // set the owning side to null (unless already changed)
            if ($typeCourseAlert->getTypeCourse() === $this) {
                $typeCourseAlert->setTypeCourse(null);
            }
        }

        return $this;
    }

    public function getImageByType()
    {
        return self::TYPES[$this->getId()] ?? self::TYPES[self::TYPE_TELEFORMACION];
    }

    public function getDenomination(): ?string
    {
        return $this->denomination;
    }

    public function setDenomination(?string $denomination): self
    {
        $this->denomination = $denomination;

        return $this;
    }

    /**
     * @return Collection<int, TypeCourseAnnouncementStepCreation>
     */
    public function getTypeCourseAnnouncementStepCreations(): Collection
    {
        return $this->typeCourseAnnouncementStepCreations;
    }

    public function addTypeCourseAnnouncementStepCreation(TypeCourseAnnouncementStepCreation $typeCourseAnnouncementStepCreation): self
    {
        if (!$this->typeCourseAnnouncementStepCreations->contains($typeCourseAnnouncementStepCreation)) {
            $this->typeCourseAnnouncementStepCreations[] = $typeCourseAnnouncementStepCreation;
            $typeCourseAnnouncementStepCreation->setTypeCourse($this);
        }

        return $this;
    }

    public function removeTypeCourseAnnouncementStepCreation(TypeCourseAnnouncementStepCreation $typeCourseAnnouncementStepCreation): self
    {
        if ($this->typeCourseAnnouncementStepCreations->removeElement($typeCourseAnnouncementStepCreation)) {
            // set the owning side to null (unless already changed)
            if ($typeCourseAnnouncementStepCreation->getTypeCourse() === $this) {
                $typeCourseAnnouncementStepCreation->setTypeCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CourseCategory>
     */
    public function getCourseCategories(): Collection
    {
        return $this->courseCategories;
    }

    public function addCourseCategory(CourseCategory $courseCategory): self
    {
        if (!$this->courseCategories->contains($courseCategory)) {
            $this->courseCategories[] = $courseCategory;
            $courseCategory->addTypeCourse($this);
        }

        return $this;
    }

    public function removeCourseCategory(CourseCategory $courseCategory): self
    {
        if ($this->courseCategories->removeElement($courseCategory)) {
            $courseCategory->removeTypeCourse($this);
        }

        return $this;
    }
}
