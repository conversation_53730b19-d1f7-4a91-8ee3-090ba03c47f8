<?php

namespace App\Entity;

use App\Repository\CourseStatRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=CourseStatRepository::class)
 */
class CourseStat
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $valoration_nps;

    /**
     * @ORM\Column(type="decimal", precision=10, scale=2, nullable=true)
     */
    private $count_valoration_nps;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="courseStat")
     */
    private $course;

    public function __construct()
    {

    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getValorationNps(): ?float
    {
        return $this->valoration_nps;
    }

    public function setValorationNps(?float $valoration_nps): self
    {
        $this->valoration_nps = $valoration_nps;

        return $this;
    }

    public function getCountValorationNps(): ?float
    {
        return $this->count_valoration_nps;
    }

    public function setCountValorationNps(?float $count_valoration_nps): self
    {
        $this->count_valoration_nps = $count_valoration_nps;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

}
