<?php

namespace App\Entity;

use App\Repository\HigherLowerWordsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=HigherLowerWordsRepository::class)
 */
class HigherLowerWords
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"higherLower"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=HigherLower::class, inversedBy="higherLowerWords")
     */
    private $higherLower;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"higherLower"})
     */

    private $position;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"higherLower"})
     */
    private $word;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getHigherLower(): ?HigherLower
    {
        return $this->higherLower;
    }

    public function setHigherLower(?HigherLower $higherLower): self
    {
        $this->higherLower = $higherLower;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getWord(): ?string
    {
        return $this->word;
    }

    public function setWord(string $word): self
    {
        $this->word = $word;

        return $this;
    }
}
