<?php

namespace App\Entity;

use App\Repository\ParejasRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Serializer\Annotation\Groups;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ParejasRepository::class)
 */
class Parejas
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"roulette","parejas"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=chapter::class, inversedBy="parejas")
     */
    private $chapter;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"roulette","parejas"})
     */
    private $tipo;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"roulette","parejas"})
     */
    private $tiempo;

    /**
     * @ORM\OneToMany(targetEntity=ParejasImagen::class, mappedBy="parejas", cascade={"persist"})
     */
    private $parejasImagens;

    public function __construct()
    {
        $this->parejasImagens = new ArrayCollection();
    }


    public function __clone()
    {
        $this->id = null;

        // clone images
        $images = $this->getParejasImagens();
        $this->parejasImagens = new ArrayCollection();
        if(\count($images)){
            foreach ($images as $parejasImagen) {
                $imagenClonada = clone $parejasImagen;
                $this->addParejasImagen($imagenClonada);
            }
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChapter(): ?chapter
    {
        return $this->chapter;
    }

    public function setChapter(?chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getTipo(): ?int
    {
        return $this->tipo;
    }

    public function setTipo(int $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getTiempo(): ?int
    {
        return $this->tiempo;
    }

    public function setTiempo(int $tiempo): self
    {
        $this->tiempo = $tiempo;

        return $this;
    }

    /**
     * @return Collection<int, ParejasImagen>
     */
    public function getParejasImagens(): Collection
    {
        return $this->parejasImagens;
    }

    public function addParejasImagen(ParejasImagen $parejasImagen): self
    {
        if (!$this->parejasImagens->contains($parejasImagen)) {
            $this->parejasImagens[] = $parejasImagen;
            $parejasImagen->setParejas($this);
        }

        return $this;
    }

    public function removeParejasImagen(ParejasImagen $parejasImagen): self
    {
        if ($this->parejasImagens->removeElement($parejasImagen)) {
            // set the owning side to null (unless already changed)
            if ($parejasImagen->getParejas() === $this) {
                $parejasImagen->setParejas(null);
            }
        }

        return $this;
    }
}
