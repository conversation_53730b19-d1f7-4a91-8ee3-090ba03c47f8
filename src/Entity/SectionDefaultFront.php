<?php

namespace App\Entity;

use App\Repository\SectionDefaultFrontRepository;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;

/**
 * @ORM\Entity(repositoryClass=SectionDefaultFrontRepository::class)
 */
class SectionDefaultFront implements TranslatableInterface
{
    
    public const ID_SECTION_FILTER = -1;
    public const ID_SECTION_OPENCOURSE = -2;
    
    use TranslatableTrait;
   
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $name;

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private $description;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isActive;

    /**
     * @ORM\Column(type="integer", nullable=true)
     */
    private $idSection;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isOpenCourse;
  

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(?bool $isActive): self
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getIdSection(): ?int
    {
        return $this->idSection;
    }

    public function setIdSection(?int $idSection): self
    {
        $this->idSection = $idSection;

        return $this;
    }

    public function isOpenCourse(): ?bool
    {
        return $this->isOpenCourse;
    }

    public function setIsOpenCourse(?bool $isOpenCourse): self
    {
        $this->isOpenCourse = $isOpenCourse;

        return $this;
    }
}
