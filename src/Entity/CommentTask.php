<?php

namespace App\Entity;

use App\Repository\CommentTaskRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;


/**
 * @ORM\Entity(repositoryClass=CommentTaskRepository::class)
 */
class CommentTask
{
    use Blamable;
    use Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"detail"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=HistoryDeliveryTask::class, inversedBy="commentTasks")
     */
    private $historyDeliveryTask;

    /**
     * @ORM\ManyToOne(targetEntity=CommentTask::class, inversedBy="children")
     * @Groups({"detail"})
     */
    private $parent;

    /**
     * @ORM\OneToMany(targetEntity=CommentTask::class, mappedBy="parent")
     * @Groups({"detail"})
     */
    private $children;

    /**
     * @ORM\Column(type="text", nullable=true)
     * @Groups({"detail"})
     */
    private $comment;

    public function __construct()
    {
        $this->children = new ArrayCollection();
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getHistoryDeliveryTask(): ?HistoryDeliveryTask
    {
        return $this->historyDeliveryTask;
    }

    public function setHistoryDeliveryTask(?HistoryDeliveryTask $historyDeliveryTask): self
    {
        $this->historyDeliveryTask = $historyDeliveryTask;

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->removeElement($child)) {
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

	/**
	 * @return string|null
	 * @Groups({"detail"})
	 */
	public function getUserCreation(): ?User
	{
		return $this->createdBy;
	}
}
