<?php

namespace App\Entity;

use App\Behavior\Fileable;
use App\Repository\AnnouncementObservationDocumentRepository;
use Doctrine\ORM\Mapping as ORM;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AnnouncementObservationDocumentRepository::class)
 * @Vich\Uploadable()
 */
class AnnouncementObservationDocument
{
    public const FILE_TYPE_PDF = 'pdf';
    public const FILE_TYPE_VIDEO = 'video';
    public const FILE_TYPE_COMPRESSED = 'compressed';
    public const FILE_TYPE_IMAGE = 'image';
    public const FILE_TYPE_OFFICE_SUITE = 'office';
    public const FILE_TYPE_TXT = 'txt';

    use AtAndBy;
    use Fileable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=AnnouncementObservation::class, inversedBy="announcementObservationDocuments")
     * @ORM\JoinColumn(nullable=false)
     */
    private $announcementObservation;

    /**
     * @ORM\Column(type="string", length=20)
     */
    private $type;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAnnouncementObservation(): ?AnnouncementObservation
    {
        return $this->announcementObservation;
    }

    public function setAnnouncementObservation(?AnnouncementObservation $announcementObservation): self
    {
        $this->announcementObservation = $announcementObservation;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
