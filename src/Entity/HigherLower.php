<?php

namespace App\Entity;

use App\Repository\HigherLowerRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use DateTime;
use Gedmo\Mapping\Annotation as Gedmo;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=HigherLowerRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class HigherLower
{
    use Blamable;
    use Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"higherLower"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"higherLower"})
     */
    private $title;

    /**
     * @ORM\ManyToOne(targetEntity=Chapter::class, inversedBy="higherLowers")
     */
    private $chapter;

    /**
     * @ORM\Column(type="integer")
     * @Groups({"higherLower"})
     */
    private $time;

    /**
     * @ORM\OneToMany(targetEntity=HigherLowerWords::class, mappedBy="higherLower", cascade={"persist"})
     * @ORM\OrderBy({"position" = "DESC"})
     * @Groups({"higherLower"})
     */
    private $higherLowerWords;

    public function __construct()
    {
        $this->higherLowerWords = new ArrayCollection();
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
    }

    public function __clone(){
        //Clone words
        $higherLowerWords = $this->getHigherLowerWords();
        $this->higherLowerWords = new ArrayCollection();
        if(\count($higherLowerWords)){
            foreach($higherLowerWords as $higherLowerWord){
                $cloneHigherLowerWord = clone $higherLowerWord;
                $this->addHigherLowerWord($cloneHigherLowerWord);
            }
        }
    }

    public function __toString()
    {
        return $this->title;
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getChapter(): ?Chapter
    {
        return $this->chapter;
    }

    public function setChapter(?Chapter $chapter): self
    {
        $this->chapter = $chapter;

        return $this;
    }

    public function getTime(): ?int
    {
        return $this->time;
    }

    public function setTime(int $time): self
    {
        $this->time = $time;

        return $this;
    }

    /**
     * @return Collection<int, HigherLowerWords>
     */
    public function getHigherLowerWords(): Collection
    {
        return $this->higherLowerWords;
    }

    public function addHigherLowerWord(HigherLowerWords $higherLowerWord): self
    {
        if (!$this->higherLowerWords->contains($higherLowerWord)) {
            $this->higherLowerWords[] = $higherLowerWord;
            $higherLowerWord->setHigherLower($this);
        }

        return $this;
    }

    public function removeHigherLowerWord(HigherLowerWords $higherLowerWord): self
    {
        if ($this->higherLowerWords->removeElement($higherLowerWord)) {
            // set the owning side to null (unless already changed)
            if ($higherLowerWord->getHigherLower() === $this) {
                $higherLowerWord->setHigherLower(null);
            }
        }

        return $this;
    }
}
