<?php

namespace App\Form\Type\Admin;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use App\Repository\FilterRepository;
use App\Repository\FilterCategoryRepository;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormEvents;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;


class UserFilterType extends AbstractType
{
    private $filterFields;

    public function __construct($filterFields,EntityManagerInterface $em)
    {
        $this->filterFields = $filterFields;
        $this->em        = $em;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {  
        $builder->addEventListener(FormEvents::PRE_SET_DATA, array($this, 'onPreSetData'));
        $builder->addEventListener(FormEvents::PRE_SUBMIT, array($this, 'onPreSubmit'));
        
    }

    protected function addElements(FormInterface $form, FilterCategory $filterCategory = null) {        
        $form->add('filterCategory', EntityType::class, array(
            'required' => true,
            'data' => $filterCategory,
            'placeholder' => 'Select a filter category...',
            'class' => FilterCategory::class
        ));        
        
        $filters = array();
        
        
        if ($filterCategory) {            
            $filterRepository = $this->em->getRepository(Filter::class);
            
            $filters = $filterRepository->createQueryBuilder("q")
                ->where("q.filterCategory = :filterCategory")
                ->setParameter("filterCategory", $filterCategory->getId())
                ->getQuery()
                ->getResult();
        }
                
        $form->add('filter', EntityType::class, array(
            'required' => true,
            'placeholder' => 'Select a filter ...',
            'class' => Filter::class,
            'choices' => $filters
        ));
    }

    function onPreSubmit(FormEvent $event) {
        $form = $event->getForm();
        $data = $event->getData();
       
        $filterCategory = $this->em->getRepository(FilterCategory::class)->find($data['filterCategory']);
        
        $this->addElements($form, $filterCategory);
    }

    function onPreSetData(FormEvent $event) {
        $filterData = $event->getData();
        $form = $event->getForm();
       
        $filter = $filterData && $filterData->getFilterCategory() ? $filterData->getFilterCategory() : null;
        
        $this->addElements($form, $filter);
    }

    public function getFieldClass ($type)
    {
        switch ($type) {
            case "choice":
                return ChoiceType::class;
            case "date":
                return DateType::class;
            default:
                return null;
        }
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'App\Entity\UserFilter'
        ));
    }
}
