<?php

declare(strict_types=1);

namespace App\Command;

use App\Enum\StatsReportType;
use App\Enum\ZipFileTaskEnum;
use App\Service\ZipFileTask\ZipFileTaskService;
use App\Utils\ZipFile;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class RemoveZipFilesTaskExpiredCommand extends Command
{
    private string $zipFilesDirPath;

    public function __construct(
        private readonly ParameterBagInterface $params,
        private readonly ZipFileTaskService $zipFileTaskService
    ) {
        parent::__construct();
    }

    private function setZipFilesDirPath(string $type): void
    {
        $this->zipFilesDirPath = ZipFile::checkPath(
            $this->params->get('app.file_manager.base_dir'),
            ZipFileTaskEnum::ZIP_PATH_COMPRESSED
        );
        if (StatsReportType::DIPLOMAS === $type) {
            $this->zipFilesDirPath = ZipFile::checkPath(
                $this->params->get('app.file_manager.base_dir'),
                ZipFileTaskEnum::ZIP_PATH_COMPRESSED
            );
        }
    }

    private function checkPathDirZipFiles(): bool
    {
        if (file_exists($this->zipFilesDirPath)) {
            return true;
        }

        return false;
    }

    private function formatOutputStyle(OutputInterface $output): void
    {
        $outputStyleRed = new OutputFormatterStyle('red');
        $outputStyleGreen = new OutputFormatterStyle('green');

        $output->getFormatter()->setStyle('error', $outputStyleRed);
        $output->getFormatter()->setStyle('green', $outputStyleGreen);
    }

    private function errorMessages(OutputInterface $output, $message): void
    {
        $output->write('<error>' . $message . '</error>', true);
    }

    private function successMessages(OutputInterface $output, $message): void
    {
        $output->write('<green>' . $message . '</green>', true);
    }

    protected function configure(): void
    {
        $this->setName('zip:files:delete-expired')
            ->setDescription('Remove expired zip files')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->formatOutputStyle($output);
        $this->successMessages($output, 'Starting to delete expired zip files');

        $zipFilesTaks = $this->zipFileTaskService->getRemoveZipFilesTaskExpired();

        if ($zipFilesTaks) {
            $this->successMessages($output, 'Starting to delete expired zip files: ' . \count($zipFilesTaks));
            $deletedFiles = 0;

            foreach ($zipFilesTaks as $itemZipFile) {
                $this->setZipFilesDirPath($itemZipFile->getType());
                if ($this->checkPathDirZipFiles()) {
                    $messages = $this->zipFileTaskService->deleteZipFilesTask(
                        $itemZipFile,
                        $this->zipFilesDirPath,
                        $deletedFiles
                    );
                    if ($messages['status']) {
                        $this->successMessages($output, $messages['message']);
                    } else {
                        $this->errorMessages($output, $messages['message']);
                    }
                } else {
                    $this->errorMessages($output, 'Directory zip files does not exist: ' . $itemZipFile->getType());
                }
            }
        } else {
            $this->errorMessages($output, 'No expired zip files found');
        }

        return Command::SUCCESS;
    }
}
