<?php

declare(strict_types=1);

namespace App\Command\Task;

use App\Entity\Task;
use App\Exception\NoAvailableSlotException;
use App\Exception\TaskNotFoundException;
use App\Resources\Traits\Command\CommandTimeoutTrait;
use App\Service\Task\TaskService;
use App\Service\TaskCron\TaskExecutorService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class ExecuteTaskCommand extends Command
{
    use CommandTimeoutTrait;

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly TaskExecutorService $taskExecutorService,
        private readonly TaskService $taskService,
        private readonly TemplatedEmailService $templatedEmailService,
        private readonly LoggerInterface $logger
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('task:execute')
            ->setDescription('Execute a programmed task');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->text('[task:execute] ');

        try {
            try {
                $executionSlot = $this->taskExecutorService->getAvailableExecutionSlot();
                $nextTask = $this->taskService->getNextTask($executionSlot->allowsLongRunningTasks());

                if ($nextTask) {
                    $io->text('{Task_id: ' . $nextTask->getId() . '} ');
                } else {
                    $io->text('{No Task_id} ');
                    $io->text('SUCCESS | ');

                    return Command::SUCCESS;
                }

                // Instead of executing the task directly, we use the Process component
                return $this->executeTaskWithProcess($nextTask, $io);
            } catch (NoAvailableSlotException $e) {
                $io->note('SLOTS-EXCEEDED');

                return Command::SUCCESS;
            } catch (TaskNotFoundException $e) {
                $io->text('{No Task_id} ');
                $io->text('SUCCESS | ');

                return Command::SUCCESS;
            }
        } catch (\Exception $e) {
            $io->error($e->getMessage());
            $io->text('FAILURE | ');

            $this->templatedEmailService->sendErrorNotification(
                'Task',
                $e,
                isset($nextTask) ? $nextTask->getId() : null
            );

            return Command::FAILURE;
        }
    }

    /**
     * Executes a task using the Symfony Process component.
     * Uses the timeout defined in the CronJobTimeout entity.
     */
    private function executeTaskWithProcess(Task $task, SymfonyStyle $io): int
    {
        return $this->executeTaskProcess($task, $io);
    }
}
