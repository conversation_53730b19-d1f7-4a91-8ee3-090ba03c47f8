<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\HelpEntityTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\Announcement;
use App\Entity\AnnouncementAprovedCriteria;
use App\Entity\AnnouncementCriteria;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementObservation;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Center;
use App\Entity\Chapter;
use App\Entity\ConfigurationClientAnnouncement;
use App\Entity\Course;
use App\Entity\Department;
use App\Entity\Export;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\ForumPost;
use App\Entity\MaterialCourse;
use App\Entity\Message;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\ProfessionalCategory;
use App\Entity\RecoveryCode;
use App\Entity\TaskCourse;
use App\Entity\TaskCourseGroup;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseTranslation;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\ConfigurationClientAnnouncement as EnumConfigurationClientAnnouncement;
use App\Enum\Games;
use App\Enum\MaterialsCourseEnum;
use App\Enum\TypeCourse as EnumTypeCourse;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Nps\NpsExtraAnswerUserService;
use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use App\Utils\TimeZoneConverter\TimeZoneConverter;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\ChoiceFilter;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use KMS\FroalaEditorBundle\Form\Type\FroalaEditorType;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementCrudController extends AbstractCrudController
{
    use SerializerTrait;
    use HelpEntityTrait;
    use VueAppDefaultConfiguration;

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private RequestStack $requestStack;
    protected TranslatorInterface $translator;
    private MailerInterface $mailer;
    private AdminUrlGenerator $adminUrlGenerator;
    private AdminContextProvider $context;
    private CourseRepository $courseRepository;
    private JWTManager $JWTManager;
    private AnnouncementGroupService $announcementGroupService;
    private SettingsService $settings;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    private NpsExtraAnswerUserService $NpsExtraAnswerUserService;
    private AnnouncementExtraService $announcementExtraService;
    private AnnouncementAuthorizationService $announcementAuthorizationService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        MailerInterface $mailer,
        AdminUrlGenerator $adminUrlGenerator,
        AdminContextProvider $context,
        CourseRepository $courseRepository,
        JWTManager $JWTManager,
        AnnouncementGroupService $announcementGroupService,
        SettingsService $settings,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        NpsExtraAnswerUserService $NpsExtraAnswerUserService,
        AnnouncementExtraService $announcementExtraService,
        AnnouncementAuthorizationService $announcementAuthorizationService,
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->mailer = $mailer;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->context = $context;
        $this->courseRepository = $courseRepository;
        $this->JWTManager = $JWTManager;
        $this->announcementGroupService = $announcementGroupService;
        $this->settings = $settings;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->NpsExtraAnswerUserService = $NpsExtraAnswerUserService;
        $this->announcementExtraService = $announcementExtraService;
        $this->announcementAuthorizationService = $announcementAuthorizationService;
    }

    public static function getEntityFqcn(): string
    {
        return Announcement::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('announcements.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('announcements.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->addFormTheme('@KMSFroalaEditor/Form/froala_widget.html.twig')
            ->setSearchFields(['id'])
            ->overrideTemplate('crud/detail', 'admin/announcement/detail.html.twig')
            ->overrideTemplate('crud/index', 'admin/announcement/app.html.twig');
    }

    public function configureFields(string $pageName): iterable
    {
        $panelBasic = FormField::addPanel($this->translator->trans('common_areas.basic_information', [], 'messages', $this->getUser()->getLocale()));

        //  $course     = AssociationField::new('course', $this->translator->trans('announcements.configureFields.courses', [], 'messages', $this->getUser()->getLocale()));
        $startAt = DateTimeField::new('startAt', $this->translator->trans('announcements.configureFields.start_at', [], 'messages', $this->getUser()->getLocale()))->setColumns('col-md-4');
        $finishAt = DateTimeField::new('finishAt', $this->translator->trans('announcements.configureFields.finish_at', [], 'messages', $this->getUser()->getLocale()))->setColumns('col-md-4');
        $called = AssociationField::new('called', $this->translator->trans('announcements.configureFields.called', [], 'messages', $this->getUser()->getLocale()));

        $panelBasic = FormField::addPanel('Basic information')->setLabel($this->translator->trans('common_areas.basic_information', [], 'messages', $this->getUser()->getLocale()));

        $course = AssociationField::new('course')
            ->setFormTypeOption('query_builder', function (CourseRepository $courseRepository) {
                return $courseRepository->createQueryBuilder('c')
                    ->andWhere('c.createdBy = :user or :user MEMBER OF c.managers')
                    ->andWhere('c.active = 1')
                    ->setParameter('user', $this->getUser());
            });

        $id = IntegerField::new('id', 'ID')
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');
        $createdAt = DateTimeField::new('createdAt');
        $updatedAt = DateTimeField::new('updatedAt');
        $deletedAt = DateTimeField::new('deletedAt');

        $subsidized = BooleanField::new('subsidized', $this->translator->trans('announcements.configureFields.subsidized', [], 'messages', $this->getUser()->getLocale()))
            ->addJsFiles('build/announcementSubsidizedCheckbox.js');
        $maxUsers = IntegerField::new('maxUsers', $this->translator->trans('announcements.configureFields.max_users', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOption('attr', ['min' => 0, 'max' => 80, 'value' => 80]);
        $tutor = CollectionField::new('tutors')
            ->setEntryType('App\Form\Type\Admin\AnnouncementTutorType')
            ->setColumns('col-md-12')
            ->setLabel($this->translator->trans('announcements.configureFields.tutor', [], 'messages', $this->getUser()->getLocale()));

        $subsidizer = AssociationField::new('subsidizer', $this->translator->trans('announcements.configureFields.subsidizer', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOption('query_builder', function (UserRepository $userRepository) {
                return $userRepository->createQueryBuilder('u')
                    ->andWhere('JSON_CONTAINS(u.roles, :role) = 1')
                    ->setParameter('role', '"' . User::ROLE_SUBSIDIZER . '"');
            });

        $formativeActionType = ChoiceField::new('formativeActionType')
            ->setChoices(array_flip($this->settings->get('app.subsidizer.formativeActionTypes')))->setLabel($this->translator->trans('announcements.configureFields.formative_action_type', [], 'messages'));
        $format = ChoiceField::new('format', $this->translator->trans('announcements.configureFields.format', [], 'messages', $this->getUser()->getLocale()))
            ->setChoices(array_flip($this->settings->get('app.subsidizer.format')));

        $totalHours = IntegerField::new('totalHours')->setLabel($this->translator->trans('announcements.configureFields.total_hours', [], 'messages', $this->getUser()->getLocale()));
        $place = TextField::new('place', $this->translator->trans('announcements.configureFields.place', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenter = TextField::new('trainingCenter', $this->translator->trans('announcements.configureFields.training_center', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterAddress = TextField::new('trainingCenterAddress')->setLabel($this->translator->trans('announcements.configureFields.training_center_address', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterNif = TextField::new('trainingCenterNif')->setLabel($this->translator->trans('announcements.configureFields.training_center_nif', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterPhone = TextField::new('trainingCenterPhone')->setLabel($this->translator->trans('announcements.configureFields.training_center_phone', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterEmail = TextField::new('trainingCenterEmail')->setLabel($this->translator->trans('announcements.configureFields.training_center_email', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterTeacher = TextField::new('trainingCenterTeacher')->setLabel($this->translator->trans('announcements.configureFields.training_center_teacher', [], 'messages', $this->getUser()->getLocale()));
        $trainingCenterTeacherDni = TextField::new('trainingCenterTeacher')->setLabel($this->translator->trans('announcements.configureFields.training_center_teacher_dni', [], 'messages', $this->getUser()->getLocale()));
        $subsidizerEntity = ChoiceField::new('subsidizerEntity')
            ->setLabel($this->translator->trans('announcements.configureFields.subsidizer_entity', [], 'messages', $this->getUser()->getLocale()))
            ->setChoices(array_flip($this->settings->get('app.subsidizer.entities')));

        $information = Field::new('generalInformation', $this->translator->trans('course.configureFields.general_information', [], 'messages', $this->getUser()->getLocale()))
            ->setFormType(FroalaEditorType::class)->setColumns('col-md-12');

        $extra = CollectionField::new('extra')
        ->setEntryType(TextField::class)
        ->setFormTypeOptions([
            'allow_add' => true,
            'allow_delete' => true,
        ])
        ->setLabel($this->translator->trans('announcements.configureFields.extra', [], 'messages', $this->getUser()->getLocale()));
        if (Crud::PAGE_INDEX === $pageName) {
            return [$startAt, $finishAt, $course, $called, $extra];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return [$id, $startAt, $finishAt, $createdAt, $updatedAt, $deletedAt, $course, $called, $extra];
        } elseif (\in_array($pageName, [Crud::PAGE_NEW, Crud::PAGE_EDIT])) {
            $fields = [
                $panelBasic,
                $course->setColumns('col-md-4'),
                $startAt->setColumns('col-md-4'),
                $finishAt->setColumns('col-md-4'),
                $tutor,
            ];

            $fields = array_merge($fields, $otherFields);

            if (Crud::PAGE_EDIT == $pageName) {
                $course->setFormTypeOption('disabled', 'disabled');
            }

            if ($this->requestStack->getCurrentRequest()->get('courseId')) {
                $course->setFormTypeOption('query_builder', function (CourseRepository $courseRepository) {
                    return $courseRepository->createQueryBuilder('c')
                        ->andWhere('c.id = :course')
                        ->setParameter('course', $this->requestStack->getCurrentRequest()->get('courseId'));
                })->setFormTypeOption('disabled', 'disabled');
            }

            $fields = array_merge($fields, [
                $maxUsers->setColumns('col-xs-12 col-md-4'),
                $formativeActionType->setColumns('col-xs-12 col-md-4'),
                $format->setColumns('col-xs-12 col-md-4'),
                $totalHours->setColumns('col-xs-12 col-md-4'),
                $place->setColumns('col-xs-12 col-md-4'),
                $trainingCenter->setColumns('col-xs-12 col-md-4'),
                $trainingCenterAddress->setColumns('col-xs-12 col-md-4'),
                $trainingCenterNif->setColumns('col-xs-12 col-md-4'),
                $trainingCenterPhone->setColumns('col-xs-12 col-md-4'),
                $trainingCenterEmail->setColumns('col-xs-12 col-md-4'),
                $trainingCenterTeacher->setColumns('col-xs-12 col-md-4'),
                $trainingCenterTeacherDni->setColumns('col-xs-12 col-md-4'),
                $information,
            ]);

            if ($this->settings->get('app.subsidizer.active')) {
                $panelSubsidized = FormField::addPanel($this->translator->trans('announcements.configureFields.subsidized_announcement', [], 'messages', $this->getUser()->getLocale()))->setCustomOption('id', 'subs');
                array_push(
                    $fields,
                    $subsidized,
                    $panelSubsidized,
                    $subsidizerEntity->setColumns('col-xs-12 col-md-4'),
                    $subsidizer->setColumns('col-xs-12 col-md-4'),
                );
            }

            return $fields;
        }

        return [];
    }

    public function checkStatesCourses($query)
    {
        $newQuery = [];
        foreach ($query as $q) {
            $infoCourse = $this->courseRepository->find($q->getId());
            if ($infoCourse->isCompletedContentChapter()) {
                $newQuery = $infoCourse;
            }
        }

        return AssociationField::new('course', $this->translator->trans('announcements.configureFields.courses'))
            ->setFormTypeOptions([
                'choices' => $newQuery,
                'by_reference' => false,
            ])
            ->setColumns('col-xl-4 col-lg-4 col-md-4');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        $courseStatus = [UserCourse::STATUS_NO_STARTED, UserCourse::STATUS_STARTED, UserCourse::STATUS_FINISHED];
        $responseParameters->set('courseStatus', $courseStatus);
        if (Crud::PAGE_INDEX === $responseParameters->get('pageName')) {
            $localeNames = Locales::getNames();
            $locales = [];
            foreach ($this->settings->get('app.languages') as $locale) {
                $locales[$locale] = $localeNames[$locale];
            }

            $this->getHelp($responseParameters, 'Announcement');
            $reportEnabled = $this->em->getRepository(ConfigurationClientAnnouncement::class)->findOneBy([
                'code' => EnumConfigurationClientAnnouncement::REPORT,
            ]);

            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
                [
                    'subsidizerActive' => $this->settings->get('app.subsidizer.active'),
                    'clientsFields' => $this->settings->get('app.announcement_client_identification'),
                    'materialsEnabled' => $this->settings->get('app.course.materials.enabled'),
                    'tasksEnabled' => $this->settings->get('app.course.tasks.enabled'),
                    'chapterImagePath' => $this->settings->get('app.chapter_uploads_path'),
                    'status' => Announcement::STATUS_LIST,
                    'isReportEnabled' => null !== $reportEnabled,
                ]
            );
        }

        if ($this->requestStack->getCurrentRequest()->get('courseId')) {
            $responseParameters->set('courseId', $this->em->getRepository(Course::class)->find($this->requestStack->getCurrentRequest()->get('courseId')));
        }

        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $announcementRepository = $this->em->getRepository(Announcement::class);
            $entity = $this->context->getContext()->getEntity();
            $announcement = $announcementRepository->find($entity->getPrimaryKeyValue());

            $referrerAnnouncement = $this->adminUrlGenerator
                ->setController(AnnouncementCrudController::class)
                ->set('tab', 'materials')
                ->setAction(Action::DETAIL)
                ->setEntityId($entity)
                ->generateUrl();

            $referrerTask = $this->adminUrlGenerator
                ->setController(AnnouncementCrudController::class)
                ->set('tab', 'task')
                ->setAction(Action::DETAIL)
                ->setEntityId($announcement->getId())
                ->generateUrl();

            $tab = $this->context->getContext()->getRequest()->get('tab');
            if (!$tab) {
                $tab = 'configuration';
            }

            $referrerForumPost = $this->adminUrlGenerator
                ->setController(AnnouncementCrudController::class)
                ->set('tab', 'foro')
                ->setAction(Action::DETAIL)
                ->setEntityId($announcement->getId())
                ->generateUrl();

            $responseParameters->set('referrer', $referrerAnnouncement);
            $responseParameters->set('announcement', $announcement);
            $responseParameters->set('referrerTask', $referrerTask);
            $responseParameters->set('referrerForumPost', $referrerForumPost);
            $responseParameters->set('tab', $tab);
        }

        return $responseParameters;
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL)
            ->remove(Crud::PAGE_INDEX, Action::NEW);

        return $actions;
    }

    public function detail(AdminContext $context): KeyValueStore
    {
        $responseParameters = parent::detail($context);

        // TODO: Change the autogenerated stub

        $announcementRepository = $this->em->getRepository(Announcement::class);
        $entity = $context->getEntity();
        $announcement = $announcementRepository->find($entity->getPrimaryKeyValue());
        $taskAnnouncement = $this->em->getRepository(TaskCourse::class)->findBy(['announcement' => $announcement->getId()], ['dateDeliveryAnnouncement' => 'asc']);
        $forumPost = $this->em->getRepository(ForumPost::class)->forumPostAnnouncement($announcement->getId());

        $taskCourse = $this->em->getRepository(TaskCourse::class)->findBy(['course' => $announcement->getCourse(), 'announcement' => null]);
        $task = array_merge($taskCourse, $taskAnnouncement);
        /**
         * @var $creator User
         */
        $creator = $this->em->getRepository(User::class)->findWithDeleted($announcement->getCreatedBy()->getId());
        $tutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement->getId()]);
        $dataTutor = $tutor ? $tutor->getTutor() : $announcement->getCreatedBy();
        $observations = $this->em->getRepository(AnnouncementObservation::class)->findBy(['announcement' => $announcement]);

        $responseParameters->set('announcement', $announcement);
        $responseParameters->set('creator', $creator);
        $responseParameters->set('taskAnnouncement', $task);
        $responseParameters->set('valorationAnnouncement', $this->valorationAnnouncement($announcement));
        $responseParameters->set('tutor', $dataTutor);
        $responseParameters->set('forumPost', $forumPost);
        $responseParameters->set('observations', $observations);

        return $responseParameters;
    }

    public function configureFilters(Filters $filters): Filters
    {
        $courses = $this->em->getRepository(Announcement::class)->getCoursesUniques();
        $coursesUniques = [];
        foreach ($courses as $course) {
            $coursesUniques[$course['name']] = $course['id'];
        }

        if ($coursesUniques) {
            $filters->add(ChoiceFilter::new('course')->setChoices($coursesUniques)
                ->setLabel($this->translator->trans('course.label_in_singular', [], 'messages', $this->getUser()->getLocale())));
        }

        return $filters;
    }

    private function valorationAnnouncement($announcement): array
    {
        $opinions = $this->em->getRepository(UserCourse::class)->getNpsAnnouncent($announcement->getId());
        $languageDefault = $this->settings->get('app.defaultLanguage');
        $user = $this->getUser();
        $locale = (isset($user) && null != $user->getLocale()) ? $user->getLocale() : $languageDefault;

        $opinionsDetail = [];
        foreach ($opinions as $opinion) {
            $nps = $this->em->getRepository(Nps::class)->getValorationCourseByUser($opinion->getId(), $opinion->getUser());
            $questions = [];
            foreach ($nps as $np) {
                $questionTranslate = $this->em->getRepository(NpsQuestion::class)->getNpsTranslation($np->getQuestion()->getId(), $locale);
                $np->getQuestion()->setQuestion($questionTranslate ? $questionTranslate['question'] : $np->getQuestion()->getQuestion());
                $questions[] = $np;
            }

            $opinionsDetail[] = [
                'user' => $opinion->getUser(),
                'nps' => $questions,
            ];
        }

        return $opinionsDetail;
    }

    /**
     * @Route ("/admin/announcements/{id}/called", name="called")
     */
    public function called(Announcement $announcement): Response
    {
        $calleds = [];
        foreach ($announcement->getCalled() as $called) {
            $calleds[] = [
                'id' => $called->getId(),
                'notified' => $called->getNotified(),
                'user' => $called->getUser(),
                'messages' => $this->messagesUser($announcement, $called->getUser()),
                'linkUser' => $this->linkDetailUser($called),
            ];
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'called' => $calleds,
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['announcement', 'list', 'messages']]);
    }

    private function linkDetailUser($announcementUser)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(SubsidizerCrudController::class)
            ->setAction('user')
            ->set('announcement_user_id', $announcementUser->getId())
            ->generateUrl();
    }

    private function messagesUser($announcement, $user)
    {
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement->getId()]);
        $tutor = $announcementTutor ? $announcementTutor->getTutor() : $announcement->getCreatedBy();

        return $this->em->getRepository(Message::class)->findBy([
            'sender' => $user->getId(),
            'recipient' => $tutor->getId(),
            'openAt' => null,
        ]);
    }

    /**
     * @Route ("/admin/announcements/{id}/filters", name="filters-announcement")
     */
    public function filters(Announcement $announcement): Response
    {
        $departament = $this->em->getRepository(Department::class)->findAll();
        $center = $this->em->getRepository(Center::class)->findAll();
        $categories = ($this->settings->get('app.professional_categories.only_first_level')) ? $this->em->getRepository(ProfessionalCategory::class)->getProfesionalCategoryUser() : $this->em->getRepository(ProfessionalCategory::class)->findAll();
        $countries = $this->getCountries();
        $divisions = $this->getDivisions();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'filters' => [
                    'categories' => $this->settings->get('app.filter.category') ? $categories : '',
                    'departaments' => $this->settings->get('app.filter.departament') ? $departament : '',
                    'center' => $this->settings->get('app.filter.center') ? $center : '',
                    'countries' => $this->settings->get('app.filter.country') ? $countries : '',
                    'divisions' => $this->settings->get('app.filter.division') ? $divisions : '',
                    'translator' => [
                        'user_title' => $this->translator->trans('announcements.configureFields.search_user_title', [], 'messages', $this->getUser()->getLocale()),
                        'search_user' => $this->translator->trans('announcements.configureFields.placeholder_search_user', [], 'messages', $this->getUser()->getLocale()),
                        'search_category' => $this->translator->trans('announcements.configureFields.placeholder_search_category', [], 'messages', $this->getUser()->getLocale()),
                        'search_departament' => $this->translator->trans('announcements.configureFields.placeholder_search_department', [], 'messages', $this->getUser()->getLocale()),
                        'search_center' => $this->translator->trans('announcements.configureFields.placeholder_search_center', [], 'messages', $this->getUser()->getLocale()),
                        'search_country' => $this->translator->trans('announcements.configureFields.placeholder_search_country', [], 'messages', $this->getUser()->getLocale()),
                        'search_division' => $this->translator->trans('announcements.configureFields.placeholder_search_division', [], 'messages', $this->getUser()->getLocale()),
                        'search' => $this->translator->trans('announcements.configureFields.search', [], 'messages', $this->getUser()->getLocale()),
                        'result_found' => $this->translator->trans('announcements.configureFields.result_found', [], 'messages', $this->getUser()->getLocale()),
                        'clear_result' => $this->translator->trans('announcements.configureFields.clear_result', [], 'messages', $this->getUser()->getLocale()),
                        'user_called' => $this->translator->trans('announcements.configureFields.called_user', [], 'messages', $this->getUser()->getLocale()),
                    ],
                ],
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['announcement', 'user_area']]);
    }

    /**
     * @Route ("/admin/announcements/{id}/user_filters", name="users-filters-announcement")
     */
    public function user_filters(Announcement $announcement): Response
    {
        $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
        $filter_categories = $filterCategoryRepository->getCategories();
        $filterRepository = $this->em->getRepository(Filter::class);
        $arrayFilters = [];
        foreach ($filter_categories as $category) {
            $filters = [];
            foreach ($filterRepository->getFilterCategory($category->getId()) as $filter) {
                if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles()) && !$this->getUser()->getFilters()->contains($filter)) {
                    continue;
                }
                $filters[] = [
                    'id' => $filter->getId(),
                    'name' => $filter->getName(),
                ];
            }

            if (!empty($filters)) {
                $arrayFilters[] = [
                    'name' => $category->getName(),
                    'filters' => $filters,
                ];
            }
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'filters' => $arrayFilters,
                'translator' => [
                    'user_title' => $this->translator->trans('announcements.configureFields.search_user_title', [], 'messages', $this->getUser()->getLocale()),
                    'search_user' => $this->translator->trans('announcements.configureFields.placeholder_search_user', [], 'messages', $this->getUser()->getLocale()),
                    'search_category' => $this->translator->trans('announcements.configureFields.placeholder_search_category', [], 'messages', $this->getUser()->getLocale()),
                    'search_departament' => $this->translator->trans('announcements.configureFields.placeholder_search_department', [], 'messages', $this->getUser()->getLocale()),
                    'search_center' => $this->translator->trans('announcements.configureFields.placeholder_search_center', [], 'messages', $this->getUser()->getLocale()),
                    'search_country' => $this->translator->trans('announcements.configureFields.placeholder_search_country', [], 'messages', $this->getUser()->getLocale()),
                    'search_division' => $this->translator->trans('announcements.configureFields.placeholder_search_division', [], 'messages', $this->getUser()->getLocale()),
                    'search' => $this->translator->trans('announcements.configureFields.search', [], 'messages', $this->getUser()->getLocale()),
                    'result_found' => $this->translator->trans('announcements.configureFields.result_found', [], 'messages', $this->getUser()->getLocale()),
                    'clear_result' => $this->translator->trans('announcements.configureFields.clear_result', [], 'messages', $this->getUser()->getLocale()),
                    'user_called' => $this->translator->trans('announcements.configureFields.called_user', [], 'messages', $this->getUser()->getLocale()),
                ],
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['announcement', 'user_area']]);
    }

    /**
     * @Route ("/admin/announcements/{id}/search", name="announcement_search")
     */
    public function search(Announcement $announcement, Request $request): Response
    {
        $content = json_decode($request->getContent());

        $results = $this->em->getRepository(User::class)->getSearchCallables($announcement, $content);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'results' => $results,
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['announcement']]);
    }

    /**
     * @Route ("/admin/announcements/{id}/search_users_filters", name="announcement_search_users_filters")
     */
    public function search_users_filters(Announcement $announcement, Request $request): Response
    {
        $content = json_decode($request->getContent());
        $user = $this->getUser();
        $results = $this->em->getRepository(User::class)->getSearchUsersFiltersCallables($announcement, $user, $content);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'results' => $results,
            ],
        ];

        return $this->sendResponse($response, ['groups' => ['announcement']]);
    }

    /**
     * @Route ("/admin/announcements/{announcement}/call", name="announcement_call_everybody")
     */
    public function callEverybody(Announcement $announcement, Request $request): Response
    {
        $content = json_decode($request->getContent());

        $users = $this->em->getRepository(User::class)->getSearchCallables($announcement, $content);

        foreach ($users as $user) {
            $called = new AnnouncementUser();
            $called->setAnnouncement($announcement);
            $called->setUser($user);

            $announcement->addCalled($called);
        }

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/announcements/{announcement}/call_filters", name="announcement_call_filters_everybody")
     */
    public function callEverybodyUsingFilters(Announcement $announcement, Request $request): Response
    {
        $content = json_decode($request->getContent());
        $user = $this->getUser();
        $users = $this->em->getRepository(User::class)->getSearchUsersFiltersCallables($announcement, $user, $content);

        foreach ($users as $user) {
            $called = new AnnouncementUser();
            $called->setAnnouncement($announcement);
            $called->setUser($user);

            $announcement->addCalled($called);
        }

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/announcements/{announcement}/call/{user}", name="announcement_call_user")
     */
    public function call(Announcement $announcement, User $user): Response
    {
        $called = $this->em->getRepository(AnnouncementUser::class)->findOneBy(
            [
                'announcement' => $announcement,
                'user' => $user,
            ]
        );

        $announcementall = $this->em->getRepository(Announcement::class)->findAnnouncementCourseAndUser($announcement->getCourse()->getId(), $user->getId());

        if ($called) {
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => $this->translator->trans('announcements.configureFields.error_already_called_user', [], 'messages', $this->getUser()->getLocale()),
            ];
        } elseif ($announcementall) {
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => $this->translator->trans('announcements.configureFields.error_already_called_user_date', [], 'messages', $this->getUser()->getLocale()),
            ];
        } else {
            $called = new AnnouncementUser();
            $called->setAnnouncement($announcement);
            $called->setUser($user);

            $announcement->addCalled($called);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/announcements/{announcement}/uncall/{call}", name="announcement_uncall_user", methods={"GET", "POST","DELETE"})
     */
    public function unCall(Announcement $announcement, AnnouncementUser $call): Response
    {
        if ($announcement->getCalled()->contains($call)) {
            $this->em->remove($call);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
            ];
        } else {
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => 'Error: User hasn\'t be called to this announcement!',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/announcements/{announcement}/notify/{call}", name="announcement_notify_user", methods={"GET","POST"})
     *
     * @throws \Exception
     */
    public function notify(Announcement $announcement, AnnouncementUser $call, MailerInterface $mailer): Response
    {
        if ($announcement->getCalled()->contains($call)) {
            if (\is_null($call->getNotified())) {
                try {
                    $announcement = $this->em->getRepository(Announcement::class)->find($call->getAnnouncement()->getId());
                    $tutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);

                    $sendEmail = (new TemplatedEmail())
                        ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
                        ->to(new Address($call->getUser()->getEmail()))
                        ->subject($this->translator->trans('message_api.announcement.call_for_aplications', [], 'message_api', $call->getUser()->getLocale()))
                        ->htmlTemplate('template_email/announcement.html.twig')
                        ->context([
                            'user' => $call->getUser()->getFirstName(),
                            'locale' => $call->getUser()->getLocale(),
                            'emailUser' => $call->getUser()->getEmail(),
                            'dateStart' => $call->getAnnouncement()->getStartAt(),
                            'dateFinish' => $call->getAnnouncement()->getFinishAt(),
                            'course' => $call->getAnnouncement()->getCourse()->getName(),
                            'appFromName' => $this->settings->get('app.fromName'),
                            'announcement' => $announcement,
                            'tutor' => \count($call->getAnnouncement()->getTutors()) > 0 ? $tutor->getTutor()->getFullName() : $announcement->getCreatedBy()->getFullName(),
                            'codigo' => $this->activeAccountUser($call->getUser()),
                            'dataUser' => $call->getUser(),
                            'dataCourse' => $call->getAnnouncement()->getCourse(),
                        ]);

                    $this->mailer->send($sendEmail);

                    $call->setNotified(new \DateTime());
                    $this->em->flush();

                    $response = [
                        'status' => Response::HTTP_OK,
                        'error' => false,
                    ];
                } catch (TransportExceptionInterface $e) {
                    $response = [
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                        'error' => true,
                        'message' => 'Error: Notification could not be sent!',
                    ];
                }
            } else {
                $response = [
                    'status' => Response::HTTP_CONFLICT,
                    'error' => true,
                    'message' => 'Error: The user has already been notified!',
                ];
            }
        } else {
            $response = [
                'status' => Response::HTTP_CONFLICT,
                'error' => true,
                'message' => 'Error: User hasn\'t be called to this announcement!',
            ];
        }

        return $this->sendResponse($response);
    }

    private function activeAccountUser(User $user): string
    {
        $codigo = '';
        if ($user && !$user->getIsActive()) {
            $codigo = md5(date('His'));

            $recovery = new RecoveryCode();
            $recovery->setUser($user);
            $recovery->setEmail($user->getEmail());
            $recovery->setDateRecovery(new \DateTime());
            $recovery->setCodeActivation($codigo);
            $recovery->setState(0);

            $this->em->persist($recovery);
            $this->em->flush();
        }

        return $codigo;
    }

    public function createEntity(string $entityFqc)
    {
        if ($this->requestStack->getCurrentRequest()->get('courseId')) {
            $announcement = new Announcement();

            $courseRepository = $this->em->getRepository(Course::class);
            $course = $courseRepository->find($this->requestStack->getCurrentRequest()->get('courseId'));

            $announcement->setCourse($course);
            $announcement->setGeneralInformation($course->getGeneralInformation());

            return $announcement;
        }
    }

    private function getCountries(): array
    {
        $countries = [];

        if ($this->getUser()->isAdmin()) {
            $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);
        } elseif (!empty($this->getUser()->getManage()->getCountries())) {
            $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);

            $userCountries = [];
            foreach ($this->getUser()->getManage()->getCountries() as $country) {
                $userCountries[$country] = $countries[$country];
            }
            $countries = $userCountries;
        }

        return array_map(function ($key, $name) {
            return ['id' => $key, 'name' => $name];
        }, array_keys($countries), $countries);
    }

    private function getDivisions(): array
    {
        $divisions = [];

        if ($this->getUser()->isAdmin()) {
            $divisions = array_flip($this->settings->get('app.user.extrafields')['division']['options']['choices']);
        }

        return array_map(function ($key, $name) {
            return ['id' => $key, 'name' => $name];
        }, array_keys($divisions), $divisions);
    }

    /**
     * New methods for announcement app in vue.
     */

    /**
     * @Rest\Get("/admin/announcements/courses/{page}", requirements={"page"="\d+"})
     */
    public function findCourses(Request $request, int $page = 1): Response
    {
        try {
            $filter = $request->get('query');
            $typeCourse = $request->get('typeCourse');
            $user = $this->getUser();

            $courseRepository = $this->em->getRepository(Course::class);

            $courses = $courseRepository->findCoursesByFilters($filter, $typeCourse, $user, $page);

            $data = [];

            /** @var Course $course */
            foreach ($courses as $course) {
                if ($course->isCompleted()) {
                    $data[] = [
                        'id' => $course->getId(),
                        'code' => $course->getCode(),
                        'name' => $course->getName(),
                        'image' => $course->getImage(),
                    ];
                }
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'items' => $data,
                    'base-path' => '/uploads/images/course',
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/announcements/pre-selected-course/{id}")
     */
    public function getSelectedCourse(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'id' => $course->getId(),
                'name' => $course->getName(),
                'code' => $course->getCode(),
                'image' => $course->getImage(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/announcements/course/{id}/chapters")
     */
    public function loadCourseChapters(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(Chapter::class)->loadCourseChapters($course),
        ]);
    }

    /**
     * @Rest\Get("/admin/announcements/tutors")
     */
    public function loadTutors(): Response
    {
        $tutors = $this->em->getRepository(User::class)->loadTutors('fromCrud');

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $tutors,
        ]);
    }

    /**
     * @Rest\Get("/admin/announcements/available-filters")
     */
    public function getAnnouncementFilters(): Response
    {
        $typeCourses = [];
        /** @var User $user */
        $user = $this->getUser();
        /** @var TypeCourse $typeCourse */
        foreach ($this->em->getRepository(TypeCourse::class)->findBy(['active' => true]) as $typeCourse) {
            /** @var TypeCourseTranslation $translated */
            $translated = $typeCourse->translate($user->getLocale(), false);
            $name = $translated->getName();
            $description = $translated->getDescription();
            $typeCourses[] = [
                'id' => $typeCourse->getId(),
                'name' => $name ?? $typeCourse->getName(),
                'description' => $description ?? $typeCourse->getDescription(),
                'code' => $typeCourse->getCode(),
                'type' => $typeCourse->getType(),
                'denomination' => $typeCourse->getDenomination(),
            ];
        }
        $formationExtern = $this->em->getRepository(TypeCourse::class)->findBy(['active' => true, 'denomination' => EnumTypeCourse::EXTERN]);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'typeCourses' => $typeCourses,
                'hasFormationExtern' => (bool) $formationExtern,
                'hasReportZip' => $this->announcementConfigurationsService->hasExportReportZip(),
                'hasReportTask' => $this->announcementConfigurationsService->hasReportTask(),
                'extraOptions' => [
                    'STATUS' => Announcement::STATUS_LIST,
                    'extra' => $this->announcementExtraService->getUniqueExtras($this->getUser()->getLocale()),
                ],
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/announcements/{page}", name="admin_api_get_announcements", requirements={"page"="\d+"})
     */
    public function announcements(Request $request, int $page = 1): Response
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $announcements = $this->em->getRepository(Announcement::class)->getAnnouncementsPaginated($request, $page, $user);

            $includeTotal = !\in_array(User::ROLE_ADMIN, $user->getRoles()) && \in_array(User::ROLE_TUTOR, $user->getRoles());
            foreach ($announcements['data'] as &$announcement) {
                if ($includeTotal) {
                    $announcement['total'] = $this->em->getRepository(AnnouncementUser::class)->getAnnouncementTotal($announcement['id'], $user);
                }
                $announcement = TimeZoneConverter::checkTimezone($announcement);

                $typeCourse = $this->em->getRepository(TypeCourse::class)->find($announcement['idTypeCourse']);
                $typeCourseTranslation = $this->em->getRepository(TypeCourse::class)->buildTypeCourseArray($typeCourse, $user->getLocale());
                $announcement['modality'] = $typeCourseTranslation['name'];

                if (Announcement::STATUS_ACTIVE === $announcement['status'] || \is_null($announcement['status'])) {
                    $timezone = $announcement['timezone'];
                    $tz = new \DateTimeZone(empty($timezone) ? $this->settings->get('app.default_timezone') : $timezone);
                    $current = new \DateTime('now', $tz);
                    $startAt = $announcement['startAt'];
                    $finishAt = $announcement['finishAt'];

                    if ($current >= $startAt && $current <= $finishAt) {
                        $announcement['status'] = Announcement::STATUS_IN_PROGRESS;
                    } elseif ($current > $finishAt) {
                        $announcement['status'] = Announcement::STATUS_FINISHED;
                    }
                }
                if (empty($announcement['timezone'])) {
                    $announcement['timezone'] = $this->settings->get('app.default_timezone');
                }
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'total-items' => (int) $announcements['totalItems'],
                    'items' => $announcements['data'],
                ],
            ], ['datetime_format' => 'c']);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/participants/{page}", name="admin_api_get_participants", requirements={"page"="\d+"})
     */
    public function participants(Request $request, int $page = 1): Response
    {
        try {
            $filter = $request->get('query');
            $fromDate = $request->get('fromDate');
            $toDate = $request->get('toDate');
            $type = $request->get('type');
            $status = $request->get('status');
            $exportRepository = $this->em->getRepository(Export::class);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $exportRepository->loadData('export-stats-file', [
                    'filter' => $filter,
                    'fromDate' => $fromDate,
                    'toDate' => $toDate,
                    'type' => $type,
                    'status' => $status,
                ]),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/announcements/subsidized-data")
     */
    public function getSubsidizedData(): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'subsidizer' => $this->em->getRepository(User::class)->getUserSubsidizer(),
                'formativeActionType' => $this->settings->get('app.subsidizer.formativeActionTypes'),
                'format' => $this->settings->get('app.subsidizer.format'),
                'subsidizerEntity' => $this->settings->get('app.subsidizer.entities'),
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement", name="admin_save_new_announcement")
     */
    public function createAnnouncement(Request $request): Response
    {
        $announcement = new Announcement();
        $result = $this->setAnnouncementData($request, $announcement);
        if ($result instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'message' => 'Announcement has been created',
                'id' => $announcement->getId(),
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement/{id}/update", name="admin_update_announcement")
     */
    public function updateAnnouncement(Request $request, Announcement $announcement): Response
    {
        $result = $this->setAnnouncementData($request, $announcement);
        if ($result instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'message' => 'Announcement has been updated',
                'id' => $announcement->getId(),
            ],
        ]);
    }

    private function setAnnouncementData(Request $request, Announcement $announcement)
    {
        try {
            $courseData = $request->get('course');
            $startAt = $request->get('startAt');
            $finishAt = $request->get('finishAt');
            $tutorsData = $request->get('tutors');

            if (empty($courseData) || empty($startAt) || empty($finishAt)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => empty($courseData) ? 'Is required course' : (empty($startAt) ? 'Is required START AT' : 'Is required FINISH AT'),
                ]);
            }

            $courseDecoded = json_decode($courseData, true);
            $course = $this->em->getRepository(Course::class)->find($courseDecoded['id']);
            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Course not found',
                ]);
            }

            $announcement->setCourse($course)
                ->setStartAt(new \DateTime($startAt))
                ->setFinishAt(new \DateTime($finishAt));

            $tutors = new ArrayCollection();
            $tutorsDecoded = json_decode($tutorsData, true);
            $userRepository = $this->em->getRepository(User::class);
            foreach ($tutorsDecoded as $t) {
                $user = $userRepository->find($t['id']);
                if ($user) {
                    $announcementTutor = new AnnouncementTutor();
                    $announcementTutor->setTutor($user);
                    $announcementTutor->setAnnouncement($announcement);
                    $tutors->add($announcementTutor);
                }
            }

            $announcement->setTutors($tutors)
                ->setSubsidized(filter_var($request->get('subsidized', false), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE));

            if ($announcement->getSubsidized()) {
                $announcement->setSubsidizerEntity($request->get('subsidizerEntity'));
                $subsidizerData = $request->get('subsidizer');
                if (!empty($subsidizerData)) {
                    $announcement->setSubsidizer($userRepository->find($subsidizerData));
                }
            }
            $announcement->setMaxUsers($request->get('maxUsers'))
                ->setFormativeActionType($request->get('formativeActionType'))
                ->setFormat($request->get('format'))
                ->setTotalHours((float) $request->get('totalHours'))
                ->setPlace($request->get('place'))
                ->setTrainingCenter($request->get('trainingCenter'))
                ->setTrainingCenterAddress($request->get('trainingCenterAddress'))
                ->setTrainingCenterNif($request->get('trainingCenterNif'))
                ->setTrainingCenterPhone('trainingCenterPhone')
                ->setTrainingCenterEmail($request->get('trainingCenterEmail'))
                ->setTrainingCenterTeacherDni($request->get('trainingCenterTeacherDni'))
                ->setGeneralInformation($request->get('generalInformation'));

            $this->em->persist($announcement);
            $this->em->flush();

            return true;
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Get("/admin/announcement/{announcement}/chat/{user}")
     *
     * @throws NonUniqueResultException
     */
    public function announcementUserChat(Announcement $announcement, User $user): Response
    {
        $currentUser = $this->getUser();
        if (\in_array(User::ROLE_ADMIN, $currentUser->getRoles()) || $currentUser->getId() === $announcement->getCreatedBy()->getId()) {
            $findUserChat = $currentUser;
        } else {
            /** @var User $findTutor */
            $findTutor = $this->em->getRepository(AnnouncementTutor::class)->findAnnouncementTutor($announcement, $currentUser);
            if (!$findTutor) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'Not allowed to see the message available for tutors or administrators',
                ]);
            }
            $findUserChat = $findTutor;
        }

        $query = $this->em->getRepository(Message::class)->getUserChatMessages($user, $findUserChat);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $query,
        ]);
    }

    /**
     * @Route("/admin/messages-student-tutor/user/{user}/announcement/{announcement}", name="messages-student-tutor", methods={"GET"})
     */
    public function messagesStudentToTutor(User $user, Announcement $announcement): Response
    {
        $data = [];
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);
            $tutor = $announcementTutor ? $announcementTutor->getTutor() : $announcement->getCreatedBy();

            $messagesStudent = $this->em->getRepository(Message::class)->findBy(['sender' => $user->getId(), 'recipient' => $tutor->getId()]);
            $messagesTutor = $this->em->getRepository(Message::class)->findBy(['sender' => $tutor->getId(), 'recipient' => $user->getId()]);

            $messages = array_merge($messagesStudent, $messagesTutor);

            $messagesStudentNoOpen = $this->em->getRepository(Message::class)->findBy([
                'sender' => $user->getId(),
                'recipient' => $tutor->getId(),
                'openAt' => null,
            ]);

            if ($messagesStudentNoOpen) {
                foreach ($messagesStudentNoOpen as $messageNoOpen) {
                    $messageNoOpen->markAsOpen();
                    $this->em->persist($messageNoOpen);
                }
                $this->em->flush();
            }

            foreach ($messages as $message) {
                $data[] = [
                    'id' => $message->getId(),
                    'body' => $message->getBody(),
                    'sender' => $message->getSender(),
                    'recipient' => $message->getRecipient(),
                    'sentAt' => $message->getSentAt(),
                    'openAt' => $message->getOpenAt(),
                ];
            }
            usort($data, function (array $elem1, $elem2) {
                return $elem1['id'] <=> $elem2['id'];
            });
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $data : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['list', 'messages', 'user_area']]);
    }

    /**
     * @Rest\Post("/messages/sent/tutor", name="admin_messages_sent")
     */
    public function sentMessage(Request $request): Response
    {
        $status = Response::HTTP_BAD_REQUEST;
        $error = true;

        $body = $request->get('form-body');
        $userId = $request->get('form-user-id');
        $idAnnouncement = $request->get('form-id-announcement');

        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);
        $sender = $announcementTutor ? $announcementTutor->getTutor() : $announcement->getCreatedBy();

        $subject = $announcement->getCourse()->getName();
        $recipient = $this->em->getRepository(User::class)->find($userId);

        if ($subject && $body) {
            $message = new Message();
            $message
                ->setSender($sender)
                ->setRecipient($recipient)
                ->setSentAt(new \DateTime())
                ->setSubject($subject)
                ->setBody($body);

            $this->em->persist($message);

            $this->em->flush();

            $status = Response::HTTP_OK;
            $error = false;
        }

        $messagesStudent = $this->em->getRepository(Message::class)->findBy(['sender' => $recipient->getId(), 'recipient' => $sender->getId()]);
        $messagesTutor = $this->em->getRepository(Message::class)->findBy(['sender' => $sender->getId(), 'recipient' => $recipient->getId()]);
        $messages = array_merge($messagesStudent, $messagesTutor);

        $data = [];
        foreach ($messages as $message) {
            $data[] = [
                'id' => $message->getId(),
                'body' => $message->getBody(),
                'sender' => $message->getSender(),
                'recipient' => $message->getRecipient(),
                'sentAt' => $message->getSentAt(),
                'openAt' => $message->getOpenAt(),
            ];
        }
        usort($data, function (array $elem1, $elem2) {
            return $elem1['id'] <=> $elem2['id'];
        });

        $response = [
            'status' => $status,
            'error' => $error,
            'data' => $data,
        ];

        return $this->sendResponse($response, ['groups' => ['list', 'messages']]);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/full-data", requirements={"id"="\d+"})
     */
    public function getAnnouncementFullData(Request $request, UserPasswordHasherInterface $userPasswordHasher, Announcement $announcement): Response
    {
        /** @var AnnouncementRepository $announcementRepository */
        $announcementRepository = $this->em->getRepository(Announcement::class);
        $course = $announcement->getCourse();
        $type = $course->getTypeCourse();

        $requestUri = $request->getRequestUri();
        $uri = $request->getUri();
        $baseUrl = str_replace($requestUri, '', $uri);

        /** @var AnnouncementCriteria[] $announcementCriteria */
        $announcementCriteria = $this->em->getRepository(AnnouncementCriteria::class)->getAnnouncementCriteria();

        $criteriaValues = $this->em->getRepository(AnnouncementAprovedCriteria::class)->getAnnouncementAprovedCriteria($announcement);
        $criteria = [];
        foreach ($announcementCriteria as $c) {
            $criteria[$c->getId()] = [
                'enabled' => false,
                'value' => 0,
            ];
        }

        foreach ($criteriaValues as $v) {
            $criteria[$v['criteriaId']]['enabled'] = true;
            $criteria[$v['criteriaId']]['value'] = $v['value'];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => [
                'id' => $announcement->getId(),
                'code' => $announcement->getCode(),
                'course' => [
                    'id' => $course->getId(),
                    'code' => $course->getCode(),
                    'image' => $course->getImage(),
                    'name' => $course->getName(),
                ],
                'startAt' => $announcement->getStartAt()->format('c'),
                'finishAt' => $announcement->getFinishAt()->format('c'),
                'totalHours' => $announcement->getTotalHours(),
                'usersPerGroup' => (int) $announcement->getUsersPerGroup(),
                'didacticGuide' => null, // Required to handle the file,
                'objectiveAndContent' => $announcement->getObjectiveAndContents(),
                'enableChapterTiming' => false,
                'enableAccessContent' => false,
                'chapterTiming' => [],
                'configAnnouncement' => [],

                'type' => $type ? $type->getType() : 'online',
                'students' => $announcementRepository->getAnnouncementStudentsWithGroup($announcement),

                'subsidized' => $announcement->getSubsidized(),
                'formativeActionType' => $announcement->getFormativeActionType(),
                'actionCode' => $announcement->getActionCode(),
                'denomination' => $announcement->getDenomination(),
                'contactPerson' => $announcement->getContactPerson(),
                'contactPersonEmail' => $announcement->getContactPersonEmail(),
                'contactPersonTelephone' => $announcement->getContactPersonTelephone(),

                'inspectorAccess' => $announcementRepository->generateAnnouncementUrl($userPasswordHasher, $this->getUser(), $announcement, $baseUrl),

                'enableChat' => false,
                'enableNotifications' => false,
                'enableSMS' => false,
                'enableForum' => false,
                'enableCertificate' => true,
                'typeDiploma' => null,
                'isConfirmationRequiredDiploma' => false,
                'typeSurvey' => null,

                'approvedCriteriaValues' => $criteria,
                'alertTypeTutorValues' => [],
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/tutors", requirements={"id"="\d+"})
     */
    public function getAnnouncementTutors(Announcement $announcement): Response
    {
        $tutors = $this->em->getRepository(AnnouncementTutor::class)->findAnnouncementTutors($announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $tutors,
        ]);
    }

    private function getTaskTutor($tasksAnnouncement, Announcement $announcement)
    {
        $isTutor = $this->shouldFilterByTutor($this->getUser());
        $groups = $this->announcementGroupService->getGroupByAnnouncement($announcement);

        $groupsTutor = [];
        foreach ($groups as $group) {
            $groupsTutor[] = $group['id'];
        }

        $tasks = [];
        foreach ($tasksAnnouncement as $t) {
            $tasksCourseGroup = $this->em->getRepository(TaskCourseGroup::class)->findBy(['taskCourse' => $t['id']], ['numGroup' => 'ASC']);

            $groupsName = [];
            $idsGroups = [];
            if ($tasksCourseGroup) {
                foreach ($tasksCourseGroup as $taskCourseGroup) {
                    $nameGroup = $this->announcementGroupService->generateGroupName($taskCourseGroup->getAnnouncementGroup()->getGroupNumber());

                    $groupsName[] = $nameGroup;
                    $idsGroups[] = $taskCourseGroup->getAnnouncementGroup()->getId();
                }
            } else {
                // If the task has no associated groups, set a default value
                $groupsName[] = 'No Group'; // You can set any default value here
            }

            $groupsNameImplode = implode(', ', $groupsName);

            // Check if the task has a group that matches a tutor's group or if it has no groups
            $taskMatchesTutorGroup = false;
            if (\count(array_intersect($idsGroups, $groupsTutor)) > 0 || $t['user_id'] === $this->getUser()->getId()) {
                $taskMatchesTutorGroup = true;
            }

            if ($isTutor && !$taskMatchesTutorGroup) {
                continue; // Skip this task if the tutor has no matching groups and the task has groups
            }

            $tasks[] = [
                'id' => $t['id'],
                'title' => $t['title'],
                'isVisible' => $t['isVisible'],
                'startDate' => $t['startDate'],
                'dateDelivery' => $t['dateDelivery'],
                'total_files' => $t['total_files'],
                'groups' => $groupsNameImplode,
                'idsGroups' => $idsGroups,
            ];
        }

        return $tasks;
    }

    private function shouldFilterByTutor($user): bool
    {
        return $user->isTutor() && !($user->isAdmin() || $user->isManager());
    }

    /**
     * @Rest\Route("/admin/announcement/{id}/called-users", methods={"GET", "POST"})
     */
    public function getAnnouncementUserCalled(Announcement $announcement): Response
    {
        $called = $this->em->getRepository(AnnouncementUser::class)->getAnnouncementUserCall($announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ['users' => $called],
        ]);
    }

    /**
     * @Rest\Route("/admin/announcement/{id}/paginated-called-users", methods={"GET"}, requirements={"id"="\d+"})
     */
    public function getAnnouncementUserCalledPaginated(Announcement $announcement): Response
    {
        $data = $this->em->getRepository(AnnouncementUser::class)->getUserCalledPaginated($announcement);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'items' => $data['data'],
                'total-items' => (int) $data['totalItems'],
            ],
        ]);
    }

    /**
     * @Rest\Route("/admin/announcement/{id}/available-users", methods={"GET","POST"})
     */
    public function findAnnouncementAvailableUsers(Request $request, Announcement $announcement): Response
    {
        $data = json_decode($request->getContent(), true);
        $filters = $data['filters'] ?? [];
        /** @var User $user */
        $user = $this->getUser();

        $userIds = [];
        $selectedUsersIds = $this->em->getRepository(AnnouncementUser::class)->getSelectedUsersIds($announcement);
        foreach ($selectedUsersIds as $selected) {
            $userIds[] = $selected['id'];
        }

        /** @var UserRepository $userRepository */
        $userRepository = $this->em->getRepository(User::class);
        $query = $userRepository->getFilteredUsers(
            $user,
            $filters,
            $userIds,
            $data['searchQuery'],
            true,
            User::ACTIVITY_STATUS_FILTER[User::ACTIVE_USERS],
        );

        $result = $userRepository->selectUserFilter($query);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $result,
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement/{id}/add-called-user/{user}")
     */
    public function addUserToAnnouncement(Announcement $announcement, User $user): Response
    {
        $total = $announcement->getCalled()->count();
        if ($total >= $announcement->getMaxUsers()) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.USERS.MAX_EXCEEDED',
                'i18n' => true,
            ]);
        }

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)
            ->findOneBy([
                'announcement' => $announcement,
                'user' => $user,
            ]);
        if (!$announcementUser) {
            $announcementUser = new AnnouncementUser();
            $announcementUser->setUser($user)
                ->setAnnouncement($announcement);
            $this->em->persist($announcementUser);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'user' => $this->em->getRepository(AnnouncementUser::class)->getAnnouncementUser($announcement, $user),
            ]);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => false,
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement/{id}/add-users")
     */
    public function addUsersToAnnouncement(Request $request, Announcement $announcement): Response
    {
        $data = json_decode($request->getContent(), true);
        if (empty($data['data'])) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'No data has been provided',
                'message' => 'No data has been provided',
            ]);
        }

        $currentUsers = $announcement->getCalled()->count();
        $maxUsers = $announcement->getMaxUsers();

        if ($currentUsers >= $maxUsers) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.USERS.MAX_EXCEEDED',
                'i18n' => true,
            ]);
        }

        if (\count($data['data']) < 1) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => false,
            ]);
        }

        $users = $this->em->getRepository(User::class)->getUsersToAnnouncement($data['data']);

        /** @var User $user */
        foreach ($users as $user) {
            if ($currentUsers >= $maxUsers) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'ANNOUNCEMENT.USERS.MAX_EXCEEDED',
                    'i18n' => true,
                ]);
            }

            $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
                'announcement' => $announcement,
                'user' => $user,
            ]);
            if (!$announcementUser) {
                $announcementUser = new AnnouncementUser();
                $announcementUser->setUser($user)
                    ->setAnnouncement($announcement);
                $announcement->addCalled($announcementUser);
                ++$currentUsers;
            }
        }

        $this->em->persist($announcement);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Route("/admin/announcement/{id}/remove-users", methods={"POST", "DELETE"})
     */
    public function removeCalledUsers(Request $request, Announcement $announcement): Response
    {
        $called = $announcement->getCalled();
        foreach ($called as $c) {
            $this->em->remove($c);
        }

        $announcement->setCalled(new ArrayCollection());
        $this->em->persist($announcement);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => 'Deleted',
            'data' => 'Deleted',
        ]);
    }

    /**
     * @Rest\Delete("/admin/announcement/{id}/remove-called-user/{user}")
     */
    public function removeUserFromAnnouncement(Announcement $announcement, User $user): Response
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $user,
        ]);

        if ($announcementUser) {
            $this->em->remove($announcementUser);
            $this->em->flush();
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    private function saveAnnouncementMaterialCourse(Announcement $announcement, UploadedFile $file, $type)
    {
        $materialCourse = new MaterialCourse();
        $materialCourse->setAnnouncement($announcement)
            ->setFilename($file->getClientOriginalName())
            ->setName($file->getClientOriginalName())
            ->setFilenameFile($file)
            ->setCourse($announcement->getCourse())
            ->setTypeMaterial($type)
            ->setIsDownload(true)
            ->setIsVisible(\in_array($type, [1, 2, 4]))
            ->setIsActive(true);
        $this->em->persist($materialCourse);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/materials")
     */
    public function getAnnouncementMaterials(Request $request, Announcement $announcement): Response
    {
        $materials = $this->em->getRepository(MaterialCourse::class)->findAnnouncementMaterials($announcement);

        $dataMaterials = [];

        foreach ($materials as $material) {
            $dataMaterials[] = [
                'id' => $material['id'],
                'name' => $material['name'],
                'isDownload' => $material['isDownload'],
                'mimeType' => $material['mimeType'],
                'createdAt' => $material['createdAt'],
                'type' => $material['typeMaterial'],
                'filename' => $material['filename'],
                'isVisible' => $material['isVisible'],
                'isActive' => $material['isActive'],
                'typeOrigen' => $material['type'],
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $dataMaterials,
            'basePath' => $this->settings->get('app.material_course_path'),
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement/{id}/upload-materials")
     */
    public function uploadAnnouncementMaterials(Request $request, Announcement $announcement, VimeoService $vimeoService): Response
    {
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
        $filesLength = $request->get('filesLength');
        $type = $request->get('type');
        if (2 === $type) {
            /** @var UploadedFile|null $file */
            $file = $request->files->get('file');
            if (!$file) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'FILE_UPLOAD.FILE.NOT_PROVIDED',
                ]);
            }
            // Is a video, only one is allowed
            $result = $vimeoService->upload($file);
            if (Response::HTTP_OK !== $result['status']) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'FILE_UPLOAD.VIDEO.FAILED',
                ]);
            }
            $materialCourse = new MaterialCourse();
            $materialCourse->setFilename($file->getClientOriginalName())
                ->setName($file->getClientOriginalName())
                ->setAnnouncement($announcement)
                ->setCourse($announcement->getCourse())
                ->setMimeType($file->getMimeType())
                ->setTypeMaterial('2')
                ->setUrlMaterial($result['link'])
                ->setType(MaterialsCourseEnum::MATERIALS_ANNOUNCEMENT)
                ->setIsDownload(false)
                ->setIsVisible(true)
                ->setIsActive(true);

            $this->em->persist($materialCourse);
        } else {
            if (!empty($filesLength) && $filesLength > 1) {
                for ($i = 0; $i < $filesLength; ++$i) {
                    $file = $request->files->get("file_$i");
                    $this->saveAnnouncementMaterialCourse($announcement, $file, $request->get('type'));
                }
            } else {
                $file = $request->files->get('file');
                if (!$file) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_OK,
                        'error' => true,
                        'data' => 'FILE_UPLOAD.FILE.NOT_PROVIDED',
                    ]);
                }
                $this->saveAnnouncementMaterialCourse($announcement, $file, $request->get('type'));
            }
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ]);
    }

    /**
     * @Rest\Post("/admin/announcement/{id}/task")
     */
    public function saveNewAnnouncementTask(Request $request, Announcement $announcement): Response
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $this->announcementAuthorizationService->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);

            $task = new TaskCourse();

            $task->setAnnouncement($announcement);
            $result = $this->setAnnouncementTaskData($request, $task);
            if ($result instanceof Response) {
                return $result;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => [
                    'id' => $task->getId(),
                    'announcementId' => $announcement->getId(),
                ],
            ]);
        } catch (UserNotAuthorizedException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_FORBIDDEN,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/admin/announcement/{announcement}/task/{task}")
     */
    public function updateAnnouncementTask(Request $request, TaskCourse $task): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $task);

        try {
            $result = $this->setAnnouncementTaskData($request, $task);
            if ($result instanceof Response) {
                return $result;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'id' => $task->getId(),
                    'announcementId' => $task->getAnnouncement()->getId(),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @return true|Response
     */
    private function setAnnouncementTaskData(Request $request, TaskCourse $task)
    {
        try {
            $title = $request->get('title');
            $description = $request->get('description');
            $startAt = $request->get('start_date');
            $deadline = $request->get('deadline');
            $groups = json_decode($request->get('groups', '[]'), true);
            $visible = filter_var($request->get('visible', false), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);

            if (empty($title)) {
                return $this->sendResponse(['status' => Response::HTTP_OK, 'error' => true, 'data' => 'ERROR.TITLE_REQUIRED']);
            }
            if (empty($description)) {
                return $this->sendResponse(['status' => Response::HTTP_OK, 'error' => true, 'data' => 'ERROR.DESCRIPTION_REQUIRED']);
            }
            if (empty($startAt)) {
                return $this->sendResponse(['status' => Response::HTTP_OK, 'error' => true, 'data' => 'ERROR.START_AT_REQUIRED']);
            }
            if (empty($deadline)) {
                return $this->sendResponse(['status' => Response::HTTP_OK, 'error' => true, 'data' => 'ERROR.DEADLINE_REQUIRED']);
            }

            $announcement = $task->getAnnouncement();
            $timezone = $announcement->getTimezone();
            if (empty($timezone)) {
                // No timezone defined in announcement, use default datetime creator
                $dtStart = new \DateTime($startAt);
                $dtDeadline = new \DateTime($deadline);
            } else {
                $tz = new \DateTimeZone('UTC');
                $dtStart = new \DateTime($startAt, $tz);
                $dtDeadline = new \DateTime($deadline, $tz);
            }

            $task->setTitle($title)
                ->setDescription($description)
                ->setStartDate($dtStart)
                ->setDateDelivery($dtDeadline)
                ->setIsVisible($visible)
                ->setCourse($task->getAnnouncement()->getCourse());

            $this->setTaskCourseGroup($groups, $task);
            $this->deleteTaskCourseGroup($groups, $task);

            $this->em->persist($task);
            $this->em->flush();

            return true;
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    private function setTaskCourseGroup($groups, TaskCourse $task)
    {
        if (!empty($groups)) {
            foreach ($groups as $group) {
                $groupTask = $this->em->getRepository(TaskCourseGroup::class)->findOneBy(['taskCourse' => $task, 'announcementGroup' => $group['id']]);
                $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($group['id']);

                if (!$groupTask) {
                    $groupTask = new TaskCourseGroup();
                }

                $groupTask->setTaskCourse($task)
                    ->setAnnouncementGroup($announcementGroup)
                    ->setNumGroup($group['numGroup'] ?? 1);

                $this->em->persist($groupTask);
            }
        }
    }

    private function deleteTaskCourseGroup($groups, TaskCourse $task)
    {
        $groupsTask = $this->em->getRepository(TaskCourseGroup::class)->findBy(['taskCourse' => $task]);

        foreach ($groupsTask as $groupTask) {
            $found = false;
            foreach ($groups as $group) {
                if ($groupTask->getAnnouncementGroup()->getId() === $group['id']) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $this->em->remove($groupTask);
            }
        }
    }

    /**
     * @Rest\Get("/admin/announcement/{announcement}/task/{task}")
     */
    public function getAnnouncementTask(Announcement $announcement, TaskCourse $task): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'title' => $task->getTitle(),
                'description' => $task->getDescription(),
                'startAt' => $task->getStartDate()->format('c'),
                'deadline' => $task->getDateDelivery()->format('c'),
                'visible' => $task->isIsVisible(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/opinions", name="admin_api_announcement_opinions", requirements={"id"="\d+"})
     */
    public function getAnnouncementOpinions(Announcement $announcement): Response
    {
        $opinions = $this->em->getRepository(Nps::class)->getAnnouncementOpinions($announcement);
        $opinions = $this->NpsExtraAnswerUserService->addExtraToItems($opinions);

        $data = [];
        foreach ($opinions as $opinion) {
            $announcement = $this->em->getRepository(Announcement::class)->find($opinion['announcement_id']);
            $opinion['courseName'] = $announcement->getCourse()->getName();

            $data[] = $opinion;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'items' => $data,
                'hideEmptyOpinions' => $this->settings->get('app.survey.hide.empty_comment'),
            ],
        ], ['datetime_format' => 'c']);
    }

    /**
     * @Rest\Post("/admin/announcement/{announcement}/send-message/{recipient}")
     */
    public function sendChatMessage(Request $request, Announcement $announcement, User $recipient): Response
    {
        $messageData = null;
        $replyTo = null;

        if (false !== strpos($request->getContentType(), 'json')) {
            $data = json_decode($request->getContent(), true);
            $messageData = empty($data['message']) ? null : $data['message'];
            $replyTo = empty($data['replyTo']) ? null : $data['replyTo'];
        } elseif (false !== strpos($request->getContentType(), 'form')) {
            $messageData = $request->get('message');
            $replyTo = $request->get('replyTo');
        }

        if (empty($messageData)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Message required',
            ]);
        }
        /** @var User $user */
        $user = $this->getUser();

        $message = new Message();
        $message->setSender($user)
            ->setRecipient($recipient)
            ->setSentAt(new \DateTime())
            ->setSubject($announcement->getCourse()->getName())
            ->setBody($messageData);

        $reply = null;
        if (!empty($replyTo) && ($reply = $this->em->getRepository(Message::class)->find($replyTo))) {
            $message->setReplyTo($reply);
        }

        $this->em->persist($message);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'id' => $message->getId(),
                'recipient_id' => $recipient->getId(),
                'parent_id' => $reply ? $reply->getId() : null,
                'subject' => $message->getSubject(),
                'message' => $message->getBody(),
                'createdAt' => $message->getSentAt(),
                'user_id' => $user->getId(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'email' => $user->getEmail(),
                'avatar' => $user->getAvatar(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/announcement/{announcement}/diploma/{user}", requirements={"announcement"="\d+","user"="\d+"})
     */
    public function announcementUserDiploma(Announcement $announcement, User $user, GenerateDiplomaUserService $diplomaUserService): Response
    {
        return $diplomaUserService->generateDiplomaAnnouncement($announcement, $user);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/course-info")
     */
    public function getAnnouncementCourseChapters(Announcement $announcement): Response
    {
        $course = $announcement->getCourse();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(Chapter::class)->getAnnouncementCourseChapters($course),
        ]);
    }

    /**
     * @Rest\Get("/admin/announcement/{id}/user-info/{user}")
     */
    public function getAnnouncementUserCourseInfo(Announcement $announcement, User $user): Response
    {
        $userChapters = [];
        $sumeTimeDays = [];
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy(['announcement' => $announcement, 'user' => $user]);
        if ($userCourse) {
            $userChapters = $this->em->getRepository(UserCourseChapter::class)->getUserChapters($userCourse);
            foreach ($userChapters as $chapter) {
                if (\array_key_exists(date_format($chapter['startedAt'], 'Y-m-d'), $sumeTimeDays)) {
                    $sumeTimeDays[date_format($chapter['startedAt'], 'Y-m-d')] += (int) $chapter['timeSpent'];
                } else {
                    $sumeTimeDays[date_format($chapter['startedAt'], 'Y-m-d')] = (int) $chapter['timeSpent'];
                }
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'user' => [
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'avatar' => $user->getAvatar(),
                    'announcementUserId' => $announcementUser->getId(),
                ],
                'notified' => $announcementUser->getNotified(),
                'userCourse' => [
                    'started' => $userCourse ? $userCourse->getStartedAt() : null,
                    'finished' => $userCourse ? $userCourse->getFinishedAt() : null,
                ],
                'timeByDay' => $sumeTimeDays,
                'userChapters' => $userChapters,
                'messages' => [],
                'resultsTypes' => [Games::QUIZ_TYPE, Games::WHEEL_TYPE, Games::PUZZLE_TYPE],
            ],
        ]);
    }
}
