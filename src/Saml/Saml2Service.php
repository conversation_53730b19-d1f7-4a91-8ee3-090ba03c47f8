<?php

namespace App\Saml;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use App\Entity\UserExtra;
use App\Service\FilesManager\FilesManagerService;
use App\Service\SettingsService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use LightSaml\Binding\BindingFactory;
use LightSaml\Builder\EntityDescriptor\SimpleEntityDescriptorBuilder;
use LightSaml\Context\Profile\MessageContext;
use LightSaml\Credential\KeyHelper;
use LightSaml\Credential\X509Certificate;
use LightSaml\Helper;
use LightSaml\Model\Assertion\Attribute;
use LightSaml\Model\Assertion\Issuer;
use LightSaml\Model\Assertion\NameID;
use LightSaml\Model\Context\DeserializationContext;
use LightSaml\Model\Context\SerializationContext;
use LightSaml\Model\Metadata\AssertionConsumerService;
use LightSaml\Model\Metadata\EntityDescriptor;
use LightSaml\Model\Metadata\KeyDescriptor;
use LightSaml\Model\Metadata\SpSsoDescriptor;
use LightSaml\Model\Protocol\AuthnRequest;
use LightSaml\Model\Protocol\LogoutRequest;
use LightSaml\Model\Protocol\LogoutResponse;
use LightSaml\Model\Protocol\Response;
use LightSaml\Model\XmlDSig\Signature;
use LightSaml\Model\XmlDSig\SignatureWriter;
use LightSaml\Model\XmlDSig\SignatureXmlReader;
use LightSaml\SamlConstants;
use Psr\Log\LoggerInterface;
use RobRichards\XMLSecLibs\XMLSecurityDSig;
use RobRichards\XMLSecLibs\XMLSecurityKey;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Twig\Environment;

/**
 * Main Service for handling authentication using SAML Protocol
 * Tested with Azure Active Directory
 * Terminology: Properties: Object properties, Attributes: SAML Attributes
 */
class Saml2Service
{
    public const ID = "ID";
    public const NAME_ID = 'nameId'; // Currently logged user for AD logout
    public const SESSION_INDEX = "SessionIndex";

    private EntityManagerInterface $em;
    private ParameterBagInterface $params;
    private LoggerInterface $logger;
    private RequestStack $requestStack;
    private UserPasswordHasherInterface $userPasswordHasher;
    private TokenStorageInterface $tokenStorage;
    private SettingsService $settings;
    private string $baseRootDirectory;
    private string $certsDirectory;
    private Environment $twig;
    private FilesManagerService $filesManagerService;
    private string $ssoLogsDir;


    public function __construct(
        EntityManagerInterface      $em,
        ParameterBagInterface       $params,
        LoggerInterface             $logger,
        RequestStack                $requestStack,
        UserPasswordHasherInterface $userPasswordHasher,
        TokenStorageInterface       $tokenStorage,
        KernelInterface             $kernel,
        SettingsService             $settings,
        Environment                 $twig,
        FilesManagerService $filesManagerService
    )
    {
        $this->em = $em;
        $this->params = $params;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->userPasswordHasher = $userPasswordHasher;
        $this->tokenStorage = $tokenStorage;
        $this->baseRootDirectory = $kernel->getProjectDir();
        $this->settings = $settings;
        $this->twig = $twig;
        $this->certsDirectory = $this->baseRootDirectory . DIRECTORY_SEPARATOR . 'certs';
        $this->filesManagerService = $filesManagerService;
        $this->ssoLogsDir = $this->baseRootDirectory . DIRECTORY_SEPARATOR . 'var' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . 'sso';
        if (!file_exists($this->ssoLogsDir)) mkdir($this->ssoLogsDir);
        $this->ssoLogsDir .= DIRECTORY_SEPARATOR;
    }

    /**
     * Generate a SAML AuthnRequest: A message sent to the IdP to initiate authentication
     * @param Request $request
     * @param string $consumeEndpoint // https://example.com/endpoint needs to be declared as reply url in Azure
     * @return string
     */
    public function makeLoginRequest(\Symfony\Component\HttpFoundation\Request $request, string $consumeEndpoint = "/saml2/consume"): string
    {
        // Get consumer service url
        $requestUri = $request->getRequestUri();                // return path, e.g /saml2/sso
        $uri = $request->getUri();                              // return full uri, e.g https://example.com/saml2/sso
        $baseUrl = str_replace($requestUri, '', $uri);  // get base uri, e.g https://example.com

        $destination = $this->settings->get('saml.destination') ?? '';
        $issuer = $this->settings->get('saml.issuer') ?? '';
        if (empty($destination)) throw new \RuntimeException("Value 'destination' not set");
        if (empty($issuer)) throw new \RuntimeException("Value 'issuer' not set");

        $authnRequest = new AuthnRequest();
        $authnRequest
            ->setAssertionConsumerServiceURL($baseUrl . $consumeEndpoint)
            ->setProtocolBinding(SamlConstants::BINDING_SAML2_HTTP_POST)
            ->setID(Helper::generateID())
            ->setIssueInstant(new \DateTime())
            ->setDestination($destination)
            ->setIssuer(new Issuer($issuer))
        ;

        $validateSignature = $this->settings->get('saml.validate_signature');
        if ($validateSignature) {
            $certificate = X509Certificate::fromFile($this->getSigningCertificatePath());
            $privateKey = KeyHelper::createPrivateKey($this->getPrivateKeyPath(), '', true, XMLSecurityKey::RSA_SHA256);
            $signature = new SignatureWriter($certificate, $privateKey, XMLSecurityDSig::SHA256);
            $authnRequest->setSignature($signature);
        }

        $bindingFactory = new BindingFactory();
        $redirectBinding = $bindingFactory->create(SamlConstants::BINDING_SAML2_HTTP_POST);
        $messageContext = new MessageContext();
        $messageContext->setMessage($authnRequest);

//        $serializationContext = new SerializationContext();
//        $authnRequest->serialize($serializationContext->getDocument(), $serializationContext);
//        file_put_contents($this->certsDirectory . DIRECTORY_SEPARATOR . 'test.xml', $serializationContext->getDocument()->saveXML());

        $response = $redirectBinding->send($messageContext);
        return $response->getContent();
    }

    /**
     * @param array $params
     * @return false|string
     */
    public function logout(array $params = []) {
        // Get nameId of user to be logout
        $sNameId = $params[self::NAME_ID] ?? null;
        if (empty($sNameId)) return null;                               // Exit. No user info has been stored

        $sessionIndex = $params[self::SESSION_INDEX] ?? null;
        $id = $params[self::ID] ?? null;

        $nameId = new NameID($sNameId);

        $samlEnabled = $this->settings->get('saml.enabled') ?? false;
        $logoutUrl = $this->settings->get('saml.logout_url') ?? '';
        if (!$samlEnabled || empty($logoutUrl)) return null;// No url, avoid calling logout
        $issuer = $this->settings->get('saml.issuer') ?? '';

        $logoutRequest = new LogoutRequest();
        $logoutRequest->setID($id)
            ->setDestination($logoutUrl)
            ->setIssuer(new Issuer($issuer))
            ->setIssueInstant(new \DateTime())
            ->setNameID($nameId)
        ;
        if (!empty($sessionIndex)) {
            $logoutRequest->setSessionIndex($sessionIndex);
        }

        $bindingFactory = new BindingFactory();
        $postBinding = $bindingFactory->create(SamlConstants::BINDING_SAML2_HTTP_POST);
        $messageContext = new MessageContext();
        $messageContext->setMessage($logoutRequest);

        $response = $postBinding->send($messageContext);
        return $response->getContent();
    }

    public function processLogoutResponse(Request $request)
    {
        $samlResponse = $request->get('SAMLResponse');
//        $samlSigAlg = $request->get('SigAlg');
//        $samlSignature = $request->get('Signature');
//        $deflated = gzinflate(base64_decode($samlResponse));
//        $this->logger->error('Test', [$samlResponse]);
//        $this->logger->error('deflated', [$deflated]);
        if (empty($samlResponse)) {
            return new SymfonyResponse(
                'No SAMLResponse',
                400
            );
        }


        $xmlContent = gzinflate(base64_decode($samlResponse));
        $env = $_ENV['APP_ENV'];
        if ($env === 'test' || $env === 'dev') {
            file_put_contents($this->ssoLogsDir . 'saml-logout-response.xml', $xmlContent);
        }
        $deserializationContext = new DeserializationContext();
        $deserializationContext->getDocument()->loadXML($xmlContent);
        $logoutResponse = new LogoutResponse();
        $logoutResponse->deserialize($deserializationContext->getDocument()->firstChild, $deserializationContext);
        return $logoutResponse->getStatus()->getStatusCode()->getValue() != 'urn:oasis:names:tc:SAML:2.0:status:Success';
    }

    private function verifySignature(Signature $signature): bool
    {
        try {
            if (!file_exists($this->getIdpSigningCertificatePath())) {
                return false;
            }
            $key = KeyHelper::createPublicKey(X509Certificate::fromFile($this->getIdpSigningCertificatePath()));
            return $signature->validate($key);
        } catch (\Exception $e) {
            $this->logger->error('Failed to verify signature', [$e->getTrace()]);
            return false;
        }
    }

    /**
     * @param Request $request
     * @return SymfonyResponse|UserContainer
     * @throws \Exception
     */
    public function processResponse(\Symfony\Component\HttpFoundation\Request $request) {
        $samlResponse = $request->get('SAMLResponse');
        if (empty($samlResponse)) {
            return new SymfonyResponse(
                'No SAMLResponse',
                400
            );
        }

        $xmlContent = base64_decode($samlResponse);
        $env = $_ENV['APP_ENV'];
        if ($env === 'test' || $env === 'dev') {
            file_put_contents($this->ssoLogsDir . 'saml-login-response.xml', $xmlContent);
        }
        $deserializationContext = new DeserializationContext();
        $deserializationContext->getDocument()->loadXML($xmlContent);

        $authnResponse = new Response();
        $authnResponse->deserialize($deserializationContext->getDocument()->firstChild, $deserializationContext);

        $status = $authnResponse->getStatus(); // <samlp:Status></samlp:Status>
        if (!$status) {
            $this->logger->error( self::class . 'No status in samlp:Response');
            return new SymfonyResponse(
                'No status',
                400,
                [
                    'Content-Type' => 'application/json'
                ]
            );
        }

        if ($status->getStatusCode()->getValue() != 'urn:oasis:names:tc:SAML:2.0:status:Success') {
            $this->logger->error(self::class . ': Unauthorized');
            return new SymfonyResponse(
                'Unauthorized',
                401,
                [
                    'Content-Type' => 'application/json'
                ]
            );
        }

        $assertion = $authnResponse->getAllAssertions(); // <Assertion></Assertion>
        if (count($assertion) === 0) {
            $this->logger->error(self::class . ': No Assertion Data');
            return new SymfonyResponse(
                'No Assertion data',
                400,
                [
                    'Content-Type' => 'application/json'
                ]
            );
        }

        $issuer = $assertion[0]->getIssuer();
        /** @var ?Signature $signature */
        $signature = $assertion[0]->getSignature();
        $validateSignature = $this->settings->get('saml.validate_signature');

        if ($validateSignature) {
            if (!$this->verifySignature($signature)) {
                $this->logger->error(self::class . ': Signature is not valid');
                return new SymfonyResponse(
                    'Signature is not valid',
                    400,
                    [
                        'Content-Type' => 'application/json'
                    ]
                );
            }
        }

        $subject = $assertion[0]->getSubject();
        $conditions = $assertion[0]->getConditions();

        /**
         * Check all attributes and update User model
         */
        $attributesStatements = $assertion[0]->getAllAttributeStatements();
        if (count($attributesStatements) == 0) {
            $this->logger->error(self::class. ': No Attributes');
            return new SymfonyResponse(
                'No attributes',
                400,
                [
                    'Content-Type' => 'application/json'
                ]
            );
        }

        $authnStatement = $assertion[0]->getFirstAuthnStatement();

//        $user = $this->setUserData($attributesStatements[0]->getAllAttributes());
        $user = $this->applyAttributes($attributesStatements[0]->getAllAttributes());
        if ($user instanceof SymfonyResponse) return $user;
        if (!$user) return new SymfonyResponse(
            'Failed to process user',
            400,
            [
                'Content-Type' => 'application/json'
            ]
        );

        $userContainer = new UserContainer($user);
        $userContainer
            ->setId($authnResponse->getID())
            ->setIssuer($issuer)
            ->setSubject($subject)
            ->setSignature($signature)
            ->setConditions($conditions)
            ->setAuthnStatement($authnStatement)
        ;

        return $userContainer;
    }

    /**
     * Generate session token for backend usage
     * @param UserContainer $userContainer Result from processResponse
     * @return void
     */
    public function generateLocalSessionToken(UserContainer $userContainer): void {
//        $nameId = $userContainer->getSubject()->getNameID();
        $session = $this->requestStack->getSession();
//        $session->set(self::NAME_ID, $nameId);
        $token = new UsernamePasswordToken($userContainer->getUser(), null, 'main', $userContainer->getUser()->getRoles());
        $this->tokenStorage->setToken($token);
        $this->requestStack->getSession()->set('_security_main', serialize($token));
    }

    /**
     * @param UserContainer $userContainer
     * @param UserTokenInterface $userToken
     * @return void
     */
    public function generateApiSessionToken(UserContainer $userContainer, UserTokenInterface $userToken): void
    {
        $token = (new \DateTime())->getTimestamp() . $userContainer->getUser()->getId() . $userContainer->getUser()->getEmail();
        $userToken->setToken(hash('sha256', $token))
            ->setValidUntil((new \DateTimeImmutable())->modify($this->params->get('saml.token_ttl') . ' seconds'))
        ;
        $subject = $userContainer->getSubject();
        $extra = [
            self::ID => $userContainer->getId()
        ];
        if ($subject && ($nameId = $subject->getNameID())) {
            $extra[self::NAME_ID] = $nameId->getValue();
        }

        $authnStatement = $userContainer->getAuthnStatement();
        if ($authnStatement && ($sessionIndex = $authnStatement->getSessionIndex())) {
            $extra[self::SESSION_INDEX] = $sessionIndex;
        }

        $userToken->setExtra($extra);
    }

    /**
     * @param Attribute[] $attributes
     * @return void
     */
    private function attributesToArray(array $attributes): array {
        $attributesAsArray = [];
        foreach ($attributes as $attribute) {
            $name = $attribute->getName();
            $value = $attribute->getFirstAttributeValue();

            /**
             * Separate the attribute full namespace to get attribute name
             * ex. http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress -> emailaddress
             */
            $nameArray = explode('/', $name);
            $attributeName = $nameArray[count($nameArray) - 1];

            $attributesAsArray[$attributeName] = $value;
        }

        return $attributesAsArray;
    }

    /**
     * @param array $indexedAttributes
     * @return User|SymfonyResponse
     * @throws \Random\RandomException
     */
    private function handleBasicUserInfo(array $indexedAttributes)
    {
        $primaryKeyProperty = $this->settings->get('saml.user.primaryKey');

        /**
         * Required attributes
         * [
         *  'property' => 'attribute'
         * ]
         * Where property is the field in User table
         * attribute is the response from saml request
         */
        $userRequiredAttributes = $this->settings->get('saml.user.required_attributes');
        if (!is_array($userRequiredAttributes)) $userRequiredAttributes = $userRequiredAttributes ? json_decode($userRequiredAttributes, true) : [];

        /**
         * Validate all required properties: email, firstName, lastName, code
         */
        $defaultAndRequiredFields = $this->params->get('saml.user.default_and_required');

        $failed = false;
        foreach ($defaultAndRequiredFields as $reqProperty => $reqAttribute)
        {
            $reqAttributeValue = $indexedAttributes[$reqAttribute] ?? null;
            if ($reqProperty === $primaryKeyProperty && empty($reqAttributeValue))
            {
                $failed = true;
                break;
            }
            if ($reqProperty === 'code' || $reqProperty === 'email') continue;// Not required to validate
            if (empty($reqAttributeValue))
            {
                $failed = true;
                break;
            }
        }

        if ($failed)
        {
            $defaultAndRequiredFieldsTemp = [];
            foreach ($defaultAndRequiredFields as $prop => $attr)
            {
                if ($primaryKeyProperty === 'code' && $prop === 'email') continue;
                if ($primaryKeyProperty === 'email' && $prop === 'code') continue;
                $defaultAndRequiredFieldsTemp[] = $attr;
            }

            $content = $this->twig->render('saml_error.html.twig', [
                'code' => SymfonyResponse::HTTP_UNAUTHORIZED,
                'locale' => $this->params->get('app.defaultLanguage'),
                'failedFields' => $defaultAndRequiredFieldsTemp,
                'message' => 'No puedes iniciar sesión porque tu usuario de SSO no tiene asignado ningún valor en alguno de estos atributos obligatorios:'
            ]);

            return new SymfonyResponse($content, 401);
        }



        $primaryKeyAttributeName = $userRequiredAttributes[$primaryKeyProperty] ?? null;// Used to identify the user
        $primaryKeyAttributeValue = $indexedAttributes[$primaryKeyAttributeName] ?? null;// Get the attribute value
        if (empty($primaryKeyAttributeValue))
            throw new \Exception("No value provided for required attribute: " . $primaryKeyAttributeName);


        /**
         * Cannot log in because one of the following required attributes: givenname, lastname, [code|email]
         * Please contact your company department
         */

        /** @var User $user */
        $user = $this->em->getRepository(User::class)->findOneBy([
            "" . $primaryKeyProperty => $primaryKeyAttributeValue
        ]);

        if (!$user)
        {
            $method = 'set' . ucwords($primaryKeyProperty);
            $user = new User();
            $password = bin2hex(random_bytes(10));

            $user->setIsActive(true)
                ->setOpen(true)
                ->setValidated(true)
                ->setRoles([User::ROLE_USER])
                ->setPassword($this->userPasswordHasher->hashPassword($user, $password))
            ;
            $user->{$method}($primaryKeyAttributeValue);

            $emailTemp = $indexedAttributes[$userRequiredAttributes['email']] ?? null;
            if ($primaryKeyProperty === 'code' && !empty($emailTemp))
            {
                $otherUser = $this->em->getRepository(User::class)->findOneBy(['email' => $emailTemp]);
                if ($otherUser)
                {
                    // A user with the current email already exists
                    $otherUser->setEmail('DELETED_' . $emailTemp);
                    $this->em->persist($otherUser);
                    $this->em->flush();
                }
            }
        }

        foreach ($userRequiredAttributes as $property => $attributeName)
        {
            $attributeValue = $indexedAttributes[$attributeName] ?? null;
            if ($primaryKeyProperty === 'code' && $property === 'email' && empty($attributeValue))
            {
                // No email provided, generate an email
                $attributeValue = 'user' . $primaryKeyAttributeValue . '@user.com';
            }

            $method = 'set' . ucwords($property);
            if (method_exists($user, $method))
                $user->{$method}($attributeValue);
//            throw new \Exception('MethodName: ' . $method);
        }

        /**
         * Optional attributes
         */
        $this->em->persist($user);
        $this->em->flush();
        return $user;
    }

    public function handleUserRoleInfo(User $user, array $indexedAttributes)
    {
        $enabled = filter_var($this->settings->get('saml.user.role.enable'), FILTER_VALIDATE_BOOLEAN);
        if (!$enabled) return;
        $attribute = $this->settings->get('saml.user.role.attribute') ?? '';
        $attributeValue = $indexedAttributes[$attribute] ?? '';
        if (empty($attributeValue))
        {
            // Clean remote roles
            $user->setRemoteRoles([]);
            $this->em->persist($user);
            $this->em->flush();
            return;
        }

        $rolesConfiguration = $this->settings->get('saml.user.role.roles') ?? [];
        if (!is_array($rolesConfiguration)) $rolesConfiguration = $rolesConfiguration ? json_decode($rolesConfiguration, true) : [];
        $rolesToAdd = [];
        foreach ($rolesConfiguration as $role => $conf)
        {
            if (in_array($attributeValue, $conf)) $rolesToAdd[] = $role;
        }
        $user->setRemoteRoles($rolesToAdd);
        $this->em->persist($user);
        $this->em->flush();
    }

    public function handleFilterInfo(User $user, array $indexedAttributes): User
    {
        $filterAttributes = $this->settings->get('saml.user.filters');
        if (!is_array($filterAttributes)) $filterAttributes = $filterAttributes ? json_decode($filterAttributes, true) : [];// Force as array

        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAll();
        $filterCategoriesIndexed = [];
        foreach ($filterCategories as $category) $filterCategoriesIndexed[$category->getId()] = $category;
        $filtersToAdd = [];

//        $excludedFilters = $user->getCustomFilters()['excluded'] ?? [];


        /**
         * @var string $attribute
        * @var int[] $categoryIds */
        foreach ($filterAttributes as $attribute => $categoryIds)
        {
            $filterName = $indexedAttributes[$attribute] ?? null;
            if (empty($filterName)) continue;
//        throw new \Exception($attribute);
            foreach ($categoryIds as $categoryId)
            {
//                $assignedFilters = $user->getCustomFilters()['assigned'] ?? [];

                $filter = $this->em->getRepository(Filter::class)->findOneBy([
                    'name' => $filterName,
                    'filterCategory' => $filterCategoriesIndexed[$categoryId]
                ]);
                if (!$filter)
                {
                    $filter = new Filter();
                    $filter->setName($filterName)
                        ->setCode($filterName)
                        ->setSource(Filter::SOURCE_REMOTE)
                        ->setFilterCategory($filterCategoriesIndexed[$categoryId])
                    ;
                    $this->em->persist($filter);
                }

//                if (in_array($filter->getId(), $excludedFilters))
//                {
//                    // Filter has been excluded, avoid adding to user filter list
//                    continue;
//                }

                $filtersToAdd[] = $filter;
//                if (in_array($filter->getId(), $assignedFilters))
//                    $user->removeCustomFilter($filter->getId());
            }
        }
        $this->em->flush();// Save all filters

        $currentFilters = $user->getFilter()->toArray();
        // Get local filters
        $localFilters = [];
        /** @var Filter $f */
        foreach ($currentFilters as $f)
        {
            if ($f->getSource() !== Filter::SOURCE_REMOTE) $localFilters[] = $f;
        }

        // Merge local filters and replace all remote filters
        $user->setFilters(array_merge($localFilters, $filtersToAdd));
        $this->em->persist($user);
        $this->em->flush();

//        throw new \Exception('Filters saved');

        return $user;
    }

    public const AVAILABLE_EXTRA_CLASS = [
        'UserExtra'
    ];
    public function handleAdditionalData(User $user, array $indexedAttributes)
    {
        $extraData = $this->settings->get('saml.user.optional_attributes') ?? [];
        if (!is_array($extraData)) $extraData = $extraData ? json_decode($extraData, true) : [];
        if (count($extraData) < 1) return;

        foreach ($extraData as $className => $values)
        {
            $object = null;
            switch ($className)
            {
                case 'userExtraAttributes':
                    $userExtra = $this->em->getRepository(UserExtra::class)->findOneBy(['user' => $user]);
                    if (!$userExtra)
                    {
                        $professionalCategory = $this->em->getRepository(ProfessionalCategory::class)->findAll();
                        $userExtra = new UserExtra();
                        $userExtra->setUser($user)
                            ->setCategory($professionalCategory[0]);
                    }
                    $object = $userExtra;
                    break;
            }

            if ($object == null) continue;// A valid object not assigned. Avoid calling methods on null

            /**
             * @var string $property Property in object
             * @var string $attributeName Attribute in saml response
             */
            foreach ($values as $property => $attributeName)
            {
                $attributeValue = $indexedAttributes[$attributeName] ?? null;
                if (empty($attributeValue)) continue;
                $method = 'set' . ucwords($property);
                if (method_exists($object, $method))
                    $object->{$method}($attributeValue);
            }
            $this->em->persist($object);
        }

        $this->em->flush();
    }

    /**
     * @param array $attributes
     * @return SymfonyResponse|User
     */
    public function applyAttributes(array $attributes)
    {
        $indexedAttributes = $this->attributesToArray($attributes);
        try {
            $user = $this->handleBasicUserInfo($indexedAttributes);
            if ($user instanceof SymfonyResponse) return $user;
            $user = $this->handleFilterInfo($user, $indexedAttributes);
            $this->handleUserRoleInfo($user, $indexedAttributes);
            $this->handleAdditionalData($user, $indexedAttributes);
        } catch (\Exception $e)
        {
            return new JsonResponse($e->getMessage(), 500);
        }
//        return new JsonResponse(
//            ['Handle user'],
//            500
//        );
        return $user;
    }

//    /**
//     * @param Attribute[] $attributes
//     * @return SymfonyResponse|User
//     * @deprecated
//     */
//    public function setUserData(array $attributes) {
//        try {
//            $attributesValues = $this->attributesToArray($attributes);
//
//            // Handle basic user info
//            $yamlUserConfig = $this->params->get('saml.user');
//            $userPrimaryKey = $yamlUserConfig['primaryKey'];
//            $userAttributeRelations = $yamlUserConfig['relations'];
//            $primaryKeyAttributeName = $userAttributeRelations[$userPrimaryKey];
//
//            if (!array_key_exists($primaryKeyAttributeName, $attributesValues))
//                return new JsonResponse([
//                    'error' => true,
//                    'cause' => "Attribute '$userPrimaryKey' not included in saml attributes"
//                ], 400);
//
//
//            $user = $this->em->getRepository(User::class)->findOneBy([$userPrimaryKey => $attributesValues[$primaryKeyAttributeName]]);
//            if (!$user) {
//                $user = new User();
//                $method = 'set' . ucwords($userPrimaryKey);
//                $user->{$method}($attributesValues[$primaryKeyAttributeName]);
//
//                $password = bin2hex(random_bytes(10));
//
//                $user->setIsActive(true)
//                    ->setOpen(true)
//                    ->setValidated(true)
//                    ->setRoles([User::ROLE_USER])
//                    ->setPassword($this->userPasswordHasher->hashPassword($user, $password))
//                ;
//
//                /**
//                 * Set a default category in user extra
//                 */
//                $userExtra = new UserExtra();
//                $firstCategory = $this->em->getRepository(ProfessionalCategory::class)
//                    ->createQueryBuilder('p')
//                    ->select('p')
//                    ->orderBy('p.id', 'ASC')
//                    ->setMaxResults(1)
//                    ->getQuery()->getOneOrNullResult();
//
//                $userExtra->setCategory($firstCategory);
//                $user->setExtra($userExtra);
//            }
//
//            /**
//             * @var string $key User entity field
//             * @var string $value Saml attribute
//             */
//            foreach ($yamlUserConfig['relations'] as $key => $value) {
//                if (array_key_exists($value, $attributesValues)) {
//                    $method = 'set' . ucwords($key);
//                    if (method_exists($user, $method))
//                        $user->{$method}($attributesValues[$value]);
//                }
//            }
//
//            // Set user filters
//            $userFilters = $this->params->get('saml.filters');
//            foreach ($userFilters as $filter => $categoryId) {
//                if (array_key_exists($filter, $attributesValues)) {
//                    $filterResult = $this->saveFilter($categoryId, $attributesValues[$filter], $attributesValues[$filter]);
//                    if ($filterResult) $user->addFilter($filterResult);
//                }
//            }
//
//            /**
//             * Set roles based on yaml configuration
//             * @see Check the example declared in saml.yaml
//             */
//            $rolesRelations = $this->params->get('saml.roles');
//            $roles = [User::ROLE_USER];
//            if ($rolesRelations['enabled'] && array_key_exists($rolesRelations['attribute'], $attributesValues)) {
//                $role = $attributesValues[$rolesRelations['attribute']];
//                foreach ($rolesRelations['relations'] as $key => $values) {
//                    if (in_array($role, $values)) {
//                        $roles[] = $key;
//                    }
//                }
//            }
//
//            $user->setRoles($roles);
//
//            /**
//             * Extra info
//             *
//             */
//            $extras = $this->params->get('saml.user.extra');
//            foreach ($extras as $extra => $values) {
//                $primaryKey = $values['primaryKey'];
//                $attributeName = $values['attribute'];
//                if (array_key_exists($attributeName, $attributesValues)) {
//                    // The related attribute exists
//                    $result = $this->em->getRepository($values['className'])->findOneBy([$primaryKey => $attributesValues[$attributeName]]);
//                    if ($result) {
//                        $method = 'set' . ucwords($values['property']);
//                        if (method_exists($user, $method))
//                            $user->{$method}($result);
//                    }
//                }
//            }
//
//            $this->em->persist($user);
//            $this->em->flush();
//
//            // Handle roles
//            return $user;
//        } catch (\Exception $e) {
//            return new JsonResponse([
//                'error' => true,
//                'cause' => 'Exception',
//                'data' => $e->getMessage(),
//                'trace' => $e->getTrace()
//            ], 500);
//        }
//    }

//    /**
//     * @param Attribute[] $attributes
//     * @return null | User | SymfonyResponse
//     * @throws \Exception
//     */
//    private function createOrUpdateUser(array $attributes)
//    {
//        $result = $this->setUserFields($attributes);
//        if ($result instanceof SymfonyResponse) return $result;
//        // Find email address
//        $totalAttributes = count($attributes);
//        $emailAddress = null;
//        for ($i = 0; $i < $totalAttributes; $i++) {
//            if ($attributes[$i]->getName() === 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress') {
//                $emailAddress = $attributes[$i]->getFirstAttributeValue();
//                break;
//            }
//        }
//
//        if (!$emailAddress) {
//            $this->logger->error("Saml2Service", [
//                'location' => 'createOrUpdateUser',
//                'cause' => "Required attribute 'emailaddress' not provided or value has not been set"
//            ]);
//
//            $result = $this->params->get('kernel.environment');
//            if ($result !== 'prod') {
//                $normalizer = new ObjectNormalizer();
//                $serializer = new Serializer([new DateTimeNormalizer(['datetime_format' => 'd-m-Y H:i']), $normalizer], [new JsonEncoder()]);
//                $content =  $serializer->serialize([
//                    'cause' => "Attribute 'emailaddress' is required or value has not been set",
//                    'params' => $attributes
//                ], 'json');
//            } else {
//                $content = "Attribute 'emailaddress' is required or value has not been set";
//            }
//
//            return new SymfonyResponse(
//                $content,
//                400,
//                [
//                    'Content-Type' => 'application/json'
//                ]
//            );
//        }
//
//        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $emailAddress]);
//        if (!$user) {
//            $password = bin2hex(random_bytes(10));
//            $user = new User();
//            $user->setEmail($emailAddress)
//                ->setIsActive(true)
//                ->setValidated(true)
//                ->setRoles([User::ROLE_USER])
//                ->setOpen(true)
//                ->setPassword($this->userPasswordHasher->hashPassword($user, $password))
//            ;
//        }
//        $userExtra = $user->getExtra();
//        if (!$userExtra) {
//            $userExtra = new UserExtra();
//            $userExtra->setCategory($this->em->getRepository(ProfessionalCategory::class)->find(1));
//        }
//
//        $filtersAttributeArray = $this->params->get('saml.filters');// Attributes to use as filters
//        $filters = [];
//        $roleFromAttribute = $this->params->get('saml.role.from_attribute');
//
//        foreach ($attributes as $attribute) {
//            $name = $attribute->getName();
//            $value = $attribute->getFirstAttributeValue();
//
//            switch ($name) {
//                case 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname':
//                    $user->setFirstName($value);
//                    break;
//                case 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname':
//                    $user->setLastName($value);
//                    break;
//                case 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/gender':
//                    $gender = 'U';
//                    if ($value == 1) $gender = 'M';
//                    elseif ($value == 2) $gender = 'F';
//                    $userExtra->setGender($gender);
//                    break;
//                case 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/employeeid':
//                    $user->setCode($value);
//                    break;
//                case 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/manager':
//                    $teamManager = $this->em->getRepository(User::class)->findOneBy(['email' => $value]);
//                    $user
//                        ->setTeamManagerEmail($value)
//                        ->setTeamManager($teamManager);
//
//                    break;
//            }
//
//            if ($roleFromAttribute && $name === "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/" . $this->params->get('saml.role.attribute_name')) {
//                // Only if user role is allowed from an attribute
//                $roles = $user->getRoles();
//                $roles[] = $value;
//                $user->setRoles($roles);
//            }
//
//            // Dynamic filter storage
//            // Not included in default branch of the switch because an attribute can be used as a filter or stored in user model
//            $attributeArray = explode('/', $name); // Separate into array
//            $attribute = $attributeArray[count($attributeArray) - 1]; // Get the last element of the array: the name of the attribute
//            if (array_key_exists($attribute, $filtersAttributeArray)) {
//                // Is included as a filter, store filter
//                $categoryId = $filtersAttributeArray[$attribute];
//                $filters[] = $this->saveFilter($categoryId, $value, $value);
//            }
//        }
//
//        $user->setExtra($userExtra);
//        $user->setFilters($filters);
//
//        $this->em->persist($user);
//        $this->em->flush();
//
//        return $user;
//    }

    /**
     * @param $categoryId
     * @param $name
     * @param $code
     * @return Filter|object|null
     */
    private function saveFilter($categoryId, $name, $code) {
        $filterCategoryRepository = $this->em->getRepository(FilterCategory::class);
        $filterRepository = $this->em->getRepository(Filter::class);

        $category = $filterCategoryRepository->find($categoryId);
        $filter = $filterRepository->findOneBy([
            'filterCategory' => $category,
            'name' => $name,
            'code' => $code
        ]);
        if (!$filter) {
            $filter = new Filter();
        }
        $filter->setFilterCategory($category)
            ->setName($name)
            ->setCode($code);
        $this->em->persist($filter);
        return $filter;
    }

    public function generateEntityDescriptor(Request $request)
    {
        $entityDescriptor = new EntityDescriptor();
        $entityDescriptor->setID(\LightSaml\Helper::generateID())
            ->setEntityID($this->settings->get('saml.issuer'));

        $validateSignature = $this->settings->get('saml.validate_signature');

        $entityDescriptor->addItem(
            $spSsoDescriptor = (new SpSsoDescriptor())->setWantAssertionsSigned($validateSignature)
        );

        if ($validateSignature) {
            $spSsoDescriptor->addKeyDescriptor(
                $keyDescriptor = (new KeyDescriptor())
                ->setUse(KeyDescriptor::USE_SIGNING)
                ->setCertificate(X509Certificate::fromFile($this->getSigningCertificatePath()))
            );
        }

        $spSsoDescriptor->addAssertionConsumerService(
            $acs = (new AssertionConsumerService())
            ->setBinding(SamlConstants::BINDING_SAML2_HTTP_POST)
            ->setLocation($request->getSchemeAndHttpHost() . '/saml2/consume/campus')
        );

        //$deserializationContext = new DeserializationContext();
        $serializationContext = new SerializationContext();
        $entityDescriptor->serialize($serializationContext->getDocument(), $serializationContext);
        return $serializationContext->getDocument()->saveXML();
    }

    const CERTIFICATE_MIME_TYPES = [
        'application/x-x509-ca-cert',
        'application/x-x509-user-cert',
        'application/x-pkcs7-crl',
        "application/x-pem-file",
        'application/pkix-cert',
        'application/pem-certificate-chain',
        'text/plain',
    ];

    public function saveIdpUploadedCertificate(Request $request)
    {
        /** @var UploadedFile|null $file */
        $file = $request->files->get('file');
        if (!$file) throw new \RuntimeException('No file uploaded');
        $this->logger->error('mimetype ' . $file->getMimeType());
        if (!$this->filesManagerService->validateFile($file, self::CERTIFICATE_MIME_TYPES)) throw new \RuntimeException("Invalid file format");
        return file_put_contents($this->getIdpSigningCertificatePath(), $file->getContent());
    }

    public function handleIdpMetadata(Request $request): array
    {
        /** @var UploadedFile|null $file */
        $file = $request->files->get('file');
        if ($file) {
            if (!$this->filesManagerService->validateFile($file, ['text/xml']))
                throw new \RuntimeException('File is not valid');
            $xmlContent = $file->getContent();
        } else {
            $content = (json_decode($request->getContent(), true))['content'] ?? null;
            $xmlContent = !empty($content) ? base64_decode($content) : null;
        }

        if (empty($xmlContent)) throw new \RuntimeException("No xml data");


        $deserializationContext = new DeserializationContext();
        $deserializationContext->getDocument()->loadXML($xmlContent);

        $entityDescriptor = new EntityDescriptor();
        $entityDescriptor->deserialize($deserializationContext->getDocument(), $deserializationContext);
        $ssoDescriptor = $entityDescriptor->getFirstIdpSsoDescriptor();

        $entityId = $entityDescriptor->getEntityID();
        $singleSignOnUrl = null;
        foreach ($ssoDescriptor->getAllSingleSignOnServices() as $singleSignOnService) {
            if ($singleSignOnService->getBinding() === SamlConstants::BINDING_SAML2_HTTP_POST) {
                $singleSignOnUrl = $singleSignOnService->getLocation();
                break;
            }
        }

//        $certificateValue = null;
//        try {
//            $keyDescriptors = $ssoDescriptor->getAllKeyDescriptorsByUse(KeyDescriptor::USE_SIGNING);
//            if (count($keyDescriptors) > 0) {
//                $certificateValue = $keyDescriptors[0]->getCertificate()->toPem();
//                file_put_contents($this->getIdpSigningCertificatePath(), $certificateValue);
//            }
//        } catch (\Exception $e) {
//            $certificateValue = "Failed to store key descriptor";
//        }

        return [
            'entityId' => $entityId,
            'singleSignOnUrl' => $singleSignOnUrl
//            'keyDescriptors' => $certificateValue
        ];
    }

    /**
     *
     * @return string
     */
    public function getIdpSigningCertificatePath(): string
    {
        return $this->certsDirectory . DIRECTORY_SEPARATOR . 'idp_signing.pem';
    }

    public function getSigningCertificatePath(): string
    {
        return $this->certsDirectory . DIRECTORY_SEPARATOR . 'signing.crt';
    }

    /**
     * @return false|string
     */
    public function getSigningCertificate()
    {
        $path = $this->getSigningCertificatePath();
        if (!file_exists($path)) {
            throw new \RuntimeException('Signing certificate not found');
        }

        return file_get_contents($path);
    }

    private function getPrivateKeyPath(): string
    {
        $path = $this->getPrivateDir();
        return $path . DIRECTORY_SEPARATOR . 'privateKey.pem';
    }

    private function getPrivateDir(): string
    {
        $path = $this->certsDirectory . DIRECTORY_SEPARATOR . 'private';
        if (!file_exists($path)) {
            if (!mkdir($path, 0755, true)) {
                throw new \RuntimeException("Check file permissions");
            }
        }
        return $path;
    }

    public function generateSaml2Certificates(): string
    {
        $dn = [
            'countryName' => 'ES',
            'stateOrProvinceName' => 'Biskaia',
            'localityName' => 'Bilbao',
            'organizationName' => 'Grupo Gestionet',
            'organizationalUnitName' => 'EasyLearning Tools',
            'commonName' => 'EasyLearning SAML2',
            'emailAddress' => '<EMAIL>'
        ];

        // Generate a new private (and public) key pair
        $privateKey = openssl_pkey_new([
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA
        ]);

        // Generate a certificate signing request
        $csr = openssl_csr_new($dn, $privateKey, ['digest_alg' => 'sha256']);

        // Generate a self-signed cert, valid for 365 days
        $x509 = openssl_csr_sign($csr, null, $privateKey, 365, ['digest_alg' => 'sha256']);

        // Save your private key, CSR and self-signed cert for later use

        $location = $this->settings->get('saml.certificate_location');

        openssl_csr_export($csr, $csrOut) and file_put_contents($this->getPrivateDir() . DIRECTORY_SEPARATOR . 'csr.csr', $csrOut);
        openssl_x509_export($x509, $certOut) and file_put_contents($this->getSigningCertificatePath(), $certOut);
        openssl_pkey_export($privateKey, $pKeyOut) and file_put_contents($this->getPrivateKeyPath(), $pKeyOut);

        return $this->getSigningCertificatePath();
    }
}
