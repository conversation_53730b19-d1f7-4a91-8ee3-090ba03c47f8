<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250902000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create payment table for V2 Payment domain';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'SQL'
                CREATE TABLE payment (
                    id CHAR(36) NOT NULL,
                    purchase_id CHAR(36) NOT NULL,
                    user_id INT NOT NULL,
                    payment_intent VARCHAR(255) NOT NULL,
                    amount INT NOT NULL,
                    currency_code VARCHAR(3) NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    created_at DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)',
                    updated_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                    deleted_at DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)',
                    PRIMARY KEY(id),
                    INDEX idx_payment_purchase_id (purchase_id),
                    INDEX idx_payment_user_id (user_id),
                    INDEX idx_payment_status (status),
                    INDEX idx_payment_payment_intent (payment_intent),
                    INDEX idx_payment_created_at (created_at)
                ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
            SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE payment');
    }
}
