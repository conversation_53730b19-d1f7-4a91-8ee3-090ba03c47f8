<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Builder;

class Director
{
    /**
     * @var Builder
     */
    private $builder;

    public function __construct(Builder $builder)
    {
        $this->builder = $builder;
    }

    public function buildFullAnnouncement()
    {
        $this->builder->setCourse();
        $this->builder->setConfigurationBase();
        $this->builder->setBonus();
        $this->builder->setStudents();
        $this->builder->setTutorGroup();
        $this->builder->setCommunication();
        $this->builder->setSurvey();
        $this->builder->setDiploma();
        $this->builder->setAlertTutor();
    }

    public function buildAnnouncemenwithoutBonus()
    {
        $this->builder->setCourse();
        $this->builder->setConfigurationBase();
        $this->builder->setStudents();
        $this->builder->setTutorGroup();
        $this->builder->setCommunication();
        $this->builder->setSurvey();
        $this->builder->setDiploma();
        $this->builder->setAlertTutor();
    }

    public function getAnnouncement()
    {
        return $this->builder->getAnnouncement();
    }
}
