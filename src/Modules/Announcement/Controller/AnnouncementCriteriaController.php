<?php

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\AnnouncementCriteria;
use App\Entity\AnnouncementCriteriaTranslation;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/")
 */
class AnnouncementCriteriaController extends AbstractCrudController
{
    use SerializerTrait;
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public static function getEntityFqcn(): string
    {
        return AnnouncementCriteria::class;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Get("announcement-criteria/all")
     * @return Response
     */
    public function allCriterias(): Response {
        $data = [];
        /** @var AnnouncementCriteria $criteria */
        foreach ($this->em->getRepository(AnnouncementCriteria::class)->findAll() as $criteria) {
            $translations = [];

            /** @var AnnouncementCriteriaTranslation $translation */
            foreach ($criteria->getTranslations() as $translation) {
                $name = $translation->getName();
                $description = $translation->getDescription();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name ?? '',
                    'description' => $description ?? ''
                ];
            }

            $data[] = [
                'id' => $criteria->getId(),
                'name' => $criteria->getName(),
                'description' => $criteria->getDescription(),
                'active' => $criteria->isActive(),
                'extra' => $criteria->getExtra(),
                'translations' => $translations
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => true,
            'data' => $data
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("announcement-criteria/{id}/active")
     * @param Request $request
     * @param AnnouncementCriteria $criteria
     * @return Response
     */
    public function changeActiveStatus(Request $request, AnnouncementCriteria $criteria): Response {
        $content = json_decode($request->getContent(), true);
        $active = $content['active'] ?? false;
        $criteria->setActive($active);
        $this->em->persist($criteria);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("announcement-criteria/create")
     * @param Request $request
     * @return Response
     */
    public function createCriteria(Request $request): Response
    {
        $criteria = new AnnouncementCriteria();
        if (($result = $this->saveCriteria($request, $criteria)) instanceof Response) return $result;
        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * @Rest\Post("announcement-criteria/update")
     * @param Request $request
     * @return Response
     */
    public function updateCriteria(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $criteria = $this->em->getRepository(AnnouncementCriteria::class)->find($id);
        if (!$criteria) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => 'NOT_FOUND'
        ]);

        if (($result = $this->saveCriteria($request, $criteria)) instanceof Response) return $result;

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => json_decode($request->getContent(), true)
        ]);
    }

    public function saveCriteria(Request $request, AnnouncementCriteria $criteria) {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;
        $extra = $content['extra'] ?? [];
        $errors = [];
        if (empty($name)) $errors[] = 'Name required';
        if (count($errors)) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => $errors
        ]);

        $criteria->setName($name)
            ->setDescription($description)
            ->setActive($active)
            ->setExtra($extra);

        $translations = $content['translations'];
        foreach ($translations as $data) {
            /** @var AnnouncementCriteriaTranslation|null $translation */
            $translation = $this->em->getRepository(AnnouncementCriteriaTranslation::class)
                ->findOneBy([
                    'translatable' => $criteria,
                    'locale' => $data['locale']
                ]);
            $name = $data['name'] ?? null;
            $description = $data['description'] ?? null;
            if (empty($name) && empty($description)) {
                if ($translation) $this->em->remove($translation);
                continue;
            }
            if (!$translation) {
                $translation = new AnnouncementCriteriaTranslation();
                $translation->setTranslatable($criteria);
                $translation->setLocale($data['locale']);
            }
            $translation->setName($name)
                ->setDescription($description)
            ;
            $this->em->persist($translation);
        }

        $this->em->persist($criteria);
        $this->em->flush();
        return true;
    }
}
