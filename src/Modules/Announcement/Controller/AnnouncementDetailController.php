<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\FileValidationTrait;
use App\Admin\Traits\ImpersonateUser;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementGroupSessionAssistanceFiles;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementTutorConnection;
use App\Entity\AnnouncementUser;
use App\Entity\TypeIdentification;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\Admin\AnnouncementGroupAssistanceService;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Service\Annoucement\Admin\AnnouncementNotificationService;
use App\Service\Annoucement\Admin\AnnouncementService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\Admin\TaskUserService;
use App\Service\Annoucement\Email\AnnouncementEmailService;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\General\IpService;
use App\Service\Inspector\InspectorAccessService;
use App\Service\TaskCron\TaskCourseService;
use App\Utils\ZipFile;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Infrastructure\Announcement\Session\AnnouncementGroupSessionResponseVirtualMeetingDataHydrator;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Rest\Route("/admin/")
 */
class AnnouncementDetailController extends AbstractController
{
    use SerializerTrait;
    use ImpersonateUser;
    use FileValidationTrait;

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private RequestStack $requestStack;
    protected TranslatorInterface $translator;
    private AnnouncementService $announcementService;
    private AnnouncementGroupService $announcementGroupService;
    private AnnouncementGroupAssistanceService $announcementGroupAssistanceService;
    private AnnouncementNotificationService $announcementNotificationService;
    private AnnouncementEmailService $announcementEmailService;
    private AnnouncementUserService $announcementUserService;

    private InspectorAccessService $inspectorAccessService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        AnnouncementService $announcementService,
        AnnouncementGroupService $announcementGroupService,
        AnnouncementGroupAssistanceService $announcementGroupAssistanceService,
        AnnouncementNotificationService $announcementNotificationService,
        AnnouncementEmailService $announcementEmailService,
        AnnouncementUserService $announcementUserService,
        InspectorAccessService $inspectorAccessService,
        private readonly AnnouncementGroupSessionResponseVirtualMeetingDataHydrator $virtualMeetingDataHydrator,
        private readonly AnnouncementAuthorizationService $announcementAuthorizationService,
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
        $this->announcementService = $announcementService;
        $this->announcementGroupService = $announcementGroupService;
        $this->announcementGroupAssistanceService = $announcementGroupAssistanceService;
        $this->announcementNotificationService = $announcementNotificationService;
        $this->announcementEmailService = $announcementEmailService;
        $this->announcementUserService = $announcementUserService;
        $this->inspectorAccessService = $inspectorAccessService;
    }

    private function handleException(\Exception $e): Response
    {
        $errorMessage = \sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @Rest\Get("announcement/{id}", requirements={"id"="\d+"})
     */
    public function getAnnouncement(Announcement $announcement, AnnouncementGroupService $announcementGroupService): Response
    {
        try {
            $announcementData = $this->announcementService->getAnnouncementData($announcement);
            $announcementData['groupBasicInfo'] = $announcementGroupService->getAnnouncementGroupsBasicInfo($announcement);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => $announcementData,
            ];

            return $this->sendResponse(
                $response,
                ['datetime_format' => 'c']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Get("groups-announcement/{id}", requirements={"id"="\d+"})
     */
    public function getGroupsAnnouncement(Announcement $announcement): Response
    {
        try {
            $groups = $this->announcementGroupService->getGroupsAnnouncement($announcement);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => $groups,
            ];

            return $this->sendResponse(
                $response,
                ['datetime_format' => 'c']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Get("groups-announcement/assistance/{id}", requirements={"id"="\d+"})
     */
    public function getAnnouncementGroupAssistance(Announcement $announcement): Response
    {
        try {
            $groups = $this->announcementGroupAssistanceService->getSessionsAnnouncementGroup($announcement);
            $groups = $this->virtualMeetingDataHydrator->hydrate($groups);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => $groups,
            ];

            return $this->sendResponse(
                $response,
                ['datetime_format' => 'c']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Post("group-announcement/save-assistance")
     */
    public function saveAssistanceGroup(Request $request): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $idGroup = $content['idGroup'];
            $idSession = $content['idSession'];
            $assistance = $content['assistance'];
            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($idGroup);
            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->findOneBy(['id' => $idSession, 'announcementGroup' => $announcementGroup]);

            if (!$announcementGroupSession) {
                return $this->sendResponse(['error' => true, 'status' => 500, 'message' => 'No se ha encontrado la sesión']);
            }

            // Save previous assistance for comparison
            $previousAssistance = $announcementGroupSession->getAssistance() ?: [];

            // Update assistance in the session
            $announcementGroupSession->setAssistance($assistance);
            $this->em->persist($announcementGroupSession);
            $this->em->flush();

            // Get all users from the group
            $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcementGroup' => $announcementGroup]);

            // Update users' approval status based on assistance changes
            $this->announcementUserService->updateApprovalStatusBasedOnAssistanceChanges(
                $previousAssistance,
                $assistance,
                $announcementUsers,
                $announcementGroupSession->getId()
            );

            return $this->sendResponse(
                [
                    'error' => false,
                    'status' => Response::HTTP_OK,
                ]
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Get("announcement/notifications/{id}", requirements={"id"="\d+"})
     */
    public function getAnnouncementNotifications(Announcement $announcement): Response
    {
        try {
            $announcementNotificationData = $this->announcementNotificationService->getAnnouncementNotification($announcement);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => $announcementNotificationData,
            ];

            return $this->sendResponse(
                $response,
                ['datetime_format' => 'c']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Post("announcement/notification/save")
     */
    public function saveAnnouncementNotifications(Request $request): Response
    {
        try {
            $this->announcementNotificationService->saveOrUpdateNotification($request);

            return $this->sendResponse([
                'error' => false,
                'status' => Response::HTTP_CREATED,
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Post("announcement/notification/delete")
     */
    public function deleteAnnouncementNotification(Request $request, AnnouncementNotificationService $announcementNotificationService): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? null;
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'id required',
            ]);
        }

        if (false === $announcementNotificationService->deleteAnnouncementNotification($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOTIFICATIONS.DELETE.FAILED_OR_NOT_FOUND',
            ]);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Rest\Post("review-homework-teacher")
     */
    public function reviewHomeWorkTeacher(Request $request, TaskUserService $taskUserService): Response
    {
        try {
            return $this->sendResponse($taskUserService->saveReviewTutorTaskUser($request));
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Post("announcement/impersonate/{id}")
     *
     * @deprecated since 2025.06. Will be removed in future versions
     */
    public function impersonateUser(Request $request, Announcement $announcement): Response
    {
        $content = json_decode($request->getContent(), true);
        $userId = $content['userId'] ?? null;

        if (empty($userId)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'userId is required',
            ]);
        }

        $user = $this->em->getRepository(User::class)->find($userId);

        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'User not found',
            ]);
        }

        if (!$this->isGranted('IMPERSONATE_USER', $user)) {
            $data = [
                'status' => Response::HTTP_FORBIDDEN,
                'error' => true,
                'data' => 'You do not have permission to impersonate this user',
            ];

            return $this->sendResponse($data);
        }

        $result = $this->impersonateAsInspector(
            $this->em,
            $userId,
            [
                'course' => $announcement->getCourse()->getId(),
                'announcement' => $announcement->getId(),
            ]
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->inspectorAccessService->generateFullUrl($request, $result),
        ]);
    }

    /**
     * @Rest\Post("announcement/activate-announcement")
     */
    public function enableAnnouncement(Request $request, AnnouncementService $announcementService): Response
    {
        $content = json_decode($request->getContent(), true);
        $idAnnouncement = $content['idAnnouncement'] ?? -1;
        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);

        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);

        $result = $announcementService->activateAnnouncement($announcement);

        return $this->sendResponse([
            'status' => $result['valid'] ? Response::HTTP_OK : Response::HTTP_ACCEPTED,
            'error' => !$result['valid'],
            'data' => $result['valid'] ? 'Announcement notified' : $result['data'],
        ]);
    }

    /**
     * @Rest\Post("announcement/cancel-announcement")
     */
    public function cancelAnnouncement(Request $request, AnnouncementService $announcementService): Response
    {
        $content = json_decode($request->getContent(), true);
        $idAnnouncement = $content['idAnnouncement'] ?? -1;
        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }

        $result = $announcementService->cancelAnnouncement($announcement);

        return $this->sendResponse([
            'status' => $result['valid'] ? Response::HTTP_OK : Response::HTTP_ACCEPTED,
            'error' => !$result['valid'],
            'data' => $result['valid'] ? 'Announcement notified' : $result['data'],
        ]);
    }

    /**
     * @IsGranted("ROLE_TUTOR")
     *
     * @Rest\Post("announcement/tutor-time/{id}", requirements={"id"="\d+"})
     */
    public function saveAnnouncementTutorTime(Request $request, Announcement $announcement, IpService $ipService): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            if (empty($content['userId'])) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'ANNOUNCEMENT.TIME_COUNTER.USER_ID_REQUIRED',
                ]);
            }

            $userId = $content['userId'];

            $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->createQueryBuilder('at')
                ->select('at')
                ->join('at.tutor', 'u')
                ->where('at.announcement =:announcement')
                ->andWhere('u.id = :userId')
                ->setParameter('announcement', $announcement)
                ->setParameter('userId', $userId)
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult();

            if (!$announcementTutor) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'ANNOUNCEMENT.TIME_COUNTER.TUTOR_PROFILE_NOT_FOUND',
                ]);
            }

            $updatedAtMin = (new \DateTime())->modify('-10 minutes');

            $ip = $ipService->getClientIp();

            $connection = $this->em->getRepository(AnnouncementTutorConnection::class)->createQueryBuilder('atc')
                ->where('atc.announcementTutor =:tutor')
                ->andWhere('atc.updatedAt >= :updatedAtMin')
                ->andWhere('atc.ip = :ip')
                ->setParameter('tutor', $announcementTutor)
                ->setParameter('updatedAtMin', $updatedAtMin)
                ->setParameter('ip', $ip)
                ->setMaxResults(1)
                ->addOrderBy('atc.createdAt', 'DESC')
                ->getQuery()
                ->getOneOrNullResult();

            if (!$connection) {
                $connection = new AnnouncementTutorConnection();
                $connection->setAnnouncementTutor($announcementTutor)
                    ->setIp($ip)
                    ->setTime(0);
            }

            $time = empty($content['time']) ? 0 : (int) $content['time']; // Time in seconds
            $connection->setTime($connection->getTime() + $time);

            $this->em->persist($connection);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $announcement->getId(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => false,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("announcement/{announcement}/groups/session/download-assistance")
     *
     * @return Response
     */
    public function downloadAnnouncementGroupSessionAssistance(Request $request, MpdfFactory $mpdfFactory)
    {
        $content = json_decode($request->getContent(), true);
        $sessionId = $content['sessionId'] ?? null;
        if (empty($sessionId)) {
            return $this->sendResponse([
                Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'sessionId required',
            ]);
        }
        $session = $this->em->getRepository(AnnouncementGroupSession::class)->find($sessionId);
        if (!$session) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Session not found',
            ]);
        }

        $group = $session->getAnnouncementGroup();
        $announcement = $group->getAnnouncement();
        $users = $this->em->getRepository(User::class)->createQueryBuilder('u')
            ->select('u.id', 'u.firstName', 'u.lastName', 'f_fields.dni')
            ->join('u.announcements', 'au')
            ->leftJoin('u.userFieldsFundae', 'f_fields')
            ->where('au.announcementGroup =:group')
            ->setParameter('group', $group)
            ->addGroupBy('u.id')
            ->getQuery()
            ->getResult();

        $mpdf = $mpdfFactory->createMpdfObject([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => 5,
            'margin_footer' => 5,
            'orientation' => 'L',
        ]);

        $mpdf->SetTopMargin('20');
        $mpdf->SetHTMLHeader($this->renderView('layout/headerpdf.html.twig'));
        $mpdf->SetFooter('{PAGENO}');

        $tutor = $group->getAnnouncementTutor()->getTutor();
        $tutor->getFullName();

        $mpdf->WriteHTML($this->renderView('fundae/sessionAssistance/assistance.html.twig', [
            'group' => $group,
            'announcement' => $announcement,
            'users' => $users,
            'tutor' => $tutor,
            'session' => $session,
            'mainIdentification' => $this->em->getRepository(TypeIdentification::class)->getMainIdentificationForThePlatform($this->getUser()->getLocale()),
        ]));

        $mpdf->AddPage('P');
        $mpdf->WriteHTML('<strong>OBSERVACIONES GENERALES:</strong>');

        return $mpdfFactory->createDownloadResponse($mpdf, 'asistance.pdf');
    }

    /**
     * @return Response|true
     */
    private function saveAnnouncementGroupSessionAssistanceFile(AnnouncementGroupSession $session, UploadedFile $file)
    {
        if (!$file->getPerms()) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Check temp folder permission. File is not readable',
            ]);
        }

        if (!$this->isPdfFile($file) && !$this->isImage($file)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'The file must be a PDF or image file type',
            ]);
        }
        $assistanceFile = new AnnouncementGroupSessionAssistanceFiles();
        $assistanceFile->setAnnouncementGroupSession($session)
            ->setFilenameFile($file);
        $this->em->persist($assistanceFile);

        return true;
    }

    /**
     * @Rest\Post("announcement/group-session/{session}/upload-assistance")
     */
    public function uploadAnnouncementGroupSessionAssistanceFile(Request $request, AnnouncementGroupSession $session): Response
    {
        $filesLength = (int) $request->get('filesLength', 0);
        if ($filesLength > 0) {
            for ($i = 0; $i < $filesLength; ++$i) {
                /** @var UploadedFile|null $file */
                $file = $request->files->get("file_$i");
                if (!$file) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_ACCEPTED,
                        'error' => true,
                        'data' => 'Read file failed',
                    ]);
                }

                if (($r = $this->saveAnnouncementGroupSessionAssistanceFile($session, $file)) instanceof Response) {
                    return $r;
                }
            }
        } else {
            /** @var UploadedFile|null $file */
            $file = $request->files->get('file');
            if (!$file) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'FILE_UPLOAD.FILE.NOT_PROVIDED',
                ]);
            }
            if (($r = $this->saveAnnouncementGroupSessionAssistanceFile($session, $file)) instanceof Response) {
                return $r;
            }
        }
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Rest\Get("announcement/groups/{id}", requirements={"id"="\d+"})
     */
    public function getGroupByAnnouncement(Announcement $announcement): Response
    {
        try {
            $announcementGroup = $this->announcementGroupService->getGroupByAnnouncement($announcement);

            $response = [
                'error' => false,
                'status' => Response::HTTP_OK,
                'data' => $announcementGroup,
            ];

            return $this->sendResponse(
                $response,
                ['datetime_format' => 'c']
            );
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Get("announcement/{id}/tasks")
     */
    public function getAnnouncementTasks(Announcement $announcement, TaskCourseService $taskCourseService): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $taskCourseService->getAnnouncementTasks($announcement),
        ], ['datetime_format' => 'c']);
    }

    /**
     * @Rest\Get("announcement/group-session/{session}/download-assistance-files")
     */
    public function downloadAllAssistanceFiles(AnnouncementGroupSession $session, KernelInterface $kernel): Response
    {
        $files = [];
        $projectDir = $kernel->getProjectDir();
        foreach ($session->getAssistanceFiles() as $assistanceFile) {
            $files[] = $projectDir . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . $this->getParameter('app.announcement_assistance_group_session') . $assistanceFile->getFilename();
        }

        $zip = new ZipFile();
        $zip->setFiles($files)
            ->setPath($projectDir . DIRECTORY_SEPARATOR . 'tmp')
            ->setFilename('Session ' . $session->getId() . ' assistance files');
        if (!$zip->generate()) {
            return new Response('Failed to generate zip file', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return $zip->download(true);
    }

    /**
     * @Rest\Post("announcement/group-session/{session}/delete-assistance-file")
     */
    public function deleteSessionAssistanceFile(Request $request, AnnouncementGroupSession $session, KernelInterface $kernel): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? null;
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'File \'id\' required',
            ]);
        }

        $assistanceFile = $this->em->getRepository(AnnouncementGroupSessionAssistanceFiles::class)->findOneBy([
            'id' => $id,
            'announcementGroupSession' => $session,
        ]);

        if (!$assistanceFile) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => false,
            ]);
        }

        $projectDir = $kernel->getProjectDir();

        $filePath = $projectDir . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . $this->getParameter('app.announcement_assistance_group_session') . $assistanceFile->getFilename();
        if (file_exists($filePath)) {
            $deletedFile = unlink($filePath);
        } else {
            $deletedFile = true;
        }

        if ($deletedFile) {
            $this->em->remove($assistanceFile);
            $this->em->flush();
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Rest\Get("announcement/{id}/groups-reports")
     */
    public function getAnnouncementGroupReports(Announcement $announcement): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $groups = $this->announcementGroupService->getAnnouncementGroups($announcement, $user);
        $data = [];

        foreach ($groups as $group) {
            $reports = $this->em->getRepository(ZipFileTask::class)
                ->createQueryBuilder('ztf')
                ->select('ztf.id as taskId', 'ztf.status', 'ztf.params', 'ztf.createdAt', 'ztf.finishedAt')
                ->addSelect('filesManager.filename', 'IFNULL(filesManager.originalName, ztf.originalName) as originalName', 'filesManager.mimeType')
                ->addSelect('CONCAT(u.firstName, \' \', u.lastName) as createdByName')
                ->leftJoin('ztf.filesManager', 'filesManager')
                ->join('ztf.createdBy', 'u')
                ->where('ztf.type = :type')
                ->andWhere('ztf.entityId = :entityId')
                ->setParameters([
                    'type' => AnnouncementReportService::TYPE_GROUP,
                    'entityId' => $group->getId(),
                ])
                ->orderBy('ztf.createdAt', 'DESC')
                ->getQuery()
                ->getResult()
            ;
            $data[$group->getId()] = $reports;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ], ['datetime_format' => 'c']);
    }
}
