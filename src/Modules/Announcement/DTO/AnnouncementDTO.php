<?php

declare(strict_types=1);

namespace App\Modules\Announcement\DTO;

class AnnouncementDTO
{
    public string $codForm;
    public string $courseName;
    public \DateTimeImmutable $startAt;
    public \DateTimeImmutable $finishAt;
    public int $hours;
    public int $cost;
    public ?string $tutor;
    public array $extra;
    public ?string $modality;
    public bool $isExternal;

    public function __construct(
        string $codForm,
        string $courseName,
        \DateTimeImmutable $startAt,
        \DateTimeImmutable $finishAt,
        int $hours,
        int $cost,
        ?string $tutor = null,
        array $extra = [],
        ?string $modality = null,
        bool $isExternal = false
    ) {
        $this->codForm = $codForm;
        $this->courseName = $courseName;
        $this->startAt = $startAt;
        $this->finishAt = $finishAt;
        $this->hours = $hours;
        $this->cost = $cost;
        $this->tutor = $tutor;
        $this->extra = $extra;
        $this->modality = $modality;
        $this->isExternal = $isExternal;
    }
}
