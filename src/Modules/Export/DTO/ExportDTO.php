<?php

declare(strict_types=1);

namespace App\Modules\Export\DTO;

class ExportDTO
{
    public int $id;
    public \DateTimeImmutable $createdAt;
    public ?\DateTimeImmutable $availableUntil;
    public string $filename;
    public ?array $meta;
    public ?\DateTimeImmutable $finishedAt;
    public int $status;

    public function __construct(
        int $id,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $availableUntil,
        string $filename,
        ?array $meta,
        ?\DateTimeImmutable $finishedAt,
        int $status
    ) {
        $this->id = $id;
        $this->createdAt = $createdAt;
        $this->availableUntil = $availableUntil;
        $this->filename = $filename;
        $this->meta = $meta;
        $this->finishedAt = $finishedAt;
        $this->status = $status;
    }
}
