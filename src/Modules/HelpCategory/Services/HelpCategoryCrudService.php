<?php

namespace App\Modules\HelpCategory\Services;

use App\Entity\HelpCategory;
use App\Entity\HelpCategoryTranslation;
use App\Modules\Common\Services\BaseService;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\SettingsService;
use Symfony\Component\Security\Core\Security;

class HelpCategoryCrudService extends BaseService
{   
    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security     
    ) {
        parent::__construct($em, $settings, $security);
    }
    
    public function formatHelpCategoryStructure(HelpCategory $helpCategory): array
    {
        $translations = $this->getTranslationCategory($helpCategory);

        return [
            'category' => [
                'id' => $helpCategory->getId(),
                'name' => $helpCategory->getName(),
                'sort' => $helpCategory->getSort(),
            ],
            'translations' => $translations
        ];
    }

    public function getTranslationCategory(HelpCategory $helpCategory): array
    {
        $translations = [];
        foreach($this->em->getRepository(HelpCategoryTranslation::class)->findBy(['translatable' => $helpCategory]) as $translation){
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName()
            ];
        }
        return $translations;
    }

    public function setTranslations(HelpCategory $helpCategory, array $translations):void
    {
        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tName = $t['name'] ?? null;
            $translation = $this->em->getRepository(HelpCategoryTranslation::class)->findOneBy([
                'translatable' => $helpCategory,
                'locale' => $tLocale
            ]);

            if (empty($tName)) {
                if ($translation) $this->em->remove($translation);
                continue;
            }

            if (!$translation) {
                $translation = new HelpCategoryTranslation();
                $translation->setTranslatable($helpCategory);
                $translation->setLocale($tLocale);
            }

            $translation->setName($tName);
            $this->em->persist($translation);
        }
    }
}