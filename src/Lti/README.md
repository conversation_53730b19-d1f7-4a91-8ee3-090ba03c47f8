# LTI (Learning Tools Interoperability)

[Standard Page](https://www.1edtech.org/standards/lti)

`composer.json`
```json
{
  "require": {
    "oat-sa/bundle-lti1p3": "*",
    "oat-sa/lib-lti1p3-ags": "^1.4",
    "oat-sa/lib-lti1p3-core": "*"
  }
}
```

## Launch new link

```php
    function testLaunch(
        LtiResourceLinkLaunchRequestBuilder $ltiResourceLinkLaunchRequestBuilder,
        RegistrationRepositoryInterface $repository
    ){
        $resourceLink = new LtiResourceLink(
            '94606241-3687-484c-a79c-d6cfbcce32cb',
            [
                'url' => '<launch-url>'
            ]
        );
    
        $result = $ltiResourceLinkLaunchRequestBuilder->buildLtiResourceLinkLaunchRequest(
                $resourceLink,// Previous resource link
                $repository->find('local'),// Provider registration
                'loginHint',        // Login Hint
                'deploymentId1',    // Deployment      
                [
                    'http://purl.imsglobal.org/vocab/lis/v2/membership#Learner' // Role
                ],
                [
                    LtiMessagePayloadInterface::CLAIM_LTI_CUSTOM => [
                        'id' => "<resource-link-id>"
                    ],
                    LtiMessagePayloadInterface::CLAIM_LTI_AGS => [
                        "scope" => [
                            "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem",   // Allow tool to send lineItems
                            "https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly", // Allow tool to send results
                            "https://purl.imsglobal.org/spec/lti-ags/scope/score", // Allow tool to send scores
                        ],
                        /**
                        * By default lineitems is the url to get all lineItems of a resource link id
                        * Every other url GET|POST|PUT|DELETE for lineitem, results and score will have
                        * lineitems URL as base.
                        */
                        "lineitems" => "<BASE-URL>/<resourec-link-id>/lineitems",
                    ]
                ]
            );
        }
```
