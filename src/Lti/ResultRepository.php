<?php

namespace App\Lti;

use App\Repository\LtiLineItemResultRepository;
use OAT\Library\Lti1p3Ags\Model\Result\ResultCollection;
use OAT\Library\Lti1p3Ags\Model\Result\ResultCollectionInterface;
use OAT\Library\Lti1p3Ags\Model\Result\ResultInterface;
use OAT\Library\Lti1p3Ags\Repository\ResultRepositoryInterface;

class ResultRepository implements ResultRepositoryInterface
{
    private LtiLineItemResultRepository $ltiLineItemResultRepository;

    public function __construct(LtiLineItemResultRepository $ltiLineItemResultRepository)
    {
        $this->ltiLineItemResultRepository = $ltiLineItemResultRepository;
    }

    public function findCollectionByLineItemIdentifier(string $lineItemIdentifier, ?int $limit = null, ?int $offset = null): ResultCollectionInterface
    {
        return $this->ltiLineItemResultRepository->findCollectionByLineItemIdentifier($lineItemIdentifier, $limit, $offset);
    }

    public function findByLineItemIdentifierAndUserIdentifier(string $lineItemIdentifier, string $userIdentifier): ?ResultInterface
    {
        return $this->ltiLineItemResultRepository->findByLineItemIdentifierAndUserIdentifier($lineItemIdentifier, $userIdentifier);
    }
}
