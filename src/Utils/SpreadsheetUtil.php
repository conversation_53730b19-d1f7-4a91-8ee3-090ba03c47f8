<?php

declare(strict_types=1);

namespace App\Utils;

use PhpOffice\PhpSpreadsheet\Cell\IValueBinder;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class SpreadsheetUtil
{
    public Spreadsheet $spreadsheet;
    private string $filename;

    public function __construct(string $filename, string $title = 'DEFAULT')
    {
        $this->spreadsheet = new Spreadsheet();
        $this->filename = $filename;
        $title = $this->cleanSheetTitle($title);
        $this->spreadsheet->getActiveSheet()->setTitle($title);
    }

    public function getFilename(): string
    {
        if (false !== strpos('.xlsx', $this->filename)) {
            return $this->filename;
        }

        return $this->filename . '.xlsx';
    }

    public function getLastColumn($row = 1): string
    {
        return $this->spreadsheet->getActiveSheet()->getHighestDataColumn($row);
    }

    public function getLastRow($column = 'A'): int
    {
        return $this->spreadsheet->getActiveSheet()->getHighestDataRow($column);
    }

    public function fromArray(array $source, $nullValue = null, $startCell = 'A1', $strictNullComparison = false): self
    {
        $this->spreadsheet->getActiveSheet()->fromArray($source, $nullValue, $startCell, $strictNullComparison);

        return $this;
    }

    public function sheetExists(string $title, bool $activate = true): ?Worksheet
    {
        foreach ($this->spreadsheet->getAllSheets() as $i => $worksheet) {
            if ($worksheet->getTitle() === $title) {
                if ($activate) {
                    $this->spreadsheet->setActiveSheetIndex($i);
                }

                return $worksheet;
            }
        }

        return null;
    }

    //    const NOT_ALLOWED_CHARS = ['/', '\\', '?', '*', ':', '[', ']'];
    public const NOT_ALLOWED_CHARS = "/[\/\\?*:\[\]]/";

    //    const NOT_ALLOWED_CHARS = "/\\?*:[]";
    public function cleanSheetTitle(string $title)
    {
        if ('History' === $title) {
            throw new \RuntimeException("Title 'History' cannot be used");
        }
        if (empty($title)) {
            throw new \RuntimeException('Title cannot be empty');
        }
        $string = preg_replace(self::NOT_ALLOWED_CHARS, '', $title);
        if (\strlen($string) > 31) {
            $string = substr($string, 0, 31);
        }

        return $string;
    }

    /**
     * @throws Exception
     */
    public function addSheet(string $title = 'Default', bool $active = true): Worksheet
    {
        $title = $this->cleanSheetTitle($title);
        if ($sheet = $this->sheetExists($title, $active)) {
            return $sheet;
        }// Find sheet and activate if it exists
        // Sheet not found, create and activate
        $sheet = $this->spreadsheet->createSheet();
        $sheet->setTitle($title);
        if ($active) {
            $sheet = $this->spreadsheet->setActiveSheetIndexByName($title);
        }

        return $sheet;
    }

    public function setAutosize(string $start = 'A', string $end = 'Z'): self
    {
        foreach (range($start, $end) as $column) {
            $this->spreadsheet->getActiveSheet()
                ->getColumnDimension($column)
                ->setAutoSize(true);
        }

        return $this;
    }

    /**
     * @param array $headers    List of headers ["HEADER1", "HEADER2", "HEADER_N"]
     * @param bool  $autosize   Enable auto sizing for the sheet
     * @param bool  $freeze     Freeze headers pane
     * @param bool  $autoFilter Enable auto filters in headers
     * @param int   $row        Row to start headers
     * @param array $style      ["fontSize" => 16, "bold" => true, "color" => "000000"]Color must be RGB
     *
     * @return $this
     *
     * @throws Exception
     */
    public function setHeaders(
        array $headers = [],
        bool $autosize = false,
        bool $freeze = false,
        bool $autoFilter = false,
        int $row = 1,
        array $style = [
            'fontSize' => 12,
            'bold' => false,
            'color' => '000000', // RGB
            'fill' => null,
        ]
    ): self {
        $this->spreadsheet->getActiveSheet()->fromArray([$headers]);
        $maxColumn = $this->spreadsheet->getActiveSheet()->getHighestDataColumn();
        $this->spreadsheet->getActiveSheet()->getStyle("A$row:$maxColumn$row")
            ->getFont()
            ->setSize($style['fontSize'] ?? 16)
            ->setBold($style['bold'] ?? false)
            ->getColor()
            ->setRGB($style['color'] ?? '000000')
        ;
        $fillColor = $style['fill'] ?? null;
        if (null !== $fillColor) {
            if (\is_array($fillColor)) {
                // Column => $color
                foreach ($fillColor as $column => $color) {
                    $this->spreadsheet->getActiveSheet()
                        ->getStyle("$column$row")
                        ->getFill()
                        ->setFillType(Fill::FILL_SOLID)
                        ->getStartColor()
                        ->setRGB($color);
                }
            } else {
                $this->spreadsheet->getActiveSheet()
                    ->getStyle("A$row:$maxColumn$row")
                    ->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setRGB($fillColor);
            }
        }

        if ($autosize) {
            $this->setAutosize('A', $maxColumn);
        }
        if ($freeze) {
            $freezeStart = $row + 1;
            $this->spreadsheet->getActiveSheet()
                ->freezePane('A' . $freezeStart);
        }

        if ($autoFilter) {
            $this->spreadsheet->getActiveSheet()->setAutoFilter("A$row:$maxColumn" . $row);
        }

        return $this;
    }

    public function setBold(string $cell = 'A1'): self
    {
        $this->spreadsheet->getActiveSheet()->getStyle($cell)->getFont()->setBold(true);

        return $this;
    }

    public function setCellColor(string $cell = 'A1', string $color = '000000'): self
    {
        $this->spreadsheet->getActiveSheet()
            ->getStyle($cell)
            ->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB($color);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws Exception
     */
    public function activateSheetByName(string $name): self
    {
        $this->spreadsheet->setActiveSheetIndexByName($name);

        return $this;
    }

    public function activateSheetByIndex(int $index = 0): self
    {
        $this->spreadsheet->setActiveSheetIndex($index);

        return $this;
    }

    /**
     * Only support from A -> ZZ.
     *
     * @throws \Exception
     */
    public static function getNextColumn(string $current = 'A'): string
    {
        if (\strlen($current) < 1) {
            return 'A';
        }
        $lastCharacter = substr($current, -1); // "AAB" returns B
        $lastCharacterNumber = \ord($lastCharacter);
        if (90 === $lastCharacterNumber) {
            // Last character is Z
            $nextCharacterNumber = 65;
        } else {
            $nextCharacterNumber = $lastCharacterNumber + 1;
        }

        $nextCharacter = \chr($nextCharacterNumber);
        if (1 === \strlen($current)) {
            if ('Z' === $lastCharacter) {
                return "A$nextCharacter";
            }

            return $nextCharacter;
        }

        $prev2Character = substr($current, -2, 1);
        $prev2CharacterNumber = \ord($prev2Character);
        if (90 === $prev2CharacterNumber && 90 === $lastCharacterNumber) {
            throw new \Exception('Max ZZ supported, AAA or greater not supported');
        }
        $temp = \chr($prev2CharacterNumber + 1);

        return $temp . $nextCharacter; // Max ZZ
    }

    public function saveReport(string $path, $preCalculateFormulas = true): void
    {
        if (!file_exists($path)) {
            if (!mkdir($path, 0777, true)) {
                throw new \Exception('Failed to create directory');
            }
        }
        $fullPath = $path . DIRECTORY_SEPARATOR . $this->getFilename();
        $writer = new Xlsx($this->spreadsheet);
        $writer->setPreCalculateFormulas($preCalculateFormulas);
        $writer->save($fullPath);
    }

    public function setHeaderColors(array $headers = [], array $colors = [])
    {
        $column = 'A';
        foreach ($headers as $header) {
            // Uppercase range 65,90
            $color = $colors[$header] ?? 'FFFFFF';
            $cell = "$column" . 1;
            $this->setCellColor($cell, $color)->setBold($cell);
            $column = self::getNextColumn($column);
        }
    }

    /**
     * @throws Exception
     */
    public function mergeCells($range): self
    {
        $this->spreadsheet->getActiveSheet()
            ->mergeCells($range);

        return $this;
    }

    /**
     * @param array $params Pass extra information to cell value: ["fontSize" => 1, "bold": true|false, 'color' => 'RGB']
     *
     * @return $this
     */
    public function setCellValue($cell, $value, array $params = [], ?IValueBinder $binder = null): self
    {
        $fontSize = $params['fontSize'] ?? 14;
        $bold = $params['bold'] ?? false;
        $color = $params['color'] ?? '000000';

        $this->spreadsheet->getActiveSheet()
            ->setCellValue($cell, $value, $binder)
            ->getStyle($cell)
                ->getFont()
                ->setSize($fontSize)
                ->setBold($bold)
                ->getColor()
                ->setRGB($color)
        ;

        return $this;
    }

    public function setCellAlignmentHorizontal($cellAlignment, $alignment)
    {
        if (\is_array($cellAlignment)) {
            foreach ($cellAlignment as $align) {
                $this->spreadsheet->getActiveSheet()
                    ->getStyle($align)
                    ->getAlignment()
                    ->setHorizontal($alignment);
            }
        } else {
            $this->spreadsheet->getActiveSheet()
                ->getStyle($cellAlignment)
                ->getAlignment()
                ->setHorizontal($alignment);
        }

        return $this;
    }

    public function getSheet(string $title): Worksheet
    {
        $sheet = $this->sheetExists($title, true);
        if ($sheet) {
            return $sheet;
        }

        return $this->addSheet($title, true);
    }

    public function alignAllLeft(): self
    {
        $worksheet = $this->spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestRow();
        $highestColumn = $worksheet->getHighestColumn();
    
        $worksheet->getStyle("A1:{$highestColumn}{$highestRow}")
            ->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
    
        return $this;
    }
    

}
