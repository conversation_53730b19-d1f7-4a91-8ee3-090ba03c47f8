<?php

declare(strict_types=1);

namespace App\Utils\UserCourse;

use App\Entity\Announcement;

class CourseStatus
{
    public bool $courseFinishedOnTime;
    public bool $courseStartedOnTime;

    public function __construct(bool $courseFinishedOnTime, bool $courseStartedOnTime)
    {
        $this->courseFinishedOnTime = $courseFinishedOnTime;
        $this->courseStartedOnTime = $courseStartedOnTime;
    }

    public static function fromContent(array $content = [], ?Announcement $announcement): self
    {
        if ($announcement) {
            return new self(false, false);
        }

        $courseFinishedOnTime = filter_var(
            !empty($content['courseFinishedIntime']) && !$announcement ? $content['courseFinishedIntime'] : false,
            FILTER_VALIDATE_BOOLEAN
        );

        $courseStartedOnTime = filter_var(
            !empty($content['courseStartedIntime']) && !$announcement ? $content['courseStartedIntime'] : false,
            FILTER_VALIDATE_BOOLEAN
        );

        return new self($courseFinishedOnTime, $courseStartedOnTime);
    }
}
