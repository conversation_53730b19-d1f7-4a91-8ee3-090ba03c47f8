<?php

declare(strict_types=1);

namespace App\Utils\UserCourse;

use App\Entity\Announcement;

class DateRange
{
    public ?\DateTimeImmutable $dateFrom;
    public ?\DateTimeImmutable $dateTo;

    public function __construct(?\DateTimeImmutable $dateFrom, ?\DateTimeImmutable $dateTo)
    {
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
    }

    public static function fromContent(array $content, ?Announcement $announcement): self
    {
        if ($announcement) {
            return new self(null, null);
        }

        $dateFrom = !empty($content['dateFrom']) ? \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateFrom']) : null;
        $dateTo = !empty($content['dateTo']) ? \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateTo']) : null;

        return new self($dateFrom, $dateTo);
    }
}
