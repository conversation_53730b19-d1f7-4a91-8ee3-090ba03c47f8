<?php

namespace App\Utils\TimeZoneConverter;

trait TimezoneVerifierTrait
{
    /**
     * @param string $timezone Set a custom timezone to DateTime values without converting from other timezone
     * @return void
     * @throws \Exception
     */
    public function defineTimezone(string $timezone)
    {
        $availableVariables = get_object_vars($this);
        $tz = new \DateTimeZone($timezone);
        foreach ($availableVariables as $key =>$value)
        {
            $variable = $this->{$key};
            if ($variable instanceof \DateTime || $variable instanceof \DateTimeImmutable)
            {
                $this->{$key} = new \DateTimeImmutable($variable->format('Y-m-d H:i:s'), $tz);
            }
        }
    }
}
