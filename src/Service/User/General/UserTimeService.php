<?php

namespace App\Service\User\General;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserTime;
use App\Service\General\IpService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Validator\Constraints\Ip;

class UserTimeService
{
    /**
     * routeName => type
     */
    const FRONT_SECTIONS = [
        'Itineraries' => 'itineraries',
        'Section' => 'section',
        'Course' => 'course',
        'forum' => 'forum',
        'challenges' => 'challenges',
        'Chapter' => 'chapter',
        'Archives' => 'archives',
        'Archive' => 'archive',
    ];

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private IpService $ipService;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger, IpService $ipService)
    {
        $this->em = $em;
        $this->logger = $logger;
        $this->ipService = $ipService;
    }

    public function handleGlobalTime(User $user, Request $request, int $time)
    {
        $currentDate = (new \DateTimeImmutable())->modify('-10 minutes');
        /** @var UserTime $userTime */
        $userTime = $this->em->getRepository(UserTime::class)->createQueryBuilder('ut')
            ->where('ut.user =:user')
            ->andWhere('ut.updatedAt >= :updated_at')
            ->andWhere('ut.name = \'GLOBAL\'')
            ->setParameter('user', $user)
            ->setParameter('updated_at', $currentDate)
            ->setMaxResults(1)
            ->orderBy('ut.updatedAt', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();

        $ip = $this->ipService->getClientIp();

        if (!$userTime || $userTime->getIp() != $ip) {
            $userTime = new UserTime();
            $userTime->setName('GLOBAL')
                ->setUser($user)
                ->setCreatedAt(new \DateTimeImmutable())
                ->setIp($ip);
        }

        $userTime->setTime($userTime->getTime() + $time);

        $this->em->persist($userTime);
        $this->em->flush();

        return new JsonResponse(
            [
                'error' => false,
                'data' => []
            ],
            Response::HTTP_CREATED
        );
    }

    public function saveUserTimer(Request $request, User $user): Response
    {
        $content = json_decode($request->getContent(), true);
        if (!isset($content['name'])) return new JsonResponse(['error' => true, 'data' => 'No name'], Response::HTTP_ACCEPTED);
        $name = $content['name'];
        $time = (int)$content['time'];
        $params = $content['params'] ?? [];
        $newRest = $content['new'] ?? false;
        
        if ($name === 'GLOBAL' || $newRest) return $this->handleGlobalTime($user, $request, $time);


        if (!array_key_exists($name, self::FRONT_SECTIONS)) return new JsonResponse(['error' => false], Response::HTTP_ACCEPTED);

        $routeName = self::FRONT_SECTIONS[$name];


        $date = new \DateTimeImmutable();

        $course = null;
        if (array_key_exists('courseId', $params)) {
            $course = $this->em->getRepository(Course::class)->find($params['courseId']);
        }

        $announcement = null;
        if ($course) {
            $announcement = $this->em->getRepository(Announcement::class)->findAnnouncementUserNotified($course, $user);
        }

        $parameters = [
            'user' => $user,
            'createdAt' => $date,
            'name' => $routeName,
            'course' => $course
        ];

        if ($course) {
            $parameters['course'] = $course;
        }

        if ($announcement && $announcement->getFinishAt() >= $date) {
            $parameters['announcement'] = $announcement;
        }

        $userTime = $this->em->getRepository(UserTime::class)->findOneBy($parameters);

        if (!$userTime) {
            $userTime = new UserTime();
            $userTime->setUser($user)
                ->setName($routeName)
                ->setCreatedAt($date)
                ->setIp($this->ipService->getClientIp())
                ->setTime(0);

            if (array_key_exists('courseId', $params)) {
                $userTime->setCourse($course);
            }

            if ($announcement && $announcement->getFinishAt() >= $date) {
                $userTime->setAnnouncement($announcement);
            }
        }

        $extra = $userTime->getExtra();
        if (array_key_exists('section', $params)) {
            // the time is for the section in the current route
            // array format is ['section' => ['id' => value ] ]

            if (!array_key_exists($params['section'], $extra)) {
                $extra[$params['section']] = [];
            }
            $section = $extra[$params['section']];
            $sectionId = $params['sectionId'];
            if (!array_key_exists($sectionId, $section)) $section[$sectionId] = 0;
            $section[$sectionId] += $time;
            $extra[$params['section']] = $section;

            $params = $extra;
        } else {
            if (!empty($extra)) $params = $extra;
            $t = $userTime->getTime() + $time;
            $userTime->setTime($t);
        }
        
        $userTime->setExtra($params);
        $userTime->setUpdatedAt(new \DateTimeImmutable());
        $this->em->persist($userTime);
        $this->em->flush();

        return new JsonResponse(['error' => false], Response::HTTP_OK);
    }
    

    public function existRegisterConexion(User $user, Announcement $announcement)
    {
        $currentDate = (new \DateTimeImmutable())->modify('-10 minutes');
        /** @var UserTime $userTime */
        $userTime = $this->em->getRepository(UserTime::class)->createQueryBuilder('ut')
            ->where('ut.user =:user')
            ->andWhere('ut.updatedAt >= :updated_at')
            ->andWhere('ut.name = \'GLOBAL\'')
            ->setParameter('user', $user)
            ->setParameter('updated_at', $currentDate)
            ->setMaxResults(1)
            ->orderBy('ut.updatedAt', 'DESC')
            ->getQuery()
            ->getOneOrNullResult();

        if($userTime && $userTime->getIp() == $this->ipService->getClientIp() && !$userTime->getAnnouncement()){            
            $userTime->setAnnouncement($announcement);        
            $userTime->setUpdatedAt(new \DateTimeImmutable());
            $this->em->persist($userTime);
            $this->em->flush();
            return true;
        }else
            return false;
    }
}
