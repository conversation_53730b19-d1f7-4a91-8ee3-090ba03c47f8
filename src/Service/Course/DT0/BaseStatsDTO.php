<?php

declare(strict_types=1);

namespace App\Service\Course\DT0;

class BaseStatsDTO
{
    public ?\DateTimeImmutable $dateFrom;
    public ?\DateTimeImmutable $dateTo;
    public ?int $announcementId;
    public ?bool $courseStartedOnTime;
    public ?bool $courseFinishedOnTime;

    public function __construct(
        ?\DateTimeImmutable $dateFrom = null,
        ?\DateTimeImmutable $dateTo = null,
        ?int $announcementId = null,
        ?bool $courseStartedOnTime = null,
        ?bool $courseFinishedOnTime = null
    ) {
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
        $this->announcementId = $announcementId;
        $this->courseStartedOnTime = $courseStartedOnTime;
        $this->courseFinishedOnTime = $courseFinishedOnTime;
    }
}
