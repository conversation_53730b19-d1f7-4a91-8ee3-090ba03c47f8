<?php

declare(strict_types=1);

namespace App\Service\Course\Report\General;

use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Nps;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Service\Course\Common\Stats\StatsGeneralService;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Service\Course\GlobalFilter;
use App\Service\StatsUser\ResultGameService;
use App\Utils\SpreadsheetUtil;
use App\Utils\TimeUtils;
use App\Utils\ToolsUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class CourseReportService extends BaseStatsService
{
    protected EntityManagerInterface $em;

    protected TranslatorInterface $trans;

    private string $baseDir;
    private $locale = 'es';

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        TranslatorInterface $translator,
        UserCourseService $userCourseService,
        RequestStack $requestStack,
        GlobalFilter $globalFilter,
        ResultGameService $resultGameService,
        StatsGeneralService $statsGeneralService
    ) {
        parent::__construct($em, $userCourseService, $requestStack, $globalFilter, $resultGameService, $statsGeneralService);
        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'courses';
        $this->trans = $translator;
    }

    public function getStats(Course $course, ?Announcement $announcement = null): array
    {
        return $this->getStructureStats($course);
    }

    public function getStatsUserChaperCourse(Course $course): array
    {
        return $this->getStructureStatsUserChapterCourse($course);
    }

    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    public function setParams(array $params = [])
    {
        $this->params = $params;
    }

    public function getChaptersParams(): array
    {
        return $this->params['chapters'] ?? [];
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function getBaseDir(): string
    {
        $baseDir = $this->baseDir;
        if (isset($this->params['baseDir'])) {
            $baseDir = $this->params['baseDir'];
        }

        return $baseDir;
    }

    public function courseTotalTimeAllChapter(Course $course, array $userIds): int
    {
        $courseTotalTime = 0;
        foreach ($course->getChapters() as $chapter) {
            $queryParamsChapter = ChapterQueryParams::create([
                'chapter' => $chapter,
                'users' => $userIds,
                'announcementId' => $this->getAnnouncementByParams() ? $this->getAnnouncementByParams()->getId() : null,
            ]);

            $timeSpent = $this->em->getRepository(UserCourseChapter::class)
                ->getTimeByChapter($queryParamsChapter);
            $courseTotalTime += $timeSpent['totalTime'];
        }

        return $courseTotalTime;
    }

    public function courseExcelDetailsGetInfoArray(Course $course, array $userIds): array
    {
        $announcement = $this->getAnnouncementByParams();
        $average_time = '-';
        $totalStarted = $this->userCourseService->courseGetTotalUsersStarted($course, true, $userIds, $announcement);
        $totalFinished = $this->userCourseService->courseGetTotalUsersFinished($course, true, $userIds, $announcement);
        $nps = $this->getNpsAvgCourse($course);
        $totalUsers = \count($this->getUsers($course));

        $courseTotalTime = $this->courseTotalTimeAllChapter($course, $userIds);

        if (\count($userIds) > 0 && $courseTotalTime > 0 && $totalStarted > 0 && $totalFinished > 0) {
            $average_time = TimeUtils::formatTime(round(($courseTotalTime * ($totalFinished + $totalStarted)) /
                (\count($userIds) * ($totalFinished + $totalStarted)), 0));
        }

        // Calculos correspondientes..
        $notStarted = \count($userIds) > 0
            ? round(\count($userIds) - $totalStarted - $totalFinished, 1) : 0;
        $notStarted_percent = \count($userIds) > 0
            ? round(((\count($userIds) - $totalStarted - $totalFinished) * 100) /
                \count($userIds), 1) . '%' : '0%';
        $started_percent = $totalUsers > 0 ? round($totalStarted * 100 / $totalUsers, 1) . '%' : '0%';
        $finished_percent = $totalUsers > 0 ? round($totalFinished * 100 / $totalUsers, 1) . '%' : '0%';

        return [
            'id' => $course->getId(),
            'name' => $course->getName(),
            'nActivities' => $course->getChapters()->count(),
            'nUsers' => \count($userIds),
            'notStarted' => $notStarted,
            'notStarted_percent' => $notStarted_percent,
            'started' => $totalStarted ?? 0,
            'started_percent' => $started_percent,
            'finished' => $totalFinished ?? 0,
            'finished_percent' => $finished_percent,
            'time_total' => $courseTotalTime > 0 ? TimeUtils::formatTime($courseTotalTime) : '-',
            'average_time' => round((float) $average_time, 2),
            'rating' => $nps ? $nps['nps'] : '-',
            'created_by' => $course->getCreatedBy()->getEmail(),
            'created_at' => $course->getCreatedAt()->format('d/m/Y H:i:s'),
        ];
    }

    public function getLastStartDateCourse(Course $course)
    {
        $result = $this->em->getRepository(UserCourse::class)->createQueryBuilder('uc')
            ->select('uc.startedAt')
            ->where('uc.course = :course')
            ->setParameter('course', $course)
            ->orderBy('uc.startedAt', 'DESC')
            ->getQuery()
            ->getArrayResult();

        if ($result) {
            return $result[0]['startedAt']->format('d/m/Y');
        } else {
            return $course->getCreatedAt()->format('d/m/Y');
        }
    }

    public function getCompleteNameUser(int $user_id): string
    {
        $user = $this->em->getRepository(User::class)->find($user_id);
        $completeName = $user->getId();
        $completeName .= '-' . ToolsUtils::str_without_accents(trim($user->getFullName()));

        return $completeName;
    }

    public function fetchCourseChapterIds(Course $course): array
    {
        return $course->getChapters()->map(function ($chapter) {
            return $chapter->getId();
        })->toArray();
    }

    public function getAttempsAndNQuestions(?int $ucc_id = null, ?int $idChapter = null)
    {
        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->findOneBy([
            'id' => $ucc_id
        ]);

        $chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

        if (!$userCourseChapter || !$chapter) {
            return [];
        }

        $gameResult = $this->resultGameService->getResultGameAttemptsWithParams($userCourseChapter, $chapter->getType());
        $data = [
            'totalItems' => 0,
            'totalAttemps' => 0,
        ];

        if ($gameResult) {
            foreach ($gameResult as $result) {
                $data['totalAttemps'] = $result['attempt'];
                $data['totalItems'] = \count($result['questions']) ?? 1;
            }
        }

        return $data;
    }

    private function answerQuestions($question)
    {
        if (\is_null($question['answers'])) {
            return false;
        }

        $answers = $question['answers'];
        $result = false;
        foreach ($answers as $answer) {
            if ($answer['correct'] ?? false) {
                $result = true;
            }
        }

        return $result;
    }

    private function getChapterTypeClassName(string $chapterType): string
    {
        $typeName = '';
        if ('game' == $chapterType) {
            $typeName = $this->getTransReport('chapter_type.games_test', 'chapters');
        }

        if ('content' == $chapterType) {
            $typeName = $this->getTransReport('chapter_type.content', 'chapters');
        }

        return $typeName;
    }

    public function getAnnouncementByParams(): ?Announcement
    {
        $params = $this->params;
        if (isset($params['announcementId']) && $params['announcementId'] > 0) {
            $announcement = $this->em->getRepository(Announcement::class)->find($params['announcementId']);
        } else {
            $announcement = null;
        }

        return $announcement;
    }

    public function getTransReport($wordTranslate, $tag = 'reports'): string
    {
        return $this->trans->trans($wordTranslate, [], $tag, $this->locale);
    }

    public function getCourseSaveFilesDirectoryName(Course $course): string
    {
        return $this->getBaseDir() . DIRECTORY_SEPARATOR . $this->getCourseDirectoryName($course);
    }

    public function getCourseDirectoryName(?Course $course = null): string
    {
        return $course->getId() . '-' . ToolsUtils::str_without_accents(trim($course->getName()));
    }

    public function getUserSaveFileDirectoryName(?Course $course = null, array $params = []): string
    {
        $directoryComplete = $this->getCompleteNameUser($params['userId']);
        $directoryComplete .= '-curso-' . $this->getCourseDirectoryName($course);

        return $this->getBaseDir() . DIRECTORY_SEPARATOR . $directoryComplete;
    }

    public function createSpreadSheet(string $fileName, string $title = 'DEFAULT'): SpreadsheetUtil
    {
        return new SpreadsheetUtil(
            $this->getTransReport($fileName),
            $this->getTransReport($title)
        );
    }

    public function getChaptersByCourse(Course $course, array $chaptersIds)
    {
        return $this->em->getRepository(Chapter::class)->getChaptersByCourse($course, $chaptersIds);
    }

    public function getDataInfoCourse($course)
    {
        return $this->em->getRepository(Course::class)->find($course);
    }

    public function getDataPagesGeneralInfo(array $dataInfo): array
    {
        $data = [];
        foreach ($dataInfo as $info) {
            $data[] = [
                'id' => $info['id'],
                'name' => $info['name'],
                'type' => $info['type'],
                'typeChapter' => $this->getChapterTypeClassName($info['class']),
                'TotalQuestion' => !empty($info['questions']) && \count($info['questions']) > 0 ? \count($info['questions']) : '-',
                'time' => $info['time'] > 0 ? TimeUtils::formatTime($info['time']) : '-',
                'finished' => $info['finished'] > 0 ? $info['finished'] : '-'
            ];
        }

        return $data;
    }

    public function getDataCoursePagesGeneralInfo(array $dataInfo): array
    {
        $data = [];
        foreach ($dataInfo as $info) {
            $data[] = [
                'id' => $info['id'] ?? 0,
                'name' => $info['name'] ?? '',
                'type' => $info['type'] ?? '',
                'typeChapter' => $this->getChapterTypeClassName($info['class'] ?? ''),
                'finished' => isset($info['finished']) && $info['finished'] > 0 ? $info['finished'] . '%' : '-',
                'time' => isset($info['time']) && $info['time'] > 0 ? TimeUtils::formatTime($info['time']) : '-'
            ];
        }

        return $data;
    }

    /**FUNCIONES NUEVAS APARTIR DE AQUI....--->**/
    public function getChapertsDetailsEvaluationData($questions, &$headerAnswer): array
    {
        $dataInfoEvaluations = [];
        foreach ($questions as $dataInfo) {
            $porcentAcerts = ($dataInfo['totalSuccess'] / ($dataInfo['totalSuccess'] + $dataInfo['totalErrors'])) * 100;
            $toDataInsert = [
                'id' => $dataInfo['id'],
                'question' => $dataInfo['question'],
                'correctAnswer' => $dataInfo['correctAnswer'],
                'porcentAcerts' => $porcentAcerts > 0 ? round($porcentAcerts, 1) . '%' : '-',
            ];

            if (!\is_null($dataInfo['answers'])) {
                $answers = $dataInfo['answers'];
                if ($headerAnswer < \count($answers)) {
                    $headerAnswer = \count($answers);
                }

                foreach ($answers as $key => $answer) {
                    $toDataInsert['ANSWER_' . $key] = $answer['answer'] ?? '-';
                }
            }

            $dataInfoEvaluations[] = $toDataInsert;
        }

        return $dataInfoEvaluations;
    }

    public function getUserChapterCourseEvaluationData($dataInfoUsers): array
    {
        $userDatainfo = [];

        foreach ($dataInfoUsers as $info) {
            foreach ($info as $row) {
                $userDatainfo[] = [
                    'USER_ID' => $row['USER_ID'],
                    'USER_CODE' => $row['USER_CODE'],
                    'FIRST_NAME' => $row['FIRST_NAME'],
                    'LAST_NAME' => $row['LAST_NAME'],
                    'EMAIL' => $row['EMAIL'],
                    'ATTEMPT' => $row['ATTEMPT'],
                    'QUESTION_ID' => $row['QUESTION_ID'],
                    'QUESTION' => $row['QUESTION'] ?? '',
                    'RESULT' => $row['RESULT'] ? $this->getTransReport('report.excel.course_details.course_statics.yes') :
                        $this->getTransReport('report.excel.course_details.course_statics.no'),
                ];
            }
        }

        return $userDatainfo;
    }

    public function getNpsAvgCourse(Course $course)
    {
        try {
            $nps = $this->em->getRepository(Nps::class)->getNpsAvgCourse($course);
        } catch (\Exception $e) {
            $nps = null;
        }

        return $nps;
    }

    public function getCourseStatisticsData(Course $course, array $userIds): array
    {
        $QB_courseStatisticsResultSet = new Query\ResultSetMapping();
        $QB_courseStatisticsResultSet->addScalarResult('userId', 'userId', 'integer');
        $QB_courseStatisticsResultSet->addScalarResult('userCode', 'userCode');
        $QB_courseStatisticsResultSet->addScalarResult('firstName', 'firstName');
        $QB_courseStatisticsResultSet->addScalarResult('lastName', 'lastName');
        $QB_courseStatisticsResultSet->addScalarResult('email', 'email');
        $QB_courseStatisticsResultSet->addScalarResult('active', 'active');
        $QB_courseStatisticsResultSet->addScalarResult('courseLocale', 'courseLocale');
        $QB_courseStatisticsResultSet->addScalarResult('started_at', 'startedAt', 'datetime');
        $QB_courseStatisticsResultSet->addScalarResult('finished_at', 'finishedAt', 'datetime');
        $QB_courseStatisticsResultSet->addScalarResult('timeSpent', 'timeSpent', 'integer');
        $QB_courseStatisticsResultSet->addScalarResult('status', 'status');
        // $QB_courseStatisticsResultSet->addScalarResult('points', 'points', 'integer');

        $chaptersIds = $this->getChaptersParams();
        $data = $this->em->getRepository(Chapter::class)
            ->createUsersCourseStatisticsNativeQuery($course, $QB_courseStatisticsResultSet, $chaptersIds, $userIds);

        return $data;
    }

    public function courseFormativeActivityData(Course $course, array $userIds)
    {
        $chaptersIds = $this->getChaptersParams();
        $announcement = $this->getAnnouncementByParams();

        return $this->em->getRepository(User::class)
            ->userCourseFormativeActivityGetQueryBuilder($course, $userIds, $chaptersIds, $announcement);
    }

    public function courseFormativeActivityPagination($baseQb, $offset, $pageSize)
    {
        return $this->em->getRepository(User::class)
            ->userCourseFormativeActivityDataPaginated($baseQb, $offset, $pageSize);
    }

    public function courseGetTotalChaptersStarted(Course $course, array $usersIds = [])
    {
        $announcement = $this->getAnnouncementByParams();
        $repository = $this->em->getRepository(UserCourseChapter::class);

        return $repository->courseGetTotalChapterStarted($course, $usersIds, $announcement);
    }

    public function courseGetTotalChaptersFinished(Course $course, array $usersIds = []): int
    {
        $announcement = $this->getAnnouncementByParams();
        $repository = $this->em->getRepository(UserCourseChapter::class);

        return $repository->courseGetTotalChapterFinished($course, $usersIds, $announcement);
    }

    public function courseExcelDetailsGetInfoPeopleArray(Course $course, array $userIds): array
    {
        $totalStarted = $this->courseGetTotalChaptersStarted($course, $userIds);
        $totalFinished = $this->courseGetTotalChaptersFinished($course, $userIds);
        $nps = $this->getNpsAvgCourse($course);
        $notStarted = $course->getChapters()->count() - $totalStarted - $totalFinished;

        $courseTotalTime = $this->courseTotalTimeAllChapter($course, $userIds);

        return [
            'id' => $course->getId(),
            'name' => $course->getName(),
            'nActivities' => $course->getChapters()->count(),
            'notStarted' => $notStarted > 0 ? $notStarted : '0',
            'started' => '' != $totalStarted ? $totalStarted : '0',
            'finished' => '' != $totalFinished ? $totalFinished : '0',
            'time_total' => $courseTotalTime > 0 ? TimeUtils::formatTime($courseTotalTime) : '-',
            'rating' => $nps ? round((float) $nps['nps'], 2) : '-',
            'created_by' => $course->getCreatedBy()->getEmail(),
            'created_at' => $course->getCreatedAt()->format('d/m/Y H:i:s'),
        ];
    }

    public function getInfoQuestion($userCourseInfo, &$toInsert, &$headerAnswer)
    {
        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->findOneBy(['id' => $userCourseInfo['ucc_id']]);
        if ($userCourseChapter) {
            $chapter = $this->em->getRepository(Chapter::class)->find($userCourseInfo['chapterId']);
            $gameResult = $this->resultGameService->getResultGameAttemptsWithParams($userCourseChapter, $chapter->getType());

            $chapterType = $chapter->getType();
            $chapterTypeName = $chapterType->getNormalized();

            if ($gameResult) {
                foreach ($gameResult as $result) {
                    $attempt = 1;
                    if (isset($result['attempt'])) {
                        $attempt = $result['attempt'];
                    }
                    foreach ($result['questions'] as $question) {
                        if ('FillGaps' === $chapterTypeName && isset($question['question']) && \is_string($question['question'])) {
                            $question['question'] = $this->filterFillGapsQuestion($question['question']);
                        }
                        $toDataInsert = [
                            'USER_ID' => $userCourseInfo['userId'],
                            'USER_CODE' => $userCourseInfo['userCode'],
                            'USER_NAME' => $userCourseInfo['firstName'],
                            'LAST_NAME' => $userCourseInfo['lastName'],
                            'USER_EMAIL' => $userCourseInfo['email'],
                            'ATTEMPT_ID' => $attempt,
                            'QUESTION_ID' => $question['id'],
                            'QUESTION' => $question['question'] ?? '-',
                            'RESULT' => $this->answerQuestions($question) ?
                                $this->getTransReport('report.excel.course_details.course_statics.yes') :
                                $this->getTransReport('report.excel.course_details.course_statics.no'),
                        ];
                        if (!\is_null($question['answers'])) {
                            $answers = $question['answers'];
                            if ($headerAnswer < \count($answers)) {
                                $headerAnswer = \count($answers);
                            }
                            foreach ($answers as $key => $answer) {
                                if ($answer['correct'] ?? false) {
                                    $toDataInsert['CORRECT_ANSWER'] = $answer['answer'] ?? '-';
                                }
                                $toDataInsert['ANSWER_' . $key] = $answer['answer'] ?? '-';
                            }
                        }
                        $toInsert[] = $toDataInsert;
                    }
                }
            }
        }
    }

    private function filterFillGapsQuestion(string $question): string
    {
        return preg_replace('/\[id\d+\-[a-zA-Z]+\]/', '______', $question);
    }
}
