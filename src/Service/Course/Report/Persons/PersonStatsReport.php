<?php

declare(strict_types=1);

namespace App\Service\Course\Report\Persons;

use App\Entity\Course;
use App\Service\Course\Report\General\BaseReport;
use App\Service\Course\Report\General\CourseReportService;
use App\Service\Course\Traits\CourseReportTrait;
use App\Utils\SpreadsheetUtil;
use App\Utils\TimeUtils;
use App\Utils\ToolsUtils;
use Doctrine\ORM\QueryBuilder;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Psr\Log\LoggerInterface;

class PersonStatsReport extends BaseReport
{
    use CourseReportTrait;
    private CourseReportService $courseReportService;
    private LoggerInterface $logger;

    public function __construct(
        CourseReportService $courseReportService,
        LoggerInterface $logger
    ) {
        $this->courseReportService = $courseReportService;
        $this->logger = $logger;
    }

    public function setDefaultData(Course $course, array $params = []) : void
    {
        $params['chapters'] = $this->courseReportService->fetchCourseChapterIds($course);
        $this->courseReportService->setLocale($course->getLocale());
        $this->courseReportService->setParams($params);
        $this->params = $params;
    }

    public function generate(?Course $course = null, array $params = []): string
    {
        $this->logger->info('Personal_Generate_Report[Started]');

        if (null === $course) {
            throw new \RuntimeException('Course not found');
        }

        $this->setDefaultData($course, $params);

        $userIds[] = $params['userId'];

        $this->personReport($course, $params, $userIds);

        $this->logger->info('Personal_Generate_Report[Completed]');

        return $this->courseReportService->getUserSaveFileDirectoryName($course, $params);
    }

    public function personReport(Course $course, array $params, array $userIds) : void
    {       
        /** @var SpreadsheetUtil $report */
        $filename = !empty($params['itineraryId']) ? ToolsUtils::str_without_accents(trim($course->getName())) : 'report.excel.chapter_detail_user';
        $report = $this->courseReportService->createSpreadSheet(
            $filename,
            'report.excel.course_details.title'
        );

        $this->courseExcelConfigStats($report, $course);
        $this->courseExcelDetailsInfoPeople($report, $course, $userIds);
        $this->courseExcelChapterListInfoUser($report, $course, $userIds);
        $this->courseExcelEvaluationActivityForReporUser($report, $course, $userIds);

        $report->saveReport($this->courseReportService->getUserSaveFileDirectoryName($course, $params));
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function courseExcelConfigStats(SpreadsheetUtil $report, Course $course) : void
    {
        $params = $this->params;
        $report->addSheet($this->getTransReport('report.excel.course_details.config'));
        $dateFrom = $course->getCreatedAt()->format('d/m/Y');
        $dateFromDisplay = false;
        if (isset($params['dateFrom'])) {
            $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $params['dateFrom']);
            $dateFrom = $dateFrom->format('d/m/Y');
            $dateFromDisplay = true;
        }

        $dateTo = $this->courseReportService->getLastStartDateCourse($course);
        $datetodisplay = false;
        if (isset($params['dateTo'])) {
            $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $params['dateTo']);
            $dateTo = $dateTo->format('d/m/Y');
            $datetodisplay = true;
        }

        $report->mergeCells('A2:C2')
            ->setCellValue(
                'A2',
                $this->getTransReport('report.excel.course_details.config.title_config'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->mergeCells('A4:E4')
            ->setCellValue(
                'A4',
                $this->getTransReport('report.excel.course_details.config.period'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->setCellValue(
            'A6',
            $this->getTransReport('report.excel.course_details.config.from'),
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B6',
            $dateFrom,
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'A7',
            $this->getTransReport('report.excel.course_details.config.until'),
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B7',
            $dateTo,
            ['fontSize' => 14]
        );

        $report->mergeCells('A10:E10')
            ->setCellValue(
                'A10',
                $this->getTransReport('report.excel.course_details.config.filter'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->setCellValue(
            'A12',
            'CourseID',
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B12',
            $course->getId(),
            ['fontSize' => 14]
        );

        if (isset($params['userId'])) {
            $report->setCellValue(
                'A13',
                $this->getTransReport('stats.export.filter.activeUsers', 'messages'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B13',
                $this->getTransReport('stats.export.filter.activeUsers_val_yes', 'messages'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'A14',
                $this->getTransReport('report.excel.course_details.config.range_date_start'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B14',
                $dateFromDisplay ?
                    $this->getTransReport('report.excel.course_details.course_statics.yes') :
                    $this->getTransReport('report.excel.course_details.course_statics.no'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'A15',
                $this->getTransReport('report.excel.course_details.config.end_range_date'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B15',
                $datetodisplay ?
                    $this->getTransReport('report.excel.course_details.course_statics.yes') :
                    $this->getTransReport('report.excel.course_details.course_statics.no'),
                ['fontSize' => 14]
            );

            $report->setAutosize('A', 'D');
            $report->setCellAlignmentHorizontal(['A2', 'A4', 'A10'], Alignment::HORIZONTAL_CENTER);
        }
    }

    private function courseExcelDetailsInfoPeopleHeaders() : array
    {
        return [
            $this->getTransReport('report.excel.course_details.general.id'),
            $this->getTransReport('report.excel.course_details.general.name'),
            $this->getTransReport('report.excel.course_details.general.activities'),
            $this->getTransReport('report.excel.course_details.general.not_started'),
            $this->getTransReport('report.excel.course_details.general.in_progress'),
            $this->getTransReport('report.excel.course_details.general.finalized'),
            $this->getTransReport('report.excel.course_details.general.total_time'),
            $this->getTransReport('report.excel.course_details.general.average'),
            $this->getTransReport('report.excel.course_details.general.authorship'),
            $this->getTransReport('report.excel.course_details.general.create_date')
        ];
    }

    private function courseExcelDetailsInfoPeopleFills() : array
    {
        return [
            'A' => 'f4cccc',
            'B' => 'a4c2f4',
            'C' => 'd9d2e9',
            'D' => 'e6b8aF',
            'E' => 'd9ead3',
            'F' => 'd9d2e9',
            'G' => 'd9ead3',
            'H' => 'b4a7d6',
            'I' => 'fff2cc',
            'J' => 'f4cccc'
        ];
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function courseExcelDetailsInfoPeople(SpreadsheetUtil $report, Course $course, array $userIds) : void
    {
        $report->addSheet($this->getTransReport('report.excel.course_details.general'), true);

        $report->setHeaders(
            $this->courseExcelDetailsInfoPeopleHeaders(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => $this->courseExcelDetailsInfoPeopleFills(),
            ]
        );
        $report->setCellAlignmentHorizontal('A', Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'], Alignment::HORIZONTAL_CENTER);
        $courseInfo = $this->courseReportService->courseExcelDetailsGetInfoPeopleArray($course, $userIds);
        $report->fromArray([$courseInfo], '--', 'A2');
    }

    private function courseExcelChapterListInfoUserHeaders() : array
    {
        return [
            $this->getTransReport('report.excel.course_details.course_statics.id_user'),
            $this->getTransReport('report.excel.headers.participants.first_name'),
            $this->getTransReport('report.excel.course_details.course_statics.surnames'),
            $this->getTransReport('report.excel.headers.participants.email'),
            $this->getTransReport('report.excel.course_details.chapters.activity'),
            $this->getTransReport('report.excel.course_details.chapters.name'),
            $this->getTransReport('report.excel.course_details.chapters.type'),
            $this->getTransReport('report.excel.course_details.chapters.class'),
            $this->getTransReport('report.excel.chapter_detail.general_info.items'),
            $this->getTransReport('report.excel.headers.attemps'),
            $this->getTransReport('report.excel.headers.announcement.start_date'),
            $this->getTransReport('report.excel.headers.announcement.end_date'),
            $this->getTransReport('report.excel.headers.announcement.status'),
            $this->getTransReport('report.excel.course_details.general.total_time')
        ];
    }

    private function courseExcelChapterListInfoUserFills() : array
    {
        return [
            'A' => 'f4cccc',
            'B' => 'a4c2f4',
            'C' => 'd9d2e9',
            'D' => 'fce5cd',
            'E' => 'e6b8af',
            'F' => 'e6b8af',
            'G' => 'c9daf8',
            'H' => 'c9daf8',
            'I' => 'd9d2e9',
            'J' => 'd9d2e9',
            'k' => 'd9ead3',
            'L' => 'b6d7a8',
            'M' => 'b4a7d6',
            'N' => 'fff2cc',
        ];
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function courseExcelChapterListInfoUser(SpreadsheetUtil $report, Course $course, array $userIds) : void
    {
        $baseQb = $this->courseReportService->courseFormativeActivityData($course, $userIds);
        if($baseQb->getQuery()->setMaxResults(1)->getOneOrNullResult()){
            $report->addSheet($this->getTransReport('report.excel.course_details.chapters'), true);
            $report->setHeaders(
                $this->courseExcelChapterListInfoUserHeaders(),
                true,
                true,
                true,
                1,
                [
                    'fontSize' => 14,
                    'bold' => true,
                    'color' => '000000',
                    'fill' => $this->courseExcelChapterListInfoUserFills(),
                ]
            );

            $report->setCellAlignmentHorizontal(['A', 'E'], Alignment::HORIZONTAL_LEFT);
            $report->setCellAlignmentHorizontal(['I', 'J', 'K', 'L', 'M', 'N'], Alignment::HORIZONTAL_CENTER);
            $this->getDataListChaptersForReporUser($report, $course, $userIds, $baseQb);
        }
    }

    private function getDataListChaptersForReporUser(SpreadsheetUtil $report, Course $course, array $userIds, QueryBuilder $baseQb): void
    {

        $offset = 0;
        $pageSize = 500;

        $row = 2;
        do {
            $lastRow = null;
            $toInsert = [];
            /** @var array $userCourseInfo */
            $paginatedData = $this->courseReportService->courseFormativeActivityPagination($baseQb, $offset, $pageSize);
            foreach ($paginatedData as $userCourseInfo) {
                /** @var \DateTimeImmutable|null $started */
                $started = $userCourseInfo['startedAt'] ?? null;
                /** @var \DateTimeImmutable|null $finished */
                $finished = $userCourseInfo['finishedAt'] ?? null;

                $typeName = '';
                if ('game' == $userCourseInfo['typeChapter']) {
                    $typeName = $this->getTransReport('chapter_type.games_test', 'chapters');
                }

                if ('content' == $userCourseInfo['typeChapter']) {
                    $typeName = $this->getTransReport('chapter_type.content', 'chapters');
                }

                $uccId = $userCourseInfo['ucc_id'];
                $chapter_Id = $userCourseInfo['chapterId'];
                $status = $this->getTransReport('report.excel.course_details.course_statics.' . $userCourseInfo['status']);
                $data = $this->courseReportService->getAttempsAndNQuestions($uccId, $chapter_Id);

                $toInsert[] = [
                    'USER_ID' => $userCourseInfo['userId'],
                    'USER_NAME' => $userCourseInfo['firstName'],
                    'LAST_NAME' => $userCourseInfo['lastName'],
                    'USER_EMAIL' => $userCourseInfo['email'],
                    'CHAPTER_ID' => $userCourseInfo['chapterId'],
                    'CHAPTER_NAME' => $userCourseInfo['chapterName'],
                    'CHAPTER_TYPE' => $userCourseInfo['chapterType'],
                    'TYPE_CLASS' => $typeName,
                    'NUMBER_ITEMS' => $data['totalItems'] ?? '-',
                    'NUMBER_ATTEMPS' => $data['totalAttemps'] ?? '-',
                    'STARTED' => $started ? $started->format('d-m-Y H:i:s') : '-',
                    'FINISHED' => $finished ? $finished->format('d-m-Y H:i:s') : '-',
                    'STATUS' => $status,
                    'TOTAL_TIME' => TimeUtils::formatTime($userCourseInfo['timeSpent']) ?? '-',
                ];
                $lastRow = empty($userCourseInfo) ? null : $userCourseInfo;
            }
            ++$offset;
            $report->fromArray($toInsert, '--', "A$row");
            $row = $report->getLastRow();
        } while (null !== $lastRow);
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function courseExcelEvaluationActivityForReporUser(SpreadsheetUtil $report, Course $course, array $userIds) : void
    {
        $baseQb = $this->courseReportService->courseFormativeActivityData($course, $userIds);
        $offset = 0;
        $pageSize = 500;

        $row = 2;
        do {
            $lastRow = null;
            $toInsert = [];
            /** @var array $userCourseInfo */
            $paginatedData = $this->courseReportService->courseFormativeActivityPagination($baseQb, $offset, $pageSize);
            $headerAnswer = 0;

            foreach ($paginatedData as $userCourseInfo) {
                $this->courseReportService->getInfoQuestion($userCourseInfo, $toInsert, $headerAnswer);
                $lastRow = empty($userCourseInfo) ? null : $userCourseInfo;
            }
            ++$offset;
            if(!empty($toInsert)){
                $this->courseExcelEvaluationActivityInfoUser($report, $headerAnswer);
                $report->fromArray($toInsert, '--', "A$row");
                $row = $report->getLastRow();
            }

        } while (null !== $lastRow);
    }

    private function courseExcelEvaluationActivityInfoUserHeaders() : array
    {
        return [
            $this->getTransReport('report.excel.course_details.course_statics.id_user'),
            $this->getTransReport('report.excel.course_details.course_statics.code'),
            $this->getTransReport('report.excel.headers.participants.first_name'),
            $this->getTransReport('report.excel.course_details.course_statics.surnames'),
            $this->getTransReport('report.excel.headers.participants.email'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_activity.attemptId'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.idquestion'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.question'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_activity.right')
        ];
    }

    private function courseExcelEvaluationActivityInfoUserFills() : array
    {
        return [
            'A' => 'f4cccc',
            'B' => 'a4c2f4',
            'C' => 'd9d2e9',
            'D' => 'fce5cd',
            'E' => 'e6b8af',
            'F' => 'e6b8af',
            'G' => 'c9daf8',
            'H' => 'c9daf8',
            'I' => 'd9d2e9'
        ];
    }

    private function courseExcelEvaluationActivityInfoUser(SpreadsheetUtil $report, int $headerAnswer) : void
    {
        $report->addSheet($this->getTransReport('report.excel.chapter_detail.evaluation_activity'), true);
        $headers = $this->courseExcelEvaluationActivityInfoUserHeaders();

        $fill = $this->courseExcelEvaluationActivityInfoUserFills();

        if ($headerAnswer > 0) {
            $headers[] = $this->getTransReport('report.excel.chapter_detail.evaluation_detail.correct_answer');
            $i = 1;
            $columns = range('J', 'Z');
            $colorV = 185;
            $fill[$columns[0]] = 'e6' . dechex($colorV) . 'af';
            $report->setCellAlignmentHorizontal($columns[0], Alignment::HORIZONTAL_LEFT);
            while ($i <= $headerAnswer) {
                $colorV = 255 == $colorV ? 185 : ($colorV + 10);
                $headers[] = $this->getTransReport('report.excel.chapter_detail.evaluation_detail.answers') . ' ' . $i;
                $fill[$columns[$i]] = 'e6' . dechex($colorV) . 'af';
                $report->setCellAlignmentHorizontal($columns[$i], Alignment::HORIZONTAL_LEFT);
                ++$i;
            }
        }

        $report->setHeaders($headers, true, true, true, 1, [
            'fontSize' => 14,
            'bold' => true,
            'color' => '000000',
            'fill' => $fill,
        ]);

        $report->setCellAlignmentHorizontal(['A', 'B'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['F', 'G', 'I'], Alignment::HORIZONTAL_CENTER);
    }

    private function getTransReport(string $wordTranslate, string $tag = 'reports'): string
    {
        return $this->courseReportService->getTransReport($wordTranslate, $tag);
    }
}
