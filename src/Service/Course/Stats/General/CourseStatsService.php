<?php

declare(strict_types=1);

namespace App\Service\Course\Stats\General;

use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\UserCourseChapter;
use App\Repository\ChapterRepository;
use App\Repository\ItineraryRepository;
use App\Service\Course\Common\Stats\StatsGeneralService;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\GlobalFilter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class CourseStatsService extends BaseStatsService
{
    private ChapterRepository $chapterRepository;
    private ItineraryRepository $itineraryRepository;

    public function __construct(
        EntityManagerInterface $em,
        UserCourseService $userCourseService,
        RequestStack $requestStack,
        GlobalFilter $globalFilter,
        StatsGeneralService $statsGeneralService,
        ChapterRepository $chapterRepository,
        ItineraryRepository $itineraryRepository
    ) {
        parent::__construct($em, $userCourseService, $requestStack, $globalFilter, $statsGeneralService);
        $this->chapterRepository = $chapterRepository;
        $this->itineraryRepository = $itineraryRepository;
    }

    public function getStats(Course $course, ?Announcement $announcement = null, ?Itinerary $itinerary = null): array
    {
        return $this->getStructureStats($course, $itinerary);
    }

    public function getChaptersStats(Course $course, ?Itinerary $itinerary = null): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $pageSize = $request->query->getInt('limit', 10);
        $page = $request->query->getInt('page', 1);

        $isItinerary = null !== $itinerary;
        $chaptersPaginated = $this->chapterRepository->loadCourseChaptersPaginated($course, $page, $pageSize);

        if (empty($chaptersPaginated['chapters'])) {
            return [
                'chapters' => [],
                'pagination' => [
                    'totalPages' => 0,
                    'totalItems' => 0
                ]
            ];
        }

        $users = $this->getUsers($course, $itinerary);
        $courseParams = $this->getParamsCourse($course, $users);

        $chapters = $chaptersPaginated['chapters'];

        $chaptersData = [];
        foreach ($chapters as $chapterEntity) {
            $chapterQueryParams = $this->getChapterQueryParameters($chapterEntity, $users, $courseParams);

            if (!empty($users) && $isItinerary) {
                $chapterQueryParams->findUsers = true;
            }

            $progressMetrics = $this->statsGeneralService->calculateChapterProgressMetrics($chapterQueryParams);
            $progress = $progressMetrics['inProgress'] ?? 0;
            $finished = $progressMetrics['finished'] ?? 0;

            $isShowView = $chapterEntity->getType()->isGame() && ($progress > 0 || $finished > 0);

            $chaptersData[] = array_merge([
                'id' => $chapterEntity->getId(),
                'name' => $chapterEntity->getTitle(),
                'thumbnail' => $chapterEntity->getImageUrl(),
                'type' => $chapterEntity->getType()->getName(),
                'showView' => $isShowView,
                'icon' => $chapterEntity->getType()->getIcon(),
            ], $progressMetrics);
        }

        return [
            'chapters' => $chaptersData,
            'pagination' => [
                'totalPages' => $chaptersPaginated['totalPages'],
                'totalItems' => $chaptersPaginated['totalItems']
            ]
        ];
    }

    public function getChapterStats(Chapter $chapter, ?Itinerary $itinerary = null): array
    {
        $users = $this->getUsers($chapter->getCourse(), $itinerary);
        $courseParams = $this->getParamsCourse($chapter->getCourse(), $users);
        $chapterQueryParams = $this->getChapterQueryParameters($chapter, $users, $courseParams);

        $triesAndQuestions = $this->statsGeneralService->chaptersStatsGetChapterData($chapterQueryParams);

        

        return $triesAndQuestions;
    }

    public function getCourseSummary(Course $course, ?Itinerary $itinerary = null): array
    {
        $users = $this->getUsers($course, $itinerary);
        if (empty($users)) {
            return [
                'totalUsers' => 0,
                'started' => 0,
                'finished' => 0,
                'totalTime' => 0
            ];
        }
        $courseParams = $this->getParamsCourse($course, $users);
        $totalTimeCourse = 0;
        foreach ($course->getChapters() as $chapter) {
            $chapterQueryParams = $this->getChapterQueryParameters($chapter, $users, $courseParams);
            $this->getTotalTimeCourse($chapterQueryParams, $totalTimeCourse);
        }
        $usersTotalStarted = $this->userCourseService->courseGetTotalUsersStarted($course, false, $users, $this->getAnnouncement());
        $usersTotalFinished = $this->userCourseService->courseGetTotalUsersFinished($course, false, $users, $this->getAnnouncement());

        return [
            'totalUsers' => \count($users),
            'started' => $usersTotalStarted,
            'finished' => $usersTotalFinished,
            'totalTime' => $totalTimeCourse
        ];
    }

    public function getCoursesByItinerary(Itinerary $itinerary): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $pageSize = $request->query->getInt('limit', 10);
        $page = $request->query->getInt('page', 1);

        $courses = $this->itineraryRepository->getCoursesItineraries($itinerary, $page, $pageSize);
        if (empty($courses['courses'])) {
            return [
                'courses' => [],
                'pagination' => [
                    'totalPages' => 0,
                    'totalItems' => 0
                ]
            ];
        }

        $data = [];
        $users = $this->getUserByItinerary($itinerary);

        foreach ($courses['courses'] as $course) {
            $courseEntity = $this->em->getRepository(Course::class)->find($course['id']);
            $time = 0;
            $courseParams = $this->getParamsCourse($courseEntity, $users);

            

            foreach ($courseEntity->getChapters() as $chapter) {
                $chapterQueryParams = $this->getChapterQueryParameters($chapter, $users, $courseParams);
                $timeByChapter = $this->em->getRepository(UserCourseChapter::class)->getTimeByChapter($chapterQueryParams);

                // Acumular el tiempo solo si existe
                if ($timeByChapter) {
                    $time += (int) $timeByChapter['totalTime'];
                }
            }

            $statistics = $this->getCourseUserStatistics($courseEntity, $users);

            $data[] = [
                'id' => $courseEntity->getId(),
                'name' => $courseEntity->getName(),
                'thumbnail' => $courseEntity->getImageUrl(),
                'type' => $courseEntity->getTypeCourse()->getName(),
                'inProgress' => $this->calculatePercentage($statistics['totalStarted'], \count($users)),
                'finished' => $this->calculatePercentage($statistics['totalFinished'], \count($users)),
                'totalChapters' => \count($courseEntity->getChapters()),              
                'time' => $time,
            ];

            $time = 0;
        }

        return [
            'courses' => $data,
            'pagination' => [
                'totalPages' => $courses['totalPages'],
                'totalItems' => $courses['totalItems']
            ]
        ];
    }


    private function calculatePercentage(int $value, int $total): float
    {
        if ($total === 0) {
            return 0.0;
        }

        $percentage = ($value / $total) * 100;

        if ($percentage > 0 && $percentage < 1) {
            return round($percentage, 2); 
        }

        return round($percentage);
    }
}
