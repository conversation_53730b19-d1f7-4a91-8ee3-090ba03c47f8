<?php

declare(strict_types=1);

namespace App\Service\Course\Stats\General;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Itinerary;
use App\Entity\UserCourseChapter;
use App\Service\Course\Common\Stats\StatsGeneralService;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Service\Course\GlobalFilter;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

abstract class BaseStatsService
{
    protected EntityManagerInterface $em;
    protected GlobalFilter $globalFilter;
    protected RequestStack $requestStack;
    protected UserCourseService $userCourseService;
    protected StatsGeneralService $statsGeneralService;

    public function __construct(
        EntityManagerInterface $em,
        UserCourseService $userCourseService,
        RequestStack $requestStack,
        GlobalFilter $globalFilter,
        StatsGeneralService $statsGeneralService
    ) {
        $this->em = $em;
        $this->userCourseService = $userCourseService;
        $this->requestStack = $requestStack;
        $this->globalFilter = $globalFilter;
        $this->statsGeneralService = $statsGeneralService;
    }

    abstract public function getStats(Course $course, ?Announcement $announcement = null, ?Itinerary $itinerary = null): array;

    abstract public function getChaptersStats(Course $course, ?Itinerary $itinerary = null): array;

    abstract public function getChapterStats(Chapter $chapter, ?Itinerary $itinerary = null): array;

    abstract public function getCoursesByItinerary(Itinerary $itinerary): array;


    protected function getRequestContent(): array
    {
        $request = $this->requestStack->getCurrentRequest();

        return json_decode($request->getContent(), true) ?? [];
    }

    public function getUsers(Course $course, ?Itinerary $itinerary = null): array
    {
        $announcement = $this->getAnnouncement();

        if ($announcement) {
            return $this->getUsersByAnnouncement($announcement);
        }

        if (!$itinerary) {            
            $content = $this->getRequestContent();
            return $this->userCourseService->getAllUsersIds($course, $content);
        }


        return $this->getUserByItinerary($itinerary);
    }

    protected function getUserByItinerary(Itinerary $itinerary)
    {
        
     
        $usersFiltersIds = [];
        $usersIds = [];
        $itineraryUsers = $itinerary->getItineraryUsers();
        $userInFilters = $this->em->getRepository(Itinerary::class)->getUsersByItineraryFilters($itinerary);
        if (!empty($userInFilters)) {
            foreach ($userInFilters as $user) {
                $usersFiltersIds[] = $user['id'];
            }
        }
        if(\count($itineraryUsers) > 0){
            foreach ($itineraryUsers as $itineraryUser) {
                $usersIds[] = $itineraryUser->getUser()->getId();
            }
        }

        $usersIdsMerge = array_unique(array_merge($usersIds, $usersFiltersIds));

        return $usersIdsMerge;
    }

    protected function getUsersByAnnouncement(Announcement $announcement): array
    {
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)
            ->getUsersByAnnouncement($announcement);

        $users = [];

        foreach ($announcementUsers as $announcementUser) {
            $users[] = $announcementUser->getUser()->getId();
        }

        return $users;
    }

    public function getParamsCourse(Course $course, array $usersIds = [], $isIncludeUsers = true): array
    {
        $chapters = $course->getChapters();
        $content = $this->getRequestContent();
        $dateFrom = null;
        $dateTo = null;
        $courseStartedIntime = filter_var( false, FILTER_VALIDATE_BOOLEAN);
        $courseFinishedIntime = filter_var(false, FILTER_VALIDATE_BOOLEAN);
        $findUsers = false;
        if(!empty($content)){
            $this->globalFilter->cleanFilters($content);
            $courseStartedIntime = filter_var($content['courseStartedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
            $courseFinishedIntime = filter_var($content['courseFinishedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
            if (!empty($content['dateFrom'])) {
                $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateFrom']);
                $courseStartedIntime = true;
            }

            if (!empty($content['dateTo'])) {
                $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateTo']);
                $courseFinishedIntime = true;
            }
            $this->globalFilter->findUsersFilter($content, $findUsers);
        }

        if ($isIncludeUsers) {
            $users = !empty($usersIds) ? $usersIds : $this->getUsers($course);
        } else {
            $users = [];
        }

        return [
            'chapters' => $chapters,
            'findUsers' => $findUsers,
            'usersIds' => $users,
            'courseFinishedIntime' => $courseFinishedIntime,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'courseStartedIntime' => $courseStartedIntime,
            'announcementId' => $content['announcementId'] ?? 0,
        ];
    }

    protected function getAnnouncement(): ?Announcement
    {
        $content = $this->getRequestContent();

        return $this->em->getRepository(Announcement::class)->find($content['announcementId'] ?? 0);
    }

    public function getChapterQueryParameters(Chapter $chapter, array $usersIds = [], array $courseParams = []): ChapterQueryParams
    {
        return ChapterQueryParams::create([
            'chapter' => $chapter,
            'findUsers' => false,
            'users' => $usersIds,
            'dateFrom' => $courseParams['dateFrom'],
            'dateTo' => $courseParams['dateTo'],
            'courseStartedOnTime' => $courseParams['courseStartedIntime'],
            'announcementId' => $courseParams['announcementId'],
            'courseFinishedOnTime' => $courseParams['courseFinishedIntime'],
        ]);
    }

    protected function getStructureStats(Course $course, ?Itinerary $itinerary = null): array
    {
        $isItinerary = (bool)$itinerary;
        $users = $this->getUsers($course, $itinerary);

        $courseParams = $this->getParamsCourse($course, $users);
        $totalTimeCourse = 0;
        $chaptersData = $this->getChaptersCourse($course, $users, $courseParams, $isItinerary, $totalTimeCourse);
        $courseStats = $this->getCourseUserStatistics($course, $users, $totalTimeCourse);
        return array_merge($chaptersData, $courseStats);
    }


    private function getChaptersCourse(Course $course, array $users = [], array $courseParams = [], bool $isItinerary = false, float &$totalTimeCourse): array
    {
        $chapters = [];
        $count = 0;
        $allUsersInChapters = true;
        foreach ($course->getChapters() as $chapter) {
            ++$count;
            $chapterQueryParams = $this->getChapterQueryParameters($chapter, $users, $courseParams);
            if (!empty($users) && $isItinerary)
                $chapterQueryParams->findUsers = true;
            $progressMetrics = $this->statsGeneralService->calculateChapterProgressMetrics($chapterQueryParams, $allUsersInChapters);
            $triesAndQuestions = $this->statsGeneralService->chaptersStatsGetChapterData($chapterQueryParams);
            $this->getTotalTimeCourse($chapterQueryParams, $totalTimeCourse);
            $chapters[] = array_merge(
                [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'image' => $chapter->getImage(),
                    'type' => $chapter->getType()->getName(),
                    'icon' => $chapter->getType()->getIcon()
                ],
                $progressMetrics,
                $triesAndQuestions
            );
        }

        return ['data' => $chapters, 'totalChapters' => $count, 'allUsersInChapters' => $allUsersInChapters];
    }

    public function getTotalTimeCourse(ChapterQueryParams $chapterQueryParams, float &$totalTimeCourse){
        $timeByChapter = $this->em->getRepository(UserCourseChapter::class)->getTimeByChapter($chapterQueryParams);
        $totalTimeCourse += \intval($timeByChapter['totalTime'] ?? 0);
    }


    public function getCourseUserStatistics(Course $course, array $users = [], float $totalTimeCourse = 0): array
    {
        if (empty($users)) {
            return [
                'totalUsers' => 0,
                'totalStarted' => 0,
                'totalFinished' => 0,
                'totalTime' => 0
            ];
        }

        $usersTotalStarted = $this->userCourseService->courseGetTotalUsersStarted($course, false, $users, $this->getAnnouncement());
        $usersTotalFinished = $this->userCourseService->courseGetTotalUsersFinished($course, false, $users, $this->getAnnouncement());
        return [
            'totalUsers' => \count($users) ?? 0,
            'totalStarted' => $usersTotalStarted ?? 0,
            'totalFinished' => $usersTotalFinished ?? 0,
            'totalTime' => $totalTimeCourse
        ];
    }
}
