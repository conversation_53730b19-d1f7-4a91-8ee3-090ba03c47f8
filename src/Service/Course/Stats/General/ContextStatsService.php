<?php

declare(strict_types=1);

namespace App\Service\Course\Stats\General;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\Itinerary;

class ContextStatsService
{
    private $strategy;

    public function __construct(BaseStatsService $strategy)
    {
        $this->strategy = $strategy;
    }

    public function setStrategy(BaseStatsService $strategy)
    {
        $this->strategy = $strategy;
    }

    public function getStats(Course $course, Announcement $announcement = null, ?Itinerary $itinerary = null): array
    {
        return $this->strategy->getStats($course, $announcement, $itinerary);
    }

    public function getChaptersStats(Course $course): array
    {
        return $this->strategy->getChaptersStats($course);
    }

    public function getCoursesByItinerary(Itinerary $itinerary): array
    {
        return $this->strategy->getCoursesByItinerary($itinerary);
    }
}
