<?php

namespace App\Service\Email;

use App\Service\SettingsService;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;


class EmailService
{
	private $mailer;
	
	private $settings;

	public function __construct(MailerInterface $mailer,  SettingsService $settings) {
		$this->mailer = $mailer;	
		$this->settings = $settings;
	}

	public function sendEmail($to, $subject, $template, $context = []) {
		$from = [$this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')];

		$email = (new TemplatedEmail())
			->from(new Address(...$from))
			->to(new Address($to))
			->subject($subject)
			->htmlTemplate($template)
			->context($context);

		$this->mailer->send($email);
	}
}
