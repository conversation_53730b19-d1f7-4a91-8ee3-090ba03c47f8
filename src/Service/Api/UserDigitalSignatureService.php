<?php

namespace App\Service\Api;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Service\SettingsService;
use App\Utils\UploadedBase64File;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\HttpFoundation\Request;

class UserDigitalSignatureService extends AbstractBaseService
{
    private ApiAnnouncementService $apiAnnouncementService;
    private Security $security;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        ApiAnnouncementService $apiAnnouncementService,
        Security $security
    ) {
        parent::__construct($em, $settings);
        $this->apiAnnouncementService = $apiAnnouncementService;
        $this->security = $security;
    }

    public function saveUserDigitalSignature(Request $request)
    {
        $content = json_decode($request->getContent(), true);
    
        // Validación de datos de entrada
        if (!isset($content['idAnnouncement'], $content['digitalSignature'])) {
            return $this->errorResponse('Datos de entrada incompletos.', 400);
        }
    
        $idAnnouncement = $content['idAnnouncement'];
        $digitalSignature = $content['digitalSignature'];
    
        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $this->security->getUser()
        ]);
    
        $announcementGroupSession = $this->apiAnnouncementService->getGroupSessionInProccessThanDigitalSignature($announcement);
    
        // Validar la sesión de grupo
        if (!$announcementGroupSession) {
            return $this->errorResponse('Al parecer existe una firma ó no hay grupo para esta sesión.', 400);
        }
            
    
        // Procesar la firma digital
        if ($digitalSignature) {
           $this->processDigitalSignature($announcementUser, $announcementGroupSession, $digitalSignature);
        }
    
        $this->em->flush();     
        $now = new \DateTime();
    
        return [
            'error' => false,
            'status' => 201,
            'message' => 'Firma digital guardada correctamente.',
            'data' =>[
                'dateDigitalSignature' =>$now->format('H:i'),
            ]

        ];
    }
    
    private function errorResponse($message, $status)
    {
        return [
            'error' => true,
            'status' => $status,
            'message' => $message
        ];
    }
 
    private function processDigitalSignature(AnnouncementUser $announcementUser, AnnouncementGroupSession $announcementGroupSession, $digitalSignature)
    {
        // Validar si ya existe una firma digital
        $announcementUserDigitalSignature = $this->em->getRepository(AnnouncementUserDigitalSignature::class)->findOneBy([
            'announcementUser' => $announcementUser,
            'announcementGroupSession' => $announcementGroupSession
        ]);

        if ($announcementUserDigitalSignature) {
            $announcementUserDigitalSignature->setImageFile(null);
            $announcementUserDigitalSignature->setImage('');
            $this->em->remove($announcementUserDigitalSignature);
        }

        // Crear la firma digital del usuario del anuncio
        $announcementUserDigitalSignature = new AnnouncementUserDigitalSignature();
        $announcementUserDigitalSignature->setAnnouncementUser($announcementUser);
        $announcementUserDigitalSignature->setAnnouncementGroupSession($announcementGroupSession);

        // Crear y asignar la imagen desde el base64
        $image = new UploadedBase64File($digitalSignature, 'digital', '.png');
        $announcementUserDigitalSignature->setImage('');
        $announcementUserDigitalSignature->setImageFile($image);

        // Persistir la firma digital en la base de datos
        $this->em->persist($announcementUserDigitalSignature);

        return $announcementUserDigitalSignature;
    }
}
