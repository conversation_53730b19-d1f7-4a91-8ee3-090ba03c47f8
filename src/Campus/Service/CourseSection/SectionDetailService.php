<?php

declare(strict_types=1);

namespace App\Campus\Service\CourseSection;

use App\Campus\Service\Base\BaseService;
use App\Campus\Service\TrainingUser\ConcreteAnnouncementTraining;
use App\Campus\Service\TrainingUser\ConcreteCategoryTraining;
use App\Campus\Service\TrainingUser\ConcreteItineraryTraining;
use App\Campus\Service\TrainingUser\ContextTraining;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Modules\CourseSection\Enum\SectionSubtypeEnum;
use App\Repository\CourseSectionRepository;
use App\Service\Api\AnnouncementNewsService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class SectionDetailService extends BaseService
{
    private $translator;
    private $announcementNewsService;
    private $courseSectionRepository;
    private $concreteCategoryTraining;
    private $concreteItineraryTraining;
    private $concreteAnnouncementTraining;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security,
        TranslatorInterface $translator,
        AnnouncementNewsService $announcementNewsService,
        CourseSectionRepository $courseSectionRepository,
        ConcreteCategoryTraining $concreteCategoryTraining,
        ConcreteItineraryTraining $concreteItineraryTraining,
        ConcreteAnnouncementTraining $concreteAnnouncementTraining
    ) {
        parent::__construct($em, $settings, $security);

        $this->translator = $translator;
        $this->announcementNewsService = $announcementNewsService;
        $this->courseSectionRepository = $courseSectionRepository;
        $this->concreteCategoryTraining = $concreteCategoryTraining;
        $this->concreteItineraryTraining = $concreteItineraryTraining;
        $this->concreteAnnouncementTraining = $concreteAnnouncementTraining;
    }

    /**
     * Obtains detailed information about a given course section.
     *
     * @param CourseSection $courseSection the course section entity
     *
     * @return array an array containing the course section details
     */
    public function getCourseSectionDetail(CourseSection $courseSection): array
    {
        $this->courseSectionRepository->obtainTranslateCourseSection($courseSection, $this->getUser()->getLocaleCampus());

        $courseSection = $this->addCategoriesToSection($courseSection);

        return [
            'id' => $courseSection->getId(),
            'name' => $courseSection->getName(),
            'description' => $courseSection->getDescription(),
            'slug' => $courseSection->getSlug(),
            'news' => $this->announcementNewsService->getNews($this->getUser()),
            'hideCategoryName' => !$courseSection->getHideCategoryName(),
            'categories' => $this->getCourseCategoriesBySubtypeSection($courseSection),
        ];
    }

    /**
     * Check if the section has categories, if not, add them, if they already exist, return the section
     * or when the section is configured manually and the categories are not added.
     *
     * @param CourseSection $courseSection the course section entity
     *
     * @return CourseSection the updated course section entity
     */
    private function addCategoriesToSection(CourseSection $courseSection): CourseSection
    {
        if ($courseSection->getCategories()->count() > 0) {
            return $courseSection;
        }

        $courseCategoryRepository = $this->em->getRepository(CourseCategory::class);
        $categories = $courseCategoryRepository->findBy([], ['sort' => 'ASC']);

        foreach ($categories as $category) {
            $courseSection->addCategory($category);
        }

        return $courseSection;
    }

    /**
     * The structure can be configured in the section, if it is not configured, it will be taken by default.
     *
     * @param CourseSection $courseSection the course section entity
     *
     * @return array an array of course categories by subtype section
     */
    private function getCourseCategoriesBySubtypeSection(CourseSection $courseSection): array
    {
        $subtypes = $this->getStructureSection($courseSection);

        $categories = [];
        $courses = [];

        foreach ($subtypes as $subtype) {
            $coursesBySubtype = $this->obtainCoursesBySubTypeSection($subtype, $courseSection);
            $categories[] = array_merge($subtype, ['categories' => $coursesBySubtype]);

            if (0 === count($coursesBySubtype)) {
                continue;
            }
            $courses[] = $coursesBySubtype;
        }

        $categories = $this->createSectionSubtypeContinueTraining($courseSection, $categories, $courses);

        $coursesCategories = [];
        foreach ($categories as $category) {
            if (empty($category['categories'])) {
                continue;
            }
            $coursesCategories[] = $category['categories'];
        }

        return array_merge(...$coursesCategories);
    }

    /**
     * In the case of active courses by filter or open campus, the structure is unified in COURSES.
     */
    private function getStructureSection(CourseSection $courseSection): array
    {
        $structure = $courseSection->getMeta();

        $newStructure = $this->filterAndSortStructure($structure, SectionSubtypeEnum::COURSES_BY_OPEN_CAMPUS, SectionSubtypeEnum::COURSES_BY_FILTERS);

        if ($courseSection->isCourseByFilter() || $courseSection->isOpenCampus()) {
            $this->activateCourseSection($newStructure);
        }

        return $newStructure;
    }

    /**
     * Activate the course section in the structure.
     *
     * @param array &$structure The structure to update
     */
    private function activateCourseSection(array &$structure): void
    {
        foreach ($structure as &$section) {
            if (SectionSubtypeEnum::COURSES === $section['subtype']) {
                $section['active'] = true;
                break;
            }
        }
    }

    /**
     * here organize the structure of the section, filtering and sorting the structure.
     */
    private function filterAndSortStructure(array $structure, string $type1, string $type2): array
    {
        usort($structure, fn ($a, $b) => $a['sort'] <=> $b['sort']);

        $filteredStructure = array_filter($structure, fn ($section) => !\in_array($section['subtype'], [$type1, $type2]));
        $subtype = $this->getSubtype($structure, $type1, $type2);

        $filteredStructure[] = $subtype;

        usort($filteredStructure, fn ($a, $b) => $a['sort'] <=> $b['sort']);

        return $filteredStructure;
    }

    /**
     * Get the subtype for the course structure.
     *
     * @param array  $structure the original structure
     * @param string $type1     the first subtype
     * @param string $type2     the second subtype
     *
     * @return array the chosen subtype for the structure
     */
    private function getSubtype(array $structure, string $type1, string $type2): array
    {
        $findType1 = array_filter($structure, fn ($section) => $section['subtype'] === $type1);
        $findType2 = array_filter($structure, fn ($section) => $section['subtype'] === $type2);

        $findType1 = array_pop($findType1);
        $findType2 = array_pop($findType2);

        $orderForStructureCourse = $findType1['sort'] < $findType2['sort'] ? $findType1 : $findType2;
        $orderForStructureCourse['subtype'] = 'COURSES';

        return $orderForStructureCourse;
    }

    /**
     * Obtain courses by subtype section.
     *
     * @param array         $subtypeSection the subtype section configuration
     * @param CourseSection $courseSection  the course section entity
     *
     * @return array the list of courses for the subtype section
     */
    private function obtainCoursesBySubTypeSection(array $subtypeSection, CourseSection $courseSection): array
    {
        if (!$subtypeSection['active']) {
            return [];
        }

        $handlers = [
            SectionSubtypeEnum::COURSES => function ($courseSection) {
                $contentTraining = new ContextTraining($this->concreteCategoryTraining);

                return $contentTraining->getTraining($courseSection);
            },
            SectionSubtypeEnum::ITINERARIES => function ($courseSection) {
                $contentTraining = new ContextTraining($this->concreteItineraryTraining);

                return $contentTraining->getTraining($courseSection);
            },
            SectionSubtypeEnum::COURSES_STARTED => function () {
                return [];
            },
            SectionSubtypeEnum::ANNOUNCEMENTS => function ($courseSection) {
                $contentTraining = new ContextTraining($this->concreteAnnouncementTraining);

                return $contentTraining->getTraining($courseSection);
            }
        ];

        if (isset($handlers[$subtypeSection['subtype']])) {
            return $handlers[$subtypeSection['subtype']]($courseSection);
        }

        return [];
    }

    /**
     * Create section subtype continue training.
     *
     * @param CourseSection $courseSection the course section entity
     * @param array         $categories    the list of categories
     * @param array         $courses       the list of courses
     *
     * @return array the updated list of categories
     */
    private function createSectionSubtypeContinueTraining(CourseSection $courseSection, array $categories, array $courses): array
    {
        if (!$courseSection->isCourseStarted()) {
            return $categories;
        }

        $courses = array_pop($courses);

        foreach ($categories as $key => $category) {
            if (SectionSubtypeEnum::COURSES_STARTED === $category['subtype']) {
                if ($courses) {
                    $categories[$key]['categories'] = $this->getStructureCategoryCoursesTraining($courses);

                    if ([] === $categories[$key]['categories'][0]['courses']) {
                        unset($categories[$key]);
                    }
                } else {
                    $categories[$key]['categories'] = [];
                }
            }
        }

        return $categories;
    }

    /**
     * Get structure category courses training.
     *
     * @param array $courses the list of courses
     *
     * @return array the structure category courses training
     */
    private function getStructureCategoryCoursesTraining(array $courses): array
    {
        return [[
            'id' => 0,
            'name' => $this->translator->trans('message_api.controller.continue_training', [], 'message_api', $this->getUser()->getLocaleCampus()),
            'type' => SectionSubtypeEnum::COURSES_STARTED,
            'courses' => $this->obtainCoursesStarted($courses)
        ]];
    }

    /**
     * Obtain courses that have been started.
     *
     * @param array $courses the list of courses
     *
     * @return array the list of courses that have been started
     */
    private function obtainCoursesStarted(array $courses): array
    {
        $uniqueCourses = [];

        foreach ($courses as $course) {
            foreach ($course['courses'] as $courseStarted) {
                if (isset($courseStarted['startAt']) && null === $courseStarted['finishAt']) {
                    $uniqueCourses[$courseStarted['id']] = $courseStarted;
                }
            }
        }

        $coursesStarted = array_values($uniqueCourses);

        usort($coursesStarted, fn ($a, $b) => $a['startAt'] <=> $b['startAt']);

        return $coursesStarted;
    }
}
