<?php

declare(strict_types=1);

namespace App\Campus\Service\Survey;

use App\Campus\Service\Base\BaseService;
use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\UserCourse;
use App\Modules\Survey\Repository\NpsModuleRepository;
use App\Service\Nps\NpsService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Symfony\Component\Security\Core\Security;

class SurveyService extends BaseService
{
    private NpsService $npsService;
    private NpsModuleRepository $npsRepository;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security,
        NpsService $npsService,
        NpsModuleRepository $npsRepository,
        UserCourseService $userCourseService
    ) {
        parent::__construct($em, $settings, $security);

        $this->npsService = $npsService;
        $this->npsRepository = $npsRepository;
        $this->userCourseService = $userCourseService;
    }

    /**
     * @throws \Exception
     */
    public function processEvaluation(array $request): void
    {
        try {
            $this->validateRequestData($request);
            $userCourse = $request['userCourse'] ? $this->getUserCourse($request['userCourse']) : null;

            if ($userCourse) {
                $this->validatePreviousSurvey($request, $userCourse);
                $this->saveUserCourseEvaluation($userCourse);
                $this->userCourseService->calculatePointsUserCourse($userCourse);
            }

            $this->createNpsList($request);
            $this->em->flush();

            $this->npsService->actionsNpsCourseStat($userCourse);
        } catch (\Exception $e) {
            throw new \Exception('Error processing evaluation: ' . $e->getMessage());
        }
    }

    private function validatePreviousSurvey($request, $userCourse)
    {
        if (empty($request['answers'])) {
            throw new \Exception('Answers not found');
        }

        foreach ($request['answers'] as $answer) {
            if (!empty($request['idAnnouncement']) && $request['idAnnouncement'] > 0) {
                $this->validatePreviousSurveyAnnouncement($request, $answer, $userCourse);
            } else {
                $this->validatePreviousSurveyUserCourse($answer, $userCourse);
            }
        }
    }

    private function validatePreviousSurveyAnnouncement(array $request, array $answer, UserCourse $userCourse)
    {
        $npsAnnouncement = $this->em->getRepository(Nps::class)->findOneBy(
            [
                'announcement' => $request['idAnnouncement'],
                'course' => $userCourse,
                'type' => $answer['type']
            ]
        );

        if ($npsAnnouncement) {
            throw new \Exception('Previous Survey Announcement Course found');
        }
    }

    private function validatePreviousSurveyUserCourse(array $answer, UserCourse $userCourse)
    {
        $npsUserCourse = $this->em->getRepository(Nps::class)->findOneBy(['course' => $userCourse, 'type' => $answer['type']]);
        if ($npsUserCourse) {
            throw new \Exception('Previous Survey Course found');
        }
    }

    public function validateRequestData(array $request): void
    {
        if (
            !isset(
                $request['answers'],
                $request['userCourse'],
                $request['idAnnouncement']
            )
        ) {
            throw new \InvalidArgumentException('Missing required fields.');
        }
    }

    /**
     * @throws NotSupported
     */
    private function getUserCourse(int $userCourseId): UserCourse
    {
        $userCourse = $this->em->getRepository(UserCourse::class)->find($userCourseId);
        if (!$userCourse) {
            throw new \InvalidArgumentException('User course not found.');
        }

        return $userCourse;
    }

    /**
     * @throws ORMException
     */
    private function saveUserCourseEvaluation(UserCourse $userCourse): void
    {
        $userCourse->setValuedAt(new \DateTime());
        $this->em->persist($userCourse);
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     */
    public function createNpsList(array $request): void
    {
        $userCourse = $request['userCourse'] ? $this->getUserCourse($request['userCourse']) : null;

        $answers = $request['answers'];
        $idAnnouncement = $request['idAnnouncement'] && $request['idAnnouncement'] > 0 ? $request['idAnnouncement'] : null;

        if (!$this->isMainQuestionTextInArray($answers)) {
            $this->addMainTextQuestion($userCourse, $idAnnouncement);
        }

        foreach ($answers as $answer) {
            $npsQuestion = $this->getNpsQuestion($answer);

            $params = $this->buildParams($answer, $idAnnouncement);

            $this->npsRepository->createNps($userCourse, $npsQuestion, $params);
        }
    }

    private function isMainQuestionTextInArray(array $answer): bool
    {
        foreach ($answer as $answer) {
            if ($answer['main'] && $answer['type'] === NpsQuestion::TYPE_TEXT) {
                return true;
            }
        }

        return false;
    }

    private function addMainTextQuestion(?UserCourse $userCourse, ?int $idAnnouncement): void
    {
        $npsTextMain = $this->em->getRepository(NpsQuestion::class)
            ->findOneBy(['main' => true, 'type' => NpsQuestion::TYPE_TEXT]);

        if (!$npsTextMain) {
            throw new \RuntimeException('Main text question not found.');
        }

        $answer = [
            'id' => $npsTextMain->getId(),
            'main' => $npsTextMain->getMain(),
            'value' => '',
            'type' => NpsQuestion::TYPE_TEXT,
        ];

        $params = $this->buildParams($answer, $idAnnouncement);
        $this->npsRepository->createNps($userCourse, $npsTextMain, $params);
    }

    /**
     * build the params for the createNps method
     */
    private function buildParams(array $answer, ?int $idAnnouncement): array
    {
        $params = [
            'answer' => $answer,
            'user' => $this->security->getUser(),
            'isPost' => $this->settings->get('app.survey.post_nps.enabled') ?? false,
        ];

        if ($idAnnouncement) {
            $params['idAnnouncement'] = $idAnnouncement;
        }

        return $params;
    }



    /**
     * @throws NotSupported
     */
    private function getNpsQuestion(array $answer): NpsQuestion
    {
        $npsQuestion = $this->em->getRepository(NpsQuestion::class)->find($answer['id']);

        if (!$npsQuestion) {
            throw new \InvalidArgumentException('Nps question not found.');
        }

        return $npsQuestion;
    }
}
