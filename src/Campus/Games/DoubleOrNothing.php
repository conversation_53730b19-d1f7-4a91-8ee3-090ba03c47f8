<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Answer;
use App\Entity\Chapter;
use App\Entity\Question;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GameEnum;

class DoubleOrNothing extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(Question::class);
        $data = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($data);
        $time = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $time,
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        $answer = isset($answers->id) ? $answers->id : null;

        if (null === $answer) {
            return [
                'correct' => false,
            ];
        }

        $repository = $this->em->getRepository(Answer::class);
        $answer = $repository->find($answers->id);
        $correct = $answer->getCorrect();

        return [
            'correct' => $correct,
        ];
    }

    private function getFormattedQuestions($data): array
    {
        $questions = [];
        foreach ($data as $question) {
            $time = $this->calculateReadingAverageTime($question->getQuestion());
            $answers = $this->getFormattedAnswers($question->getAnswers());

            $questions[] = [
                'id' => $question->getId(),
                'question' => $question->getQuestion(),
                'imageUrl' => $question->getImageUrl(),
                'random' => $question->getRandom(),
                'answers' => $answers,
                'time' => $question->getTime() + GameEnum::DOUBLE_OR_NOTHING_AVERAGE_TIME_TO_ANSWER,
            ];
        }

        return $questions;
    }

    private function getFormattedAnswers($rouletteAnswers): array
    {
        $answers = [];
        foreach ($rouletteAnswers as $answer) {
            $answers[] = [
                'id' => $answer->getId(),
                'answer' => $answer->getAnswer(),
            ];
        }

        return $answers;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $question) {
            $time += $question['time'];
        }

        return $time;
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])) {
            return 0;
        }

        $answers = $data['answers'];
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts']) ? \count($data['attempts']) : 1;
        $level = \count($answers);

        if (\count($answers) > 0 && false === $answers[\count($answers) - 1]['correct']) {
            return 0;
        } else {
            $basePercentage = (((($level + 1) * $level) / 2) / (((EnumGameFormula::DOUBLE_OR_NOTHING_MAX_LEVEL + 1) * EnumGameFormula::DOUBLE_OR_NOTHING_MAX_LEVEL) / 2));
            $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
            $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

            return $attemptsCorrectionPercentage;
        }
    }
}
