<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\AdivinaImagen;
use App\Entity\Chapter;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class Riddle extends Game
{
    protected EntityManagerInterface $em;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, SettingsService $settings)
    {
        $this->em = $em;
        $this->settings = $settings;
        parent::__construct($em);
    }

    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(AdivinaImagen::class);
        $data = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($data);

        return [
            'questions' => $questions,
        ];
    }

    private function getFormattedQuestions($data): array
    {
        $path = $this->settings->get(GamesEnum::RIDDLE_UPLOADS_PATH) . '/';
        $questions = [];

        foreach ($data as $question) {
            $questions[] = [
                'id' => $question->getId(),
                'words' => $question->getWords(),
                'image' => $path . $question->getImage(),
                'clue' => $question->getClue(),
                'time' => $question->getTime(),
                'title' => $question->getTitle(),
            ];
        }

        return $questions;
    }

    public function check($userCourseChapter, $answers): array
    {
        $repository = $this->em->getRepository(AdivinaImagen::class);
        $answer = $repository->find($answers->questionId);
        $expectedAnswer = $this->normalize($answer->getWords());

        if (!isset($answers->value)) {
            return [
                'correct' => false,
            ];
        }

        $normalizeAnswer = $this->normalize($answers->value);

        return [
            'correct' => $expectedAnswer === $normalizeAnswer,
        ];
    }

    private function normalize($name): string
    {
        $name = strtolower($name);
        $normalized = [];

        $words = preg_split('/([^a-z])/', $name, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
        foreach ($words as $word) {
            if (preg_match('/^(mc)(.*)$/', $word, $matches)) {
                $word = $matches[1] . ucfirst($matches[2]);
            }

            $normalized[] = ucfirst($word);
        }

        return implode('', $normalized);
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
            || !isset($data['totalQuestions'])
            || !$data['totalQuestions'] > 0) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $maxTime = $data['timeTotal'];
        $nQuestions = $data['totalQuestions'];
        $failsAnswers = 0;
        $rightAnswers = 0;
        $time = 0;
        $nAttempts = \array_key_exists('attempts', $data) ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if(isset($answer['correct'])){
                    if ($answer['correct']) {
                        ++$rightAnswers;
                    }
                    if (!$answer['correct']) {
                        ++$failsAnswers;
                    }
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nQuestions > 0 ? ($rightAnswers / $nQuestions) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (EnumGameFormula::BASE_QUARTER * pow(EnumGameFormula::RIDDLE_PENALTY, ($failsAnswers * 2) / $nQuestions) + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime)));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrection = pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);
        $attemptsCorrectionPercentage = ($adjustedPercentage * $attemptsCorrection);

        return $attemptsCorrectionPercentage;
    }
}
