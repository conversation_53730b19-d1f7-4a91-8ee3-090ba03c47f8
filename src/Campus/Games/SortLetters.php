<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\OrdenarMenormayor;
use App\Enum\Games as EnumGameFormula;

class SortLetters extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $guessQuestions = $userCourseChapter->getChapter()->getOrdenarMenormayors();
        $questions = $this->getFormattedQuestions($guessQuestions);

        return [
            'questions' => $questions,
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        $repository = $this->em->getRepository(OrdenarMenormayor::class);
        $expectedAnswer = $repository->find($answers->questionId);

        $isCorrect = $this->isCorrect($answers, $expectedAnswer->getWordsArray());

        return [
            'correct' => $isCorrect,
        ];
    }

    private function mb_str_shuffle($str, $encoding = 'UTF-8'): string
    {
        $array = mb_str_split($str, 1, $encoding);
        shuffle($array);

        return implode('', $array);
    }

    private function getFormattedQuestions($guessQuestions): array
    {
        $questions = [];

        foreach ($guessQuestions as $question) {
            $wordsText = $question->getWordsArray();

            $formattedQuestion = [
                'id' => $question->getId(),
                'time' => $question->getTime(),
                'question' => $question->getTitle(),
                'word' => $this->mb_str_shuffle($wordsText),
            ];

            $questions[] = $formattedQuestion;
        }

        return $questions;
    }

    private function isCorrect($answer, $expectedWord): bool
    {
        if (!isset($answer) || !isset($answer->word)) {
            return false;
        }

        $word = $answer->word;

        return strtolower($word) === strtolower($expectedWord);
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
            || !isset($data['totalQuestions'])
            || !$data['totalQuestions'] > 0) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $maxTime = $data['timeTotal'];
        $nQuestions = $data['totalQuestions'];
        $nAttempts = \array_key_exists('attempts', $data) ? \count($data['attempts']) : 1;
        $wordsFails = 0;
        $totalAttempts = 0;
        $rightQuestions = 0;
        $time = 0;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                foreach ($answer['attempts'] as $answerAttempt) {
                    ++$totalAttempts;
                    if(isset($answerAttempt['correct'])){
                        if ($answerAttempt['correct']) {
                            ++$rightQuestions;
                        }
                        if (!$answerAttempt['correct']) {
                            ++$wordsFails;
                        }
                    }
                    $time += $answerAttempt['time'];
                }
            }
        }

        if (0 == $rightQuestions || ($rightQuestions < $nQuestions)) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (EnumGameFormula::BASE_QUARTER * pow(EnumGameFormula::SORT_LETTERS_PENALTY, ($wordsFails * 2) / $nQuestions) + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime)));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
