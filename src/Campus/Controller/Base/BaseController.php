<?php

declare(strict_types=1);

namespace App\Campus\Controller\Base;

use App\Admin\Traits\LanguagesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/api")
 */
class BaseController extends AbstractFOSRestController
{
    use SerializerTrait;
    use LanguagesTrait;

    public $settings;
    public $em;
    public $translator;

    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        TranslatorInterface $translator
    ) {
        $this->settings = $settings;
        $this->em = $em;
        $this->translator = $translator;
    }

    protected function createResponse($data = null, array $paramsExtra = [])
    {
        $status = $paramsExtra['status'] ?? Response::HTTP_OK;
        $error = $paramsExtra['error'] ?? false;
        $message = $paramsExtra['message'] ?? null;
        $groups = $paramsExtra['groups'] ?? [];

        $responseData = [
            'status' => $status,
            'error' => $error,
            'data' => $data,
        ];

        if ($error && null !== $message) {
            $responseData['message'] = $message;
        }

        return $this->sendResponse($responseData, ['groups' => $groups]);
    }

    protected function handleException(\Exception $e): Response
    {
        $errorMessage = \sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

        return $this->sendResponse(
            ['error' => true, 'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => $errorMessage]
        );
    }

    protected function validateData(Request $request, array $rules)
    {
        $requestData = json_decode($request->getContent(), true);

        $params = [];
        $params['status'] = Response::HTTP_BAD_REQUEST;
        $params['error'] = true;

        foreach ($rules as $key => $rule) {
            if (!\array_key_exists($key, $requestData)) {
                $params['message'] = 'Missing required field: '.$key;

                return $this->createResponse(null, $params);
            }

            if ('required' === $rule && empty($requestData[$key])) {
                $params['status'] = Response::HTTP_BAD_REQUEST;
                $params['message'] = 'Field '.$key.' is required';

                return $this->createResponse(null, $params);
            }
        }
    }

    protected function executeSafe(callable $callback, $data = [], $groups = [], $code = Response::HTTP_OK): Response
    {
        try {
            $result = $callback($data, $groups);
            $params = ['groups' => $groups];
            $params['status'] = $code;

            return $this->createResponse($result, $params);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
