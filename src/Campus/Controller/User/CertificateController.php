<?php

namespace App\Campus\Controller\User;

use App\Campus\Controller\Base\BaseController;
use App\Service\SettingsService;
use Symfony\Component\Routing\Annotation\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use Swagger\Annotations as SWG;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;
use Doctrine\ORM\EntityManagerInterface;

use App\Campus\Service\User\CertificateService;
use App\Service\Diploma\DiplomaService;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;


use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\UserHistoryDownloadDiploma;



/**
 * Class CertificateController
 * @package App\Campus\Controller\User
 *
 * @Route("/api")
 */
class CertificateController extends BaseController
{

	private DiplomaService                      $diplomaService;
	private AnnouncementConfigurationsService   $announcementConfigurationsService;
	private CertificateService                  $certificateService;

    public function __construct(
        SettingsService                   $settings,
        EntityManagerInterface            $em,
        TranslatorInterface               $translator,
        DiplomaService                    $diplomaService,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        CertificateService                $certificateService
    )
    {
        parent::__construct($settings, $em, $translator);
        $this->diplomaService = $diplomaService;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->certificateService = $certificateService;
    }


    /**
	 * @Rest\Get("/passport", name="api_passport")
	 *
	 * @SWG\Tag(name="Diplomas")
	 * @SWG\Response(
	 *    response=200,
	 *   description="OK response",
	 * )
	 * @SWG\Response(
	 *     response=500,
	 *     description="Error"
	 * )
	 */
	public function fetchDiplomasUser(): Response
	{
		return  $this->executeSafe(function () {
			return $this->certificateService->obtainCertificatesUser();
		}, [], ['passport', 'progress'], Response::HTTP_OK);
	}

	/**
	 * @Rest\Post("/print-diploma", name="api_print_diploma")
	 *
	 * @SWG\Tag(name="Diplomas")

	 * @SWG\Response(
	 *     response=200,
	 *     description="OK response",
	 *     @SWG\Schema(
	 *         type="object",
	 *         @SWG\Property(
	 *             property="status",
	 *             type="integer",
	 * 			  	description="HTTP status code"
	 *           
	 *         ),
	 *         @SWG\Property(
	 *             property="error",
	 *             type="boolean",
	 *           
	 *         ),
	 *         @SWG\Property(
	 *             property="data",
	 *             type="string",	
	 *             description="return file base64 encoded"
	 *         ),
	 *     )
	 * )
	 */
	public function printDiploma(Request $request)
	{
		$requestData = \json_decode($request->getContent(), true);
		$idAnnouncement = $requestData['announcement'] ?? null;

		$announcement = null;
		if ($idAnnouncement && is_numeric($idAnnouncement)) {
			$announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
		}

		$code = Response::HTTP_OK;
		$error = false;
		try {
			if ($announcement) {
				$output = $this->diplomaService->generateUserRequestedDiploma($announcement, $this->getUser());
			} else {
				$output = $this->diplomaService->generateDiplomaDefault($requestData, $this->getUser());
			}

			$this->saveUserHistoryDownloadDiploma($requestData['idCourse'], $idAnnouncement);

			$response = [
				'status' => $code,
				'error'  => $error,
				'data'   => $output['diploma'],
				'nombre'   => $output['nombre']
			];
		} catch (\Exception $e) {
			$response = [
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => 'Ha ocurrido un error al descargar el pdf' . ' {' . $e->getMessage() . '}'
			];
		}

		return $this->sendResponse($response);
	}

	private function saveUserHistoryDownloadDiploma($idCourse, $idAnnouncement = null)
	{
		$user = $this->getUser();
		$course = $this->em->getRepository(Course::class)->find($idCourse);

		$announcement = null;
		if ($idAnnouncement && is_numeric($idAnnouncement)) {
			$announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
		}

		$newHistoryDownloadDiploma = new UserHistoryDownloadDiploma();
		$newHistoryDownloadDiploma->setCreatedBy($user);
		$newHistoryDownloadDiploma->setUpdatedBy($user);
		$newHistoryDownloadDiploma->setCourse($course);

		if ($announcement !== null) {
			$newHistoryDownloadDiploma->setAnnouncement($announcement);
			$this->updateAnnouncementUser($announcement);
		}
		$this->em->persist($newHistoryDownloadDiploma);
		$this->em->flush();
	}

	private function updateAnnouncementUser(Announcement $announcement)
	{
		$announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
			'announcement' => $announcement,
			'user' => $this->getUser()
		]);

		if ($announcementUser && !$announcementUser->isDownloadDiploma()) {
			$announcementUser->setDownloadDiploma(true);
			$this->em->persist($announcementUser);
			$this->em->flush();
		}
	}
}
