<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Campus\Games\GameFactory;
use App\Entity\UserCourseChapter;
use App\Repository\AnnouncementRepository;
use App\Repository\ChapterRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiQuestionController extends ApiBaseController
{
    /**
     * ApiController constructor.
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, TranslatorInterface $translator, SettingsService $settingsService)
    {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
    }

    /**
     * @Rest\Get("/chapters/{id}/questions", name="api_chapter_questions")
     *
     * @return Response
     */
    public function chapterQuestions(UserCourseChapter $userCourseChapter, GameFactory $gameFactory)
    {
        try {
            $chapter = $userCourseChapter->getChapter();
            $chapterType = $chapter->getType();
            $gameNormalizedName = $chapterType->getNormalized();

            $game = $gameFactory->createGame($gameNormalizedName);
            $responseData = $game->getQuestions($userCourseChapter);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $responseData,
            ];

            // switch ($userCourseChapter->getChapter()->getType()->getId()) {
            // 	VIDEOQUIZ_TYPE: 'VideoQuiz';
            return $this->sendResponse($response);
        } catch (\Exception $e) {
            $response = $this->responseError($e->getMessage());

            return $this->sendResponse($response);
        }
    }

    /**
     * @Rest\Post("/chapters/answer/{id}/check", name="api_chapter_answer_check")
     *
     * @return Response
     */
    public function answerCheck(UserCourseChapter $userCourseChapter, Request $request, GameFactory $gameFactory)
    {
        try {
            $chapter = $userCourseChapter->getChapter();
            $chapterType = $chapter->getType();
            $gameNormalizedName = $chapterType->getNormalized();
            $content = json_decode($request->getContent());

            $game = $gameFactory->createGame($gameNormalizedName);
            $responseData = $game->check($userCourseChapter, $content);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $responseData,
            ];

            return $this->sendResponse($response);
        } catch (\Exception $e) {
            $response = $this->responseError($e->getMessage());

            return $this->sendResponse($response);
        }
    }

    private function responseError($message)
    {
        return [
            'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
            'error' => true,
            'data' => "Error: {$message}",
        ];
    }

    /**
     * @Rest\Post("/chapters/{id}/maxquestion", name="api_update_max_question")
     *
     * @return Response
     */
    public function updateMaxQuestion($id, ChapterRepository $chapterRepository, Request $request)
    {
        try {
            $chapterRepository->updateMaxQuestion(
                $request->get('maxQuestion'),
                $chapterRepository->find($id),
                filter_var($request->get('doAllQuestions', false), FILTER_VALIDATE_BOOLEAN)
            );

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'OK',
            ];

            return $this->sendResponse($response);
        } catch (\Exception $e) {
            $response = $this->responseError($e->getMessage());

            return $this->sendResponse($response);
        }
    }
}
