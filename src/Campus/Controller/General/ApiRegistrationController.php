<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Admin\Traits\LanguagesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\RecoveryCode;
use App\Entity\Setting;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use App\Service\User\Authentication\UserPasswordValidatorService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiRegistrationController extends AbstractController
{
    use SerializerTrait;
    use LanguagesTrait;

    private MailerInterface $mailer;
    private UserRepository $userRepository;
    private EntityManagerInterface $em;
    private TranslatorInterface $translator;
    private LoggerInterface $logger;
    private UserPasswordHasherInterface $userPasswordHasher;
    private SettingsService $settings;
    private RequestStack $requestStack;

    public function __construct(
        MailerInterface $mailer,
        UserRepository $userRepository,
        EntityManagerInterface $em,
        TranslatorInterface $translator,
        LoggerInterface $logger,
        UserPasswordHasherInterface $userPasswordHasher,
        SettingsService $settings,
        RequestStack $requestStack
    ) {
        $this->mailer = $mailer;
        $this->userRepository = $userRepository;
        $this->em = $em;
        $this->translator = $translator;
        $this->logger = $logger;
        $this->userPasswordHasher = $userPasswordHasher;
        $this->settings = $settings;
        $this->requestStack = $requestStack;
    }

    /**
     * @Route("/user-register", name="api-user-register",methods={"POST"})
     */
    public function registerUser(Request $request, LoggerInterface $userRegisterLogger): Response
    {
        $userRegisterLogger->info('User registration started');
        $locale = '';

        try {
            $requestData = json_decode($request->getContent(), true);
            $email = $requestData['email'];
            $firstName = $requestData['firstName'];
            $lastName = $requestData['lastName'];
            $password = $requestData['password'];
            $languageDefault = $this->settings->get('app.defaultLanguage');
            $localeUser = (isset($requestData['locale'])) ? $requestData['locale'] : $languageDefault;

            try {
                $extraFields = $this->validateAndFormatExtraFields($requestData);
            } catch (BadRequestException $e) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => $this->translator->trans('user.extra_fields.bad_request', [], 'user', $locale),
                ]);
            }

            $user = '';
            $message = '';
            $locale = '';

            if (isset($requestData['key']) && false == $this->settings->get('app.free.user.registration')) {
                $registerKey = str_replace(['.', '-', ' '], '', $requestData['key']);
                $user = $this->userRepository->findOneBy(['registerKey' => $registerKey]);
                $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $localeUser;
                $message = $this->translator->trans('email.messages_to_user.key_not_found', [], 'email', $locale);

                if (!$user) {
                    $userRegisterLogger->info(
                        'email: ' . $email
                        . ' |  firstName: ' . $firstName
                        . ' | lastName: ' . $lastName
                        . ' | key: ' . $requestData['key']
                    );
                }
            } elseif (false == $this->settings->get('app.free.user.registration')) {
                $user = $this->userRepository->findOneBy(['email' => $email]);
                $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $localeUser;
                $message = $this->translator->trans('email.messages_to_user.email_not_found', [], 'email', $locale);
            } else {
                $user = $this->userRepository->findOneBy(['email' => $email]);
                $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $localeUser;
                $message = $this->translator->trans('email.messages_to_user.email_not_found', [], 'email', $locale);
            }

            if ($user && !$user->getIsActive() && $user->getValidated()) {
                $user->setEmail($email);
                $user->setFirstName($firstName);
                $user->setLastName($lastName);
                $user->setPassword($this->userPasswordHasher->hashPassword($user, $password));
                // $user->setIsActive(1);
                $user->setLocaleCampus($localeUser);
                $user->setLocale($localeUser);
                $user->setMetaByLabel('extraFields', $extraFields);
                $this->em->persist($user);
                $this->em->flush();
                $this->sendNotification($email, $locale);

                $response = [
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => $this->translator->trans('email.messages_to_user.register', [], 'email', $locale),
                ];
            } elseif ($user && $user->getIsActive() && $user->getValidated()) {
                $response = [
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => $this->translator->trans('email.messages_to_user.activated', [], 'email', $locale),
                ];
            } elseif ($user && !$user->getIsActive() && !$user->getValidated()) {
                $response = [
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => $this->translator->trans('email.messages_to_user.exist_email', ['%email%' => $email], 'email', $localeUser),
                ];
            } elseif ($user && $user->getIsActive() && !$user->getValidated()) {
                $response = [
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => $this->translator->trans('email.messages_to_user.exist_email', ['%email%' => $email], 'email', $localeUser),
                ];
            } elseif ($this->settings->get('app.free.user.registration')) {
                $user = $this->userRepository->findOneBy(['email' => $email]);
                if (!$user) {
                    $user = new User();
                    $user->setEmail($email);
                    $user->setFirstName($firstName);
                    $user->setLastName($lastName);
                    $user->setPassword($password);
                    $user->setLocaleCampus($localeUser);
                    $user->setLocale($localeUser);
                    $user->setMetaByLabel('extraFields', $extraFields);
                    $user->setOpen(true);

                    $roles = ['ROLE_USER'];
                    $user->setRoles($roles);

                    $this->em->persist($user);
                    $this->em->flush();

                    if ($this->settings->get('app.free.user.registration-autovalidate')) {
                        $user->setValidated(true);
                        $this->em->flush();
                        $this->sendNotification($email, $localeUser);
                    } else {
                        $this->requestReceivedNotification($email, $firstName, $localeUser);
                        $this->requestReceivedNotificationAdministrator($email, $firstName, $lastName);
                    }

                    //                    $this->container->remove('security.token_storage');
                    //                    $session = $this->container->get('session');
                    //                    $session = new Session();
                    //                    $session->invalidate();

                    $response = [
                        'status' => Response::HTTP_OK,
                        'error' => false,
                        'data' => $this->translator->trans('email.messages_to_user.registre_free', [], 'email', $localeUser),
                    ];
                } else {
                    $response = [
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                        'error' => false,
                        'data' => $this->translator->trans('email.messages_to_user.exist_email', ['%email%' => $email], 'email', $localeUser),
                    ];
                }
            } else {
                $response = [
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => $message,
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_register', [], 'email', $locale) . '{' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function validateAndFormatExtraFields($requestData): array
    {
        $settingsRepository = $this->em->getRepository(Setting::class);
        $extraFieldsConfig = $settingsRepository->findOneBy(['code' => 'app.user.extra_fields']);
        $extraFieldsConfigValue = json_decode($extraFieldsConfig->getValue(), true);
        $extraFields = $requestData['extraFields'] ?? [];

        if (empty($extraFields) && !empty($extraFieldsConfigValue)) {
            throw new BadRequestException($this->translator->trans('user.extra_fields.bad_request', [], 'user'));
        }
        if (empty($extraFields) && empty($extraFieldsConfigValue)) {
            return [];
        }

        $result = [];
        $requiredFields = array_column(array_filter($extraFieldsConfigValue, fn ($f) => $f['required'] ?? false), 'name');
        $receivedFields = array_column($extraFields, 'name');

        if (array_diff($requiredFields, $receivedFields)) {
            throw new BadRequestException($this->translator->trans('user.extra_fields.bad_request', [], 'user'));
        }

        foreach ($extraFieldsConfigValue as $extraFieldConfig) {
            foreach ($extraFields as $extraField) {
                if ($extraFieldConfig['name'] === $extraField['name']) {
                    // if options are set, validate that the value is within them.
                    if (isset($extraFieldConfig['options'])) {
                        $validOptions = array_column($extraFieldConfig['options'], 'value');
                        if (!\in_array($extraField['value'], $validOptions, true)) {
                            return [];
                        }
                    }
                    $result[$extraField['name']] = $extraField['value'];
                }
            }
        }

        return $result;
    }

    /**
     * @Route("/user-register-help-options", name="user-register-help-options",methods={"GET", "POST"})
     *
     * @return Response
     */
    public function registerHelpOptions(Request $request)
    {
        $status = Response::HTTP_OK;
        $error = false;

        try {
            $requesData = json_decode($request->getContent(), true);

            $locale = (isset($requesData['locale'])) ? $requesData['locale'] : $this->settings->get('app.defaultLanguage');

            $options = $this->settings->get('register.help.options');

            $id = 1;
            foreach ($options as $name => $config) {
                $option = [
                    'id' => 1,
                    'name' => $name,
                    'text' => $this->translator->trans('fields.' . $name, [], 'register_help', $locale),
                    'choices' => [],
                ];

                if ('filter' == $config['type']) {
                    $filterCategory = $this->em->getRepository(FilterCategory::class)->find($config['id']);
                    $values = $this->em->getRepository(Filter::class)->findBy([
                        'filterCategory' => $filterCategory,
                    ], ['name' => 'ASC']);
                    $choices = [];
                    foreach ($values as $value) {
                        if ('' != $value->getName()) {
                            $choices[] = [
                                'id' => $value->getId(),
                                'name' => ($config['translatable']) ? $this->translator->trans($name . '.' . $value->getId(), [], 'register_help', $this->settings->get('app.defaultLanguage')) : $value->getName(),
                            ];
                        }
                    }

                    $option['choices'] = $choices;
                }

                $data['options'][] = $option;
                ++$id;
            }
        } catch (\Exception $e) {
            $status = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $data = $this->translator->trans('email.messages_to_user.error_register', [], 'email', $this->settings->get('app.defaultLanguage')) . '{' . $e->getMessage() . '}';
        }

        $response = [
            'status' => $status,
            'error' => $error,
            'data' => $data,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/user-register-help", name="user-register-help",methods={"POST"})
     *
     * @return Response
     */
    public function registerHelp(Request $request)
    {
        $status = Response::HTTP_OK;
        $error = false;
        $data = [];

        try {
            $requestData = json_decode($request->getContent(), true);

            $content = '';
            $to = [];

            $registerHelpTo = $this->settings->get('register.help.to');

            // Start checkin center
            if (isset($registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['center'][$requestData['center']['id']]['email'])) {
                $to = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['center'][$requestData['center']['id']]['email'];
                $locale = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['center'][$requestData['center']['id']]['locale'];
            } else {
                // Step 2. Country
                if (isset($registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['email'])) {
                    $to = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['email'];
                    $locale = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['country'][$requestData['country']['id']]['locale'];
                } else {
                    // Step 3. collective
                    if (isset($registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['email'])) {
                        $to = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['email'];
                        $locale = $registerHelpTo['division'][$requestData['division']['id']]['collective'][$requestData['collective']['id']]['locale'];
                    } else {
                        // Step 4. Division
                        if (isset($registerHelpTo['division'][$requestData['division']['id']]['email'])) {
                            $to = $registerHelpTo['division'][$requestData['division']['id']]['email'];
                            $locale = $registerHelpTo['division'][$requestData['division']['id']]['locale'];
                        }
                    }
                }
            }

            if (empty($to)) {
                $to = $registerHelpTo['default']['email'];
                $locale = $registerHelpTo['default']['locale'];
            }

            if (!\is_array($to)) {
                $to = [$to];
            }

            $content = [
                'requestData' => $requestData,
                'to' => $to,
                'locale' => $locale,
                'options' => $this->settings->get('register.help.options'),
            ];

            $sendEmail = (new TemplatedEmail())
                ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
                ->to(...$to)
                ->subject($this->translator->trans('email.subject', [], 'register_help', $locale))
                ->text('')
                ->htmlTemplate('template_email/register-help.html.twig')
                ->context($content);

            $this->mailer->send($sendEmail);

            $data = $this->translator->trans('email.sent', [], 'register_help', $locale);
        } catch (\Exception $e) {
            $status = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $data = $this->translator->trans('email.messages_to_user.error_register', [], 'email', $this->settings->get('app.defaultLanguage')) . '{' . $e->getMessage() . '}';
        }

        $response = [
            'status' => $status,
            'error' => $error,
            'data' => $data,
        ];

        return $this->sendResponse($response);
    }

    public function sendNotification($email, $locale)
    {
        $user = $this->userRepository->findOneBy(['email' => $email]);
        $codigo = date('His');

        $recovery = new RecoveryCode();
        $recovery->setUser($user);
        $recovery->setEmail($email);
        $recovery->setDateRecovery(new \DateTime());
        $recovery->setCodeActivation(md5($codigo));
        $recovery->setState(0);

        $this->em->persist($recovery);
        $this->em->flush();

        $sendEmail = (new TemplatedEmail())
            ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
            ->to(new Address($email))
            ->subject($this->translator->trans('email.template_email.active_account', [], 'email', $locale))
            ->text('')
            ->htmlTemplate('template_email/register-user.html.twig')
            ->context([
                'expiration_date' => new \DateTime('+7 days'),
                'user' => $user,
                'codigo' => md5($codigo),
                'locale' => $locale,
            ]);

        $this->mailer->send($sendEmail);
    }

    public function requestReceivedNotification($email, $firstName, $locale)
    {
        $sendEmail = (new TemplatedEmail())
            ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
            ->to(new Address($email))
            ->subject($this->translator->trans('email.template_email.your_registration_request', [], 'email', $locale))
            ->text('')
            ->htmlTemplate('template_email/receive_registration_user.html.twig')
            ->context([
                'user' => $firstName,
                'locale' => $locale,
                'appFromName' => $this->settings->get('app.fromName'),
            ]);

        $this->mailer->send($sendEmail);
    }

    public function requestReceivedNotificationAdministrator($email, $firstName, $lastName)
    {
        $locale = $this->settings->get('app.defaultLanguage');
        $sendEmail = (new TemplatedEmail())
            ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
            ->to(new Address($this->settings->get('app.email.administrator.reception')))
            ->subject($this->translator->trans('email.template_email.your_registration_admin_request', [], 'email', $locale))
            ->text('')
            ->htmlTemplate('template_email/receive_registration_admin.html.twig')
            ->context([
                'user' => $firstName . ' ' . $lastName,
                'locale' => $locale,
                'emailUser' => $email,
                'appFromName' => $this->settings->get('app.fromName'),
            ]);

        $this->mailer->send($sendEmail);
    }

    /**
     * @Route("/change-password", name="api-change-password",methods={"POST"})
     */
    public function changePassword(
        Request $request,
        UserPasswordHasherInterface $passwordEncoder,
        EntityManagerInterface $em,
        UserPasswordValidatorService $userPasswordValidatorService
    ): Response {
        $locale = $this->settings->get('app.defaultLanguage');
        try {
            $user = $this->getUser();
            if (!$user) {
                return $this->sendResponse([
                    'status' => Response::HTTP_UNAUTHORIZED,
                    'error' => true,
                    'data' => 'User login required',
                ]);
            }
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);

            $content = json_decode($request->getContent(), true);
            $password = $content['actualPassword'] ?? null;
            $newPassword = $content['newPassword'] ?? null;

            if (empty($password) || empty($newPassword)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Current and new password required',
                ]);
            }

            $locale = $user->getLocaleCampus() ?? $this->settings->get('app.defaultLanguage');

            // Validate password
            $validationResult = $userPasswordValidatorService->isPasswordValid($newPassword, $locale);
            if (true !== $validationResult) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => $validationResult,
                ]);
            }

            if (!$passwordEncoder->isPasswordValid($user, $password)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => $this->translator->trans('email.messages_to_user.password_does_not_match', [], 'email', $locale),
                ]);
            }

            // Password is valid, set new password
            $user->setPassword($this->userPasswordHasher->hashPassword($user, $newPassword));
            $em->persist($user);
            $em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->translator->trans('email.messages_to_user.change_pasword', [], 'email', $locale),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_change_password', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ]);
        }
    }

    /**
     * @Route("/change-avatar", name="api-change-avatar",methods={"POST"})
     */
    public function changeAvatar(Request $request): Response
    {
        try {
            /** @var User|null $user */
            $user = $this->getUser();
            if (!$user) {
                return $this->sendResponse([
                    'status' => Response::HTTP_UNAUTHORIZED,
                    'error' => true,
                    'data' => 'Unauthorized',
                ]);
            }

            $locale = $user->getLocaleCampus() ?? $this->settings->get('app.defaultLanguage');

            /** @var UploadedFile|null $avatarFile */
            $avatarFile = $request->files->get('userAvatar');
            $dataAvatar = $request->get('dataAvatar');
            if (!$avatarFile) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => $this->translator->trans('email.messages_to_user.error_avatar', [], 'email', $locale),
                ]);
            }

            $mimeType = $avatarFile->getMimeType();
            if (false === strpos($mimeType, 'image')) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Invalid image format',
                ]);
            }

            if (empty($user->getAvatar())) {
                $this->deleteImage($user->getAvatar());
            }

            $user->setAvatar('')
                ->setAvatarFile($avatarFile)
                ->setDataAvatar($dataAvatar);

            $this->em->persist($user);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'avatar' => $user->getAvatar(),
                    'message' => $this->translator->trans('email.messages_to_user.change_avatar', [], 'email', $locale),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_avatar', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ]);
        }
    }

    public function deleteImage($file)
    {
        $filename = $this->settings->get('app.avatar_uploads_path') . DIRECTORY_SEPARATOR . $file;

        if (file_exists($filename) && is_file($filename)) {
            $success = unlink($filename);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $filename");
            }
        }
    }

    /**
     * @Route("/languages", name="api-languages",methods={"GET"})
     *
     * @return Response
     */
    public function languages()
    {
        $lenguage = $this->getLanguages();
        $flipped = array_flip($lenguage);

        foreach ($flipped as $key => $value) {
            $name = Locales::getName($key, $key);
            $flipped[$key] = ucfirst($name);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $flipped,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/change-information-user", name="api-change-information-user",methods={"POST"})
     */
    public function changeInformationUser(Request $request, EntityManagerInterface $em): Response
    {
        $code = Response::HTTP_OK;
        $error = false;

        try {
            $requestData = json_decode($request->getContent(), true);

            $firstName = $requestData['firstName'];
            $lastName = $requestData['lastName'];
            $lenguage = $requestData['language'];

            // $getTypeLenguage = $this->getLanguages();

            if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
                $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            } else {
                $user = null;
            }

            $languageDefault = $this->settings->get('app');
            $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $languageDefault;

            if ($user) {
                $user->setFirstName($firstName);
                $user->setLastName($lastName);
                $user->setLocaleCampus($lenguage);
                $em->persist($user);
                $em->flush();

                //  $this->requestStack->getSession()->set('_locale', $lenguage);
                $response = [
                    'status' => $code,
                    'error' => $error,
                    'data' => [
                        'message' => $this->translator->trans('email.messages_to_user.modified_information', [], 'email', $locale),
                        'user' => [
                            'firstName' => $user->getFirstName(),
                            'lastName' => $user->getLastName(),
                            'locale' => $user->getLocaleCampus(),
                        ],
                    ],
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_change_information', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/change-user-locale", name="api-change-user-locale",methods={"POST"})
     */
    public function changeLocale(Request $request, EntityManagerInterface $em): Response
    {
        $user = $this->getUser();
        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'error' => true,
                'data' => 'Unauthorized',
            ]);
        }

        try {
            $requestData = json_decode($request->getContent(), true);

            $language = $requestData['language'];
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $user->getUsername()]);
            $locale = $user->getLocaleCampus() ?? $this->settings->get('app.defaultLanguage');

            $user->setLocaleCampus($language);
            $em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('email.messages_to_user.modified_information', [], 'email', $user->getLocaleCampus()),
                    'user' => [
                        'locale' => $user->getLocaleCampus(),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_change_information', [], 'email', $locale ?? $this->settings->get('app.defaultLanguage')) . ' {' . $e->getMessage() . '}',
            ]);
        }
    }

    /**
     * @Route("/accept-policies", name="api-user-accept-policies",methods={"POST"})
     *
     * @return Response
     */
    public function acceptPolicies(Request $request)
    {
        $code = Response::HTTP_OK;
        $error = false;

        try {
            if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
                $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            } else {
                $user = null;
            }

            $languageDefault = $this->settings->get('app.defaultLanguage');
            $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $languageDefault;

            if ($user) {
                $user->setMetaByLabel('inactivityPeriodAccepted', date('Y-m-d H:i:s'));
                $user->setMetaByLabel('policiesAccepted', date('Y-m-d H:i:s'));
                $this->em->flush();

                $response = [
                    'status' => $code,
                    'error' => $error,
                    'data' => null,
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_change_information', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/delete-avatar", name="api-delete-avatar",methods={"DELETE"})
     */
    public function resetUserAvatar(): Response
    {
        try {
            /** @var User|null $user */
            $user = $this->getUser();
            if (!$user) {
                return $this->sendResponse([
                    'status' => Response::HTTP_UNAUTHORIZED,
                    'error' => true,
                    'data' => 'Unauthorized',
                ]);
            }

            if (!empty($user->getAvatar())) {
                $this->deleteImage($user->getAvatar());
            }

            $locale = $user->getLocaleCampus() ?? $this->settings->get('app.defaultLanguage');

            $user->setAvatar(null)
                ->setDataAvatar(null);
            $this->em->persist($user);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_NO_CONTENT,
                'error' => false,
                'data' => [],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_avatar', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ]);
        }
    }

    /**
     * @Route("/registration/extra-fields", name="api-get-user-extra-fields",methods={"GET"})
     */
    public function getUserExtraFields(Request $request): Response
    {
        $locale = $request->get('locale', $this->settings->get('app.defaultLanguage'));
        $settingsExtraFields = $this->settings->get('app.user.extra_fields');

        if (\is_null($settingsExtraFields)) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => $this->translator->trans('settings.user_extra_fields.not_found', [], 'settings', $locale),
            ]);
        }

        $settingsExtraFields = json_decode($settingsExtraFields, true);
        $result = [];

        if (\is_array($settingsExtraFields)) {
            foreach ($settingsExtraFields as $extraField) {
                if (!isset($extraField['name']) && !isset($extraField['required']) && !isset($extraField['label']) && !isset($extraField['type'])) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                        'error' => true,
                        'data' => $this->translator->trans('settings.user_extra_fields.invalid_field', [], 'settings', $locale),
                    ]);
                }

                $result[] = [
                    'name' => $extraField['name'],
                    'required' => $extraField['required'],
                    'label' => $this->getUserExtraFieldsLabelTranslation($extraField, $locale),
                    'type' => $extraField['type'],
                    'options' => 'select' === $extraField['type'] ? $this->getUserExtraFieldsOptionsTranslation($extraField, $locale) : null,
                ];
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result,
        ]);
    }

    private function getUserExtraFieldsLabelTranslation(array $extraField, string $language): ?string
    {
        $label = null;

        if (isset($extraField['label']['translations']) && \is_array($extraField['label']['translations'])) {
            $label = $this->getUserExtraFieldsTranslation($extraField['label']['translations'], $language);
        }

        return $label ?? $extraField['label']['default'];
    }

    private function getUserExtraFieldsOptionsTranslation(array $extraField, string $language): ?array
    {
        $options = [];
        if (isset($extraField['options']) && \is_array($extraField['options'])) {
            foreach ($extraField['options'] as $option) {
                if (isset($option['name']['translations']) && \is_array($option['name']['translations'])) {
                    $options[] = [
                        'value' => $option['value'],
                        'name' => $this->getUserExtraFieldsTranslation($option['name']['translations'], $language),
                    ];
                }
            }
        }

        return $options;
    }

    private function getUserExtraFieldsTranslation(array $translations, string $language): ?string
    {
        foreach ($translations as $translation) {
            if ($translation['language'] === $language) {
                return $translation['value'];
            }
        }

        return null;
    }
}
