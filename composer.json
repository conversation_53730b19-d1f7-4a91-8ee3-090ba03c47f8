{"name": "easylearning/easylearning", "description": "EasyLearning", "version": "2.0.0", "type": "project", "license": "proprietary", "require": {"php": ">=8.3", "ext-ctype": "*", "ext-fileinfo": "*", "ext-iconv": "*", "ext-json": "*", "ext-openssl": "*", "ext-zip": "*", "a2lix/translation-form-bundle": "^3.0", "beberlei/doctrineextensions": "^1.3", "composer/package-versions-deprecated": "*********", "cron/cron-bundle": "^2.6", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.3", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^2.8", "easycorp/easyadmin-bundle": "3.5.*", "firebase/php-jwt": "^6.9", "friendsofsymfony/ckeditor-bundle": "^2.2", "friendsofsymfony/rest-bundle": "^3.0", "gesdinet/jwt-refresh-token-bundle": "^1.0.0", "kms/froala-editor-bundle": "^3.1", "knplabs/doctrine-behaviors": "^2.2", "knplabs/knp-paginator-bundle": "^5.6", "knpuniversity/oauth2-client-bundle": "^2.17", "league/tactician-bundle": "*", "lexik/jwt-authentication-bundle": "^2.8", "liip/imagine-bundle": "^2.6", "litesaml/lightsaml": "^4.0.0", "mobiledetect/mobiledetectlib": "^2.8", "mpdf/mpdf": "^8.2", "mynaparrot/plugnmeet-sdk": "^1.1", "nelmio/api-doc-bundle": "^3.6", "nelmio/cors-bundle": "^2.1", "oat-sa/bundle-lti1p3": "*", "oat-sa/lib-lti1p3-ags": "^2.0", "oat-sa/lib-lti1p3-core": "*", "oat-sa/lib-lti1p3-nrps": "^8.0", "phpdocumentor/reflection-docblock": "^5.3", "phpoffice/phpspreadsheet": "^1.18", "phpstan/phpdoc-parser": "^1.2", "scienta/doctrine-json-functions": "^4.3", "sensio/framework-extra-bundle": "^5.5", "stof/doctrine-extensions-bundle": "^1.4", "stripe/stripe-php": "^17.6", "symfony/console": "5.*", "symfony/dotenv": "5.*", "symfony/event-dispatcher": "5.*", "symfony/flex": "^1.3.1", "symfony/framework-bundle": "5.4.*", "symfony/http-client": "5.*", "symfony/ldap": "5.4.*", "symfony/loco-translation-provider": "5.4.*", "symfony/mailchimp-mailer": "5.*", "symfony/mailer": "5.*", "symfony/mime": "5.4.*", "symfony/monolog-bundle": "^3.5", "symfony/password-hasher": "5.4.*", "symfony/property-access": "5.4.*", "symfony/property-info": "5.4.*", "symfony/proxy-manager-bridge": "5.4.*", "symfony/rate-limiter": "5.4.*", "symfony/security-bundle": "5.*", "symfony/serializer": "5.4.*", "symfony/twig-bundle": "5.*", "symfony/validator": "5.4.*", "symfony/var-dumper": "5.4.*", "symfony/webpack-encore-bundle": "^1.16.1", "symfony/yaml": "5.*", "symfonycasts/reset-password-bundle": "^1.3", "twig/cssinliner-extra": "^3.3", "twig/extra-bundle": "^3.3", "twig/intl-extra": "^3.5", "twig/string-extra": "^3.3", "twig/twig": "^2.12|^3.0", "vich/uploader-bundle": "^1.13", "vimeo/vimeo-api": "^3.0"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "doctrine/doctrine-fixtures-bundle": "^3.3", "friendsofphp/php-cs-fixer": "^3.63", "phpunit/phpunit": "^10.5", "rector/rector": "^2.0", "squizlabs/php_codesniffer": "^3.10", "symfony/browser-kit": "5.4.*", "symfony/css-selector": "5.4.*", "symfony/debug-bundle": "^5.4", "symfony/maker-bundle": "^1.18", "symfony/phpunit-bridge": "^7.1", "symfony/stopwatch": "^5.4", "symfony/web-profiler-bundle": "^5.2"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "ckeditor:install --tag=4.22.1": "symfony-cmd", "froala:install": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts", "cghooks add --ignore-lock"], "post-update-cmd": ["@auto-scripts", "cghooks update"], "fix-code:project": "make fix-code-project", "fix-code:staged": "make fix-code-staged", "test": ["make test"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}, "hooks": {"pre-commit": "make fix-code-staged"}}}