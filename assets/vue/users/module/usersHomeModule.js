import { UserModel } from '../models/user.model'
import { make } from 'vuex-pathify'
import axios from 'axios'

const getDefaultState = () => ({
  userList: {
    loading: false,
    pagination: { currentPage: 1, pageSize: 10, totalItems: 0 },
    data: []
  }
})
const state = () => getDefaultState()
export const getters = {
  getUserList: (state) => () => state.userList,
}
export const mutations = {
  ...make.mutations(state),
  SET_USER_LOADING(state, loading) {
    state.userList.loading = loading
  },
  SET_USER_LIST_DATA(state, { data, pagination }) {
    state.userList.data = data
    state.userList.pagination = pagination
  }
}
export const actions = {
  async loadUserList({ commit, state }) {
    if (state.userList.loading) return null
    commit('SET_USER_LOADING', true)
    // TODO load data
    commit('SET_USER_LIST_DATA', {
      data: new Array(10).fill({ name: 'test', lastName: 'test', email: '<EMAIL>', roles: ['Admin', 'manager', 'Tutor', 'creador', 'Usuario'], points: 99999, avatar: 'https://picsum.photos/200/300' })
        .map((user, index) => new UserModel({ id: index + 1, ...user})),
      pagination: { currentPage: 1, totalItems: 15 }
    })
    commit('SET_USER_LOADING', false)
  },

  async impersonateUser({}, userId) {
    const { data } = await axios.post('/admin/user/impersonate', { user: userId}).catch(() => ({ data: '' }))
    if (data?.url?.length) window.open(data.url, '_blank').focus()
  },

  async viewUserInfo({}, viewUrl = '') {
    if (!viewUrl.length) return null
    window.location.href = viewUrl
  },

  async deleteUser({}, deleteUrl) {
    if (!deleteUrl.length) return null
    await axios.post(deleteUrl).catch(() => {})
  },
}
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};