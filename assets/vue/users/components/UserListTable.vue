<script>
import Pagination from '../../admin/components/Pagination.vue'
import BaseSwitch from '../../base/BaseSwitch.vue'

export default {
  name: "UserListTable",
  components: { BaseSwitch, Pagination },
  props: {
    currentPage: { type: Number, default: 1 },
    userList: { type: Array, default: () => [] },
    totalUsers: { type: Number, default: 0 },
    loading: { type: Boolean, default: false },
  },
  methods: {
    updatePage(page) {
      if (this.loading) return null;
      this.$emit('update-page', page)
    },
    toggleUserActive(index) {
      if (this.userList[index].isUpdating || !(this.$isAdmin() || this.$isSuperAdmin())) return null
      this.userList[index].isUpdating = true
      this.$emit('toggle-user-inactive', index)
    }
  }
}
</script>

<template>
<div class="UserListTable">
  <div class="table-responsive">
    <table class="table table-condensed w-100">
      <thead>
      <tr>
        <th>{{ $t('AVATAR') }}</th>
        <th>{{ $t('SUBSCRIPTION.EMAIL') }}</th>
        <th>{{ $t('SUBSCRIPTION.NAME') }}</th>
        <th>{{ $t('SUBSCRIPTION.LAST_NAME') }}</th>
        <th>{{ $t('USER.ROLES.TITLE') }}</th>
        <th>{{ $t('LIBRARY.ACTIVE') }}</th>
        <th>{{ $t('COURSE.SCORE') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr
        v-for="(user, index) in userList"
        :key="user.key">
        <td>
          <img :src="user.avatar" class="userAvatar" alt="">
        </td>
        <td>
          <span
            class="text-primary cursor-pointer"
            @click="$emit('view', user.viewUrl)">
            {{ user.email }}
          </span>
        </td>
        <td>{{ user.firstName }}</td>
        <td>{{ user.lastName }}</td>
        <td>
          <div class="td-roles">
            <div
              class="roles"
              v-for="role in user.roles"
              :key="role.key">
              <span class="badge badge-secondary">{{ role.letter }}</span>
              <span class="message">{{ role.value }}</span>
            </div>
          </div>
        </td>
        <td>
          <BaseSwitch
            :tag="user.key + '_cb'"
            v-model="user.isActive"
            @change="toggleUserActive(index)"
          />
        </td>
        <td>{{ user.score }}</td>
        <td>
          <div class="dropdown">
            <button
              class="btn btn-default"
              type="button"
              :id="`dropdown-menu-${user.key}`"
              data-bs-toggle="dropdown"
              aria-expanded="false"
            >
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul
              class="dropdown-menu"
              :aria-labelledby="`dropdown-menu-${user.key}`">
              <li
                role="button"
                @click="$emit('view', user.viewUrl)">
                {{ $t("VIEW") }}
              </li>
              <li
                v-if="$isAdmin() || $isSuperAdmin()"
                role="button"
                @click="$emit('impersonate', user.id)">
                {{ $t("ANNOUNCEMENT.STUDENTTAB.IMPERSONATE") }}
              </li>
              <li
                role="button"
                @click="$emit('update', user.id)">
                {{ $t("EDIT") }}
              </li>
              <li
                class="bg-danger text-white"
                role="button"
                @click="$emit('delete', user.deleteUrl)">
                {{ $t("DELETE") }}
              </li>
            </ul>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
  <Pagination
    class="justify-content-end"
    :prop-current-page="currentPage"
    :total-items="totalUsers"
    :disabled="loading"
    @current-page="updatePage"
  />
</div>
</template>

<style scoped lang="scss">
.UserListTable {
  .userAvatar {
    width: 3rem;
    height: 3rem;
    object-fit: cover;
    object-position: center;
  }
  
  .td-roles {
    display: flex;
    gap: 0.5rem;
    
    .roles {
      position: relative;
      display: grid;
      place-content: center;
    }
    
    .message {
      position: absolute;
      display: none;
      z-index: 9;
      top: -1.7rem;
      left: -100%;
      font-size: 0.8rem;
      padding: 0.15rem 0.5rem;
      border-radius: 3px;
      background: var(--color-primary-light);
      text-transform: capitalize;
      
      &:hover {
        display: block;
      }
    }
    
    .badge {
      cursor: pointer;
    }
    
    .badge:hover {
      background-color: rgba(0, 0, 0, 0.3);
      & + .message {
        display: block;
      }
    }
  }
  
  .dropdown-menu {
    li {
      padding: 0.25rem 1rem;
      text-align: center;
      
      &:hover {
        background-color: var(--color-primary-lighter);
      }
    }
  }
}
</style>