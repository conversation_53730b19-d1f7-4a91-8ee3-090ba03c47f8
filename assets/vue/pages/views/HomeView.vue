<template>
  <div class="home">
    <home
      title="PAGES.HOME.TITLE"
      description="PAGES.HOME.DESCRIPTION"
      src-thumbnail="/assets/imgs/pages.svg"
    >
      <template v-slot:content-main>
        <div
          class="col-12 d-flex flex-row align-items-center justify-content-center"
          v-if="loading"
        >
          <spinner />
        </div>
        <div v-else>
          <div class="col-12 d-flex align-items-center justify-content-end mt-0">
            <router-link class="btn btn-primary" :to="{ name: 'PagesCreate' }">{{ $t('PAGES.CREATE_PAGE') }}</router-link>
          </div>

          <table class="table table-condensed mt-5" >
            <thead>
              <tr>
                <th>{{ $t("PAGES.SORT") }}</th>
                <th>
                  {{ $t("PAGES.NAME") }}
                </th>
                <th>{{ $t("PAGES.ACTIVE") }}</th>
                <th>
                  <label-with-info
                      :info="$t('PAGES.INITIAL_INFO') + ''"
                      id="initial-info"
                      location="top"
                    >
                    {{ $t("PAGES.INITIAL") }}
                  </label-with-info>
                </th>
                <th style="text-align: right;">{{ $t("PAGES.ACTIONS") }}</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="(c, index) in pagesbd" 
                :key="c.id"
                draggable
                @dragstart="startDrag($event, index)"
                @drop="onDrop(index)"
                @dragover.prevent
                @dragenter.prevent
              >
                <td><i class="fa fa-bars sections"></i></td>
                <td>                 
                  <router-link :to="{ name: 'PagesUpdate', params: {...$route.params, id: c.id} }">
                    {{ c.name }}
                  </router-link >                           
                </td>
                <td>
                  <BaseSwitch
                    :tag="`switcher-pages-${c.id}`"
                    v-model="c.active"
                    @change="changeStatus(c)"
                    :disabled="c.isMain"
                    theme="light"              
                  />
                </td>
                <td>
                    <button @click="ChangeIsMain(c)" type="button" style="border: none; background-color: transparent;">
                      <i v-if="c.isMain" class="fas fa-flag sections"></i>
                      <i v-else class="fas fa-flag" style="color:#CBD5E1" ></i>
                    </button>
                </td>
                <td>
                  <div class="d-flex  justify-content-end ">
                    <router-link class="btn btn-sm btn-primary ml-1" :to="{ name: 'PagesUpdate', params: {...$route.params, id: c.id} }">
                      <i class="fa fa-pen"></i>
                    </router-link>
                    <button @click="deletePages(c)" type="button" class="btn btn-sm btn-danger ms-2">
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      </template>
    </home>
  </div>
</template>
<script>
import Spinner from "../../admin/components/base/Spinner.vue";
import Home from "../../base/Home.vue";
import {get} from "vuex-pathify";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";

export default {
  name: "PageGroup",
  components: {Spinner, Home, LabelWithInfo},    
  data() {
    return {
      sortBy: "name",
      sortDesc: false,
      dragItem: null,
      dropItem: null,
      antIdMain: null,
    };
  },
  computed: {
    ...get("pagesModule", ["loading", "getPagesBD"]),

    pagesbd() {
      return this.getPagesBD();
    },
  },
  async created() { 
    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('PAGES.TITLE'),
        params: {}
      }
    });

    await this.$store.dispatch('pagesModule/load', '/admin/pages/all'); 
  },
  methods: {    
    async ChangeIsMain(page){
      if(page.isMain){
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR1") + "");
      }else if(!page.active){
        this.$toast.error(this.$t("COURSE.COURSE_SECTION.ISMAIN_ERROR2") + "");
      }else{
        await this.$store.dispatch("pagesModule/changeIsMain", {
          endpoint: `/admin/pages/${page?.id}/change-is-main`,
        });
      }
    },
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = 'move'
      evt.dataTransfer.effectAllowed = 'move'
    },
    
    async onDrop(index) {      
      let data = this.orderNewSort( this.dragItem,  index+1);

      this.dropItem = this.pagesbd.splice(this.dragItem, 1)[0];
      this.pagesbd.splice(index,0,this.dropItem);
      this.dropItem=null;

      await this.$store.dispatch("pagesModule/changePosition", {
        endpoint: `/admin/pages/changePosition`,
        requestData: { pages:  data},
      });
    },

    orderNewSort(antPosSort, newPosSort ){
      let newSortSections = [];

      let elmentTras = {
        id: this.pagesbd[antPosSort].id,
        newSort: newPosSort
      }
      newSortSections.push(elmentTras);

      if(this.dragItem > newPosSort){
        this.pagesbd.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }
          
          if(item.position >= newPosSort && item.position <= antPosSort){
            element.id = item.id;
            element.newSort = item.position + 1;
            newSortSections.push(element);
          }
        });
      }else{
        this.pagesbd.forEach((item) => {
          let element = {
            id:null,
            newSort:null
          }
          if(item.position > antPosSort+1 && item.position <= newPosSort){
            element.id = item.id;
            element.newSort = item.position - 1;
            newSortSections.push(element);
          }
        });
      }

      return newSortSections;
    },
    
    async changeStatus(pages) {
      if(pages.isMain){
        this.$toast.error(this.$t("PAGES.ISMAIN_ERROR1") + "");
      }else{
        await this.$store.dispatch("pagesModule/changeStatus", {
          endpoint: `/admin/pages/${pages?.id}/active`,
        });
      }
    },
    deletePages(pages) {
      this.$alertify.confirmWithTitle(
        this.$t("DELETE"),
        this.$t("COMMON_AREAS.QUESTION_DELETE"),
        () => {
          this.$store
            .dispatch("pagesModule/deletePages", {
              endpoint: `/admin/pages/${pages?.id}/delete`,
            })
            .then((r) => {
              const index = this.pagesbd.findIndex((item) => item.id === pages.id);
              this.pagesbd.splice(index, 1); 
              this.$toast.success(this.$t("DELETE_SUCCESS") + "");
            })
            .catch((e) => {
              this.$toast.error("DELETE_FAILED");
            })
            .finally(() => {

            });
        },
        () => {}
      );
    },
  }
}
</script>

 <style scoped lang="scss"> 
.home {
  .sort-asc::after,
  .sort-desc::after {
    content: " ▼";
    font-size: 10px;
    opacity: 0.5;
    margin-left: 5px;
  }

  .sort-desc::after {
    content: " ▲";
  }

  th {
    cursor: pointer;
  }

  th.sort-asc:hover::after,
  th.sort-desc:hover::after {
    opacity: 1;
    cursor: pointer;
  }

  .sections {
    color: var(--color-primary);
  }
}
</style>
