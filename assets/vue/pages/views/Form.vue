<template>
  <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
    <spinner/>
  </div>
  <div class="FormView" v-else>
    <div class="p-0 pt-1 pb-1 m-5">
      <h5 class="CurrentStepSectionTitle">
        <span><i class="fa fa-folder" aria-hidden="true"></i></span>
        {{ $t("PAGES.LABEL.BASIC_INFORMATION") }}
      </h5>
      <div
        class="basic-info--translation"
      >

        <div class="row align-items-start">
          <div class="col-2 pe-3 border-right"></div>
          <template
              v-for="(t, index) in translations"
          >
            <Translation
                v-if="t.locale === locale"
                v-model="locale"
                :warning="warningLocales"
                :key="`locale-${index}`"
            >
              <template v-slot:content>
                <div class="form-group col-12">
                  <label
                  >{{ $t("NAME") }}<span style="color: red">*</span></label
                  >
                  <input type="text" class="form-control" v-model="t.name" />
                </div>
                <div class="form-group col-12">
                  <label>{{ $t("DESCRIPTION") }}</label>
                  <froala
                      tag="textarea"
                      v-model="t.description"
                      :config="froalaDescriptionConfig"
                  ></froala>
                </div>
              </template>
            </Translation>
          </template>
        </div>
      </div>
    </div>
  </div>
  
</template>

<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../base/BaseSwitch.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import Translation from "../../common/components/Translation.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm, Translation, Spinner},
  data() {
    return {
      locale: 'es',
      translations:[],
      pages: {},
      warningLocales: {},
    };
  },
  computed: {
    ...get("pagesModule", ["loading", "getPagesBD",  "getLanguages"]),

    locales: get("localeModule/locales"),
    defaultLocale: get("localeModule/defaultLocale"),

    pagesbd() {
      return this.getPagesBD();
    },
    languages() {
      return this.getLanguages();
    },
    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 150,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },
  },
  async created() {
    let isUpdate = this.$route.name === 'PagesUpdate';
    this.setButtons(isUpdate);
    this.locale = this.defaultLocale;
    this.translations = this.initTranslation();
   
    if(isUpdate){
      this.pages = this.pagesbd.find(c => c.id === this.$route.params.id);
      this.translations = this.getTranslations(this.pages.translations);
    }

    this.setWarningLocales(this.translations);

    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('PAGES.UPDATE_PAGES') : this.$t('PAGES.CREATE_PAGES'),
        params: {}
      }
    });

  },
  mounted() {
    this.$eventBus.$on('onSubmit', () => {
      this.submit();
    });
    this.$eventBus.$on("onDelete", (e) => {
      this.remove();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off('onSubmit');
  },
  methods: {
    setButtons(isUpdate){
      const actions = [];

      if(isUpdate){
        actions.push({
          name: this.$t("DELETE"),
          event: "onDelete",
          class: "btn btn-danger",
        });
      }  

      actions.push({
        name: this.$t("SAVE"),
        event: "onSubmit",
        class: "btn btn-primary",
      });
      
      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions,
      });
    },
    returnToList() {
      this.$router.push({name: 'Home', params: this.$route.params});
    }, 
    setWarningLocales(translations) {
      translations.forEach((t) => {
        let warning = t.locale !== this.locale;
        if (t.locale !== this.locale)
          warning = t.name.length < 1 || t.description.length < 1;

        this.warningLocales[t.locale] = warning;
      });
    },
    initTranslation(){
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        translations.push({
          locale: k,
          name: "",
          description: "",
        });
      });      

      return translations;
    },
    getTranslations(pagestranslations) {
      let translations = [];
      const keys = Object.keys(this.locales);
      keys.forEach((k) => {
        const translated = pagestranslations.find((e) => e.locale === k);
        translations.push({
          locale: k,
          name: translated?.name ?? "",
          description: translated?.description ?? "",
        });
      });

      return translations;
    },
    getTranslationName() {
      let translationName = this.translations.filter(
        (translation) => translation.locale === this.defaultLocale
      );
      return translationName[0].name;
    },
    getTranslationDescription() {
      let translationName = this.translations.filter(
        (translation) => translation.locale === this.defaultLocale
      );
      return translationName[0].description;
    },
    async submit() {
      this.pages.name =  this.getTranslationName();
      this.pages.description = this.getTranslationDescription();

       if (!this.pages.name || this.pages.name.length < 1) {
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
        return;
      } 

      if (this.$route.name === "PagesCreate") {
        this.pages.active = true;
       // this.pages.position = this.pagesbd.length+1;
      }
      this.pages.translations = this.translations;

      const update = this.$route.name === "PagesUpdate";
      const endpoint = update
        ? "/admin/pages/update"
        : "/admin/pages/create";
      await this.$store.dispatch("pagesModule/save", {
        endpoint: endpoint,
        requestData: this.pages,
      });

      this.returnToList();
    },
    remove() {
      this.$alertify.confirmWithTitle(
        this.$t("DELETE"),
        this.$t("COMMON_AREAS.QUESTION_DELETE"),
        () => {
          this.$store
            .dispatch("pagesModule/deletePages", {
              endpoint: `/admin/pages/${this.pages?.id}/delete`,
            })
            .then((r) => {
              this.$toast.success(this.$t("DELETE_SUCCESS") + "");
            })
            .catch((e) => {
              this.$toast.error("DELETE_FAILED");
            })
            .finally(() => {
              this.returnToList();
            });
        },
        () => {}
      );
    },
  }
}
</script>

<style scoped lang="scss">
.FormView {
  .Header {
    padding: 1rem;
    & > h4 {
      font-size: 22px;
      color: var(--color-neutral-darkest);
    }
  }

  .tab-pane {
    padding: 1rem;
  }

  .CurrentStepSectionTitle {
    font-weight: bold;
    border-bottom: 1px solid $base-border-color;
    width: 100%;
    font-size: 20px;
    padding-bottom: 0.3rem;
  }
}
</style>
