import  initVueApp, { getI18n } from "../common/mainApp";
import '../../css/courseStats.scss'
import '../../css/vueMultiSelect.css'

import store from './store';
import router from "./router";

const i18n = getI18n(store);

const app = initVueApp(
    {
        store,
        router,
        i18n,
        paramMounted: () => {
            document.title = i18n.t('REPORTS_DIPLOMAS');
        }
    }
);
app.$mount('#app');
