import { make } from "vuex-pathify";

const state = {
    config: [],
    defaultImageB64: 'data:image/svg+xml;base64,' + 'PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNi4wLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkNhcGFfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSIxMDAuMzc1cHgiIGhlaWdodD0iNjRweCIgdmlld0JveD0iMC4xMjUgMCAxMDAuMzc1IDY0IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAuMTI1IDAgMTAwLjM3NSA2NCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+DQo8Zz4NCgk8Zz4NCgkJPHBhdGggZmlsbD0iIzQ4NEY1NiIgZD0iTTY2LjQ1NiwxNS41NjNIMzQuMjE5Yy0xLjY2MSwwLTMuMDI5LDEuNDY2LTMuMDI5LDMuMjIzdjI2LjQyNWMwLDEuNzYsMS4zMTgsMy4yMjUsMi45OCwzLjIyNWgzMi4yMzcNCgkJCWMxLjY2MiwwLDMuMDI4LTEuNDY1LDMuMDI4LTMuMjI1VjE4Ljc4N0M2OS40MzYsMTcuMDI5LDY4LjExNywxNS41NjMsNjYuNDU2LDE1LjU2M3ogTTYyLjUsMjguNjA1DQoJCQljLTAuMzkyLTAuMzkxLTAuODMxLTAuNTM4LTEuMjIyLTAuNTM4Yy0wLjQ4OCwwLTAuOTc3LDAuMjQ0LTEuMzE5LDAuNTM4Yy0wLjQzOSwwLjM5LTcuMzI2LDYuMTU0LTkuOTY0LDguNGw0Ljg4NSw0LjE1Mg0KCQkJYzAuMjQzLDAuMjk0LDAuMjQzLDAuNzMzLDAsMC45NzhjLTAuMTQ3LDAuMTQ2LTAuMjkzLDAuMTk1LTAuNDM5LDAuMTk1Yy0wLjE0NywwLTAuMzQyLTAuMDUtMC40MzktMC4xOTUNCgkJCWMwLDAtOC4zNTQtNy42Mi05LjM3OS04LjY0NmMtMC4yOTMtMC4zNDMtMC42ODQtMC42ODUtMS4xNzMtMC42ODVjLTAuMzQyLDAtMC43ODEsMC4xOTUtMS4zNjcsMC43ODENCgkJCWMtMS44MDgsMS45MDUtNy42NjksOC4xMDktNy42NjksOC4xMDlsLTAuMDQ5LTIxLjQ0NGMwLTAuODgsMC42ODQtMS42MTIsMS41MTUtMS42MTJoMjguOTE2YzAuODMxLDAsMS41MTUsMC43MzIsMS41MTUsMS42MTINCgkJCWwwLjA1LDEyLjExNEM2Ni4zNTksMzIuNDE2LDYzLjk2NSwzMC4wNyw2Mi41LDI4LjYwNXoiLz4NCgkJPHBhdGggZmlsbD0iIzQ4NEY1NiIgZD0iTTUzLjkwMywyNS45MThjMCwyLjU0LTIuMDA0LDQuNTkyLTQuNDk1LDQuNTkyYy0yLjQ5LDAtNC40OTMtMi4wNTItNC40OTMtNC41OTJzMi4wMDMtNC42NCw0LjQ5My00LjY0DQoJCQlDNTEuODk5LDIxLjMyNyw1My45MDMsMjMuMzc4LDUzLjkwMywyNS45MTh6Ii8+DQoJPC9nPg0KPC9nPg0KPC9zdmc+DQo='
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
        getConfig(state) { return state.config; },
    },
    mutations: {
        ...make.mutations(state),
        setConfigKey(state, { key, value }) {
            state.config[key] = value;
        }
    },
    actions: {
        setConfig({ commit }, config) {
            commit("SET_CONFIG", config);
        }
    }
}
