import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    chatChannelTypes: []
};

export const actions = {
    async getChannelTypes({ commit }, { base = null}) {
        axios.post('/admin/chat/available-channel-types', { base }).then(r => {
            const { data, error } = r.data;
            if (!error) commit('SET_CHAT_CHANNEL_TYPES', data);
        }).catch(() => {

        })
    },
    async getChannels({}, { serverId = null, channelId = null, all = true, cEntityId = null, cEntityType = null}) {
        return axios.post('/admin/chat/channels', { serverId, channelId, all, cEntityId, cEntityType })
            .then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },
    async getMessages({}, { channelId }) {
        return axios.post("/admin/chat/channel/messages", { channelId })
            .then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },

    async saveNewChannel({}, channelInfo) {
        return axios.post('/admin/chat/channel/create', channelInfo).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },

    async updateChat({}, channelInfo){
      const result = await axios.post('/admin/chat/channel/update', channelInfo);
      return result.data;
    },

    async getChannelUsers({}, { channelId }) {
        return axios.post('/admin/chat/channel/users', { channelId }).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },

    async addUserToChannel({}, { channelId, userId }) {
        return axios.post('/admin/chat/channel/add-user', { channelId, userId }).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },

    async deleteUserFromChannel({}, { channelId, userId }) {
        return axios.post('/admin/chat/channel/remove-user', { channelId, userId }).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },
    async sendMessage({}, data) {
        return axios.post('/admin/chat/message/send', data).then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },
    async removeChat({}, { channelId, parentId }) {
      const result = await axios.post('/admin/chat/channel/remove', { channelId, parentId });
      return result.data;
    }
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
    },
    mutations: {
        ...make.mutations(state),
    },
    actions
}
