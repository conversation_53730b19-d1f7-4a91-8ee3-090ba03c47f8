import {make} from "vuex-pathify";

const state = {
    loading: false,
    message: ''
};

const mutations = {
    ...make.mutations(state)
};

const getters = {
    ...make.getters(state)
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: {
        setLoading({ commit }, {loading, message = ''}) {
            commit('SET_LOADING', loading);
            commit('SET_MESSAGE', message);
        },
        setMessage({ commit }, message = '') {
            commit('SET_MESSAGE', message);
        },
    }
}
