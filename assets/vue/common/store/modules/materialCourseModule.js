import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    loading: true,
    materials: [],
    fileBasePath: '',
    allowedFileTypes: [
        {
            name: 'PDF',
            accept: "application/pdf",
            multiple: true,
            type: 1
        },
        {
            name: 'VIDEO',
            accept: "video/*",
            multiple: false,
            type: 2
        },
        {
            name: 'COMPRESSED',
            accept: ".zip, .rar",
            multiple: true,
            type: 3
        },
        {
            name: 'IMAGE',
            accept: "image/*",
            multiple: true,
            type: 4
        },
        {
            name: 'OFFICE',
            accept: ".doc, .docx, .docx, .docm, .dot, .dotx, .odt, dotm, .cvs, .dbf, .xla, .xls, .xlsb, .xlsm, .xlsx, .pptx, .pptm, .ppt, .mpt, .mpx, .mpp, .vsdx, .vsdm, .vssx, .vssm, .vstx, .vstm, .accdb",
            multiple: true,
            type: 5
        },
        {
            name: 'TEXT',
            accept: ".txt",
            multiple: true,
            type: 6
        },
    ]
};

const mutations = {
    ...make.mutations(state),
};

const getters = {
    ...make.getters(state)
};

/**
 * Share common state for froala editor
 */
export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: {
        async setMaterialCourseDownloadable({}, { id, downloadable }) {
            const url = `/admin/material-course/${id}/downloadable`;
            const result = await axios.put(url, { downloadable });
            return result.data;
        },

        async setMaterialCourseVisibitily({}, { id, visibitily, announcement }) {
            const url = `/admin/announcement-material-course/${id}/visibitily`;
            const result = await axios.put(url, { visibitily, announcement });
            return result.data;
        },

        async setMaterialCourseActive({}, { id, isActive, announcement }) {
            const url = `/admin/announcement-material-course/${id}/active`;
            const result = await axios.put(url, { isActive, announcement });
            return result.data;
        },

        async deleteMaterialCourse({}, id ) {
            const url = `/admin/announcement-material-course/${id}`;
            const result = await axios.delete(url);
            return result.data;
        },

        async getAnnouncementMaterials({ commit  }, id) {
            commit('SET_LOADING', true);
            try {
                const url = `/admin/announcement/${id}/materials`;
                const result = await axios.get(url);
                const { data, basePath } = result.data;
                commit('SET_MATERIALS', data.map((material) => {
                    const newMaterial = { ...material }
                    newMaterial.disableDownload = (newMaterial.type === '2')
                    newMaterial.disableVisible = (![1,2,4].includes(+newMaterial.type))
                    return newMaterial
                }));
                commit('SET_FILE_BASE_PATH', basePath);
                // return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        }
    }
}
