<template>
  <div class="coupleType">
    <b>Pareja {{ index }}/{{ total }}:</b> <span class="text-capitalize">{{ question.text }}</span> -
    <span class="text-capitalize" v-if="details" :class="{'text-success': answer.correct, 'text-danger': answer.incorrect}">{{ answer.text }}</span>
    <span class="text-capitalize" v-else>{{ answer.text }}</span>
  </div>
</template>

<script>
import { attemptQuestions } from '../../models/UserStatsModel'

export default {
  name: "coupleType",
  props: {
    index: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    details: { type: Boolean, default: false },
    question: { type: attemptQuestions, default: () => (new attemptQuestions()) },
  },
  computed: {
    indexValue() {
      return this.index ? `${this.index} ` : ''
    },
    answer() {
     return this.question.answers[this.question.answers.length - 1] || { correct: false, incorrect: false, text: '' }
    }
  }
}
</script>