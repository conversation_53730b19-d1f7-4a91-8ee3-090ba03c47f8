<template>
  <div class="UserCourseDetails">
    <BaseModal
      :identifier="identifier"
      size="modal-xl"
      padding="0"
      :title="userData.fullName"
      @close="chapterSelected = undefined"
    >
      <div class="row mx-0 courseDataModal" :key="'course_data' + courseId">
        <div class="col-lg-3 col-md-4 col-sm-12 bg-gray py-3" :class="courseData.imgCss">
          <h4 class="font-weight-bold mb-3">{{ courseData.name }}</h4>
          <img class="w-100 h-auto" :src="courseData.image" :alt="courseData.name">
          <p class="courseDesc my-1"><b class="text-capitalize" v-show="userData.type">{{ userData.type }}</b></p>
          <p class="courseDesc my-1" v-show="userData.category">
            <b>{{ $t('LIBRARY.CATEGORY') }}:</b>
            {{ userData.category }}
          </p>
          <p class="courseDesc my-1">
            <b>{{ $t('ANNOUNCEMENT.SESSIONSTAB.START') }}:</b>
            <span>
              {{ formatDate(userData.startString) }}
            </span>
          </p>
          <p class="courseDesc my-1">
            <b>{{ $t('ANNOUNCEMENT.SESSIONSTAB.END') }}:</b> 
            <span>
              {{ formatDate(userData.endString) }}
            </span>
          </p>
          <p class="courseDesc my-1">
            <b>{{ $t('ANNOUNCEMENT.FORM.STEPS.SURVEY') }}:</b>
            <span>
              {{ formatDate(userData.surveyString) }}
            </span>
          </p>
          <div style="margin-top: 10%;">
            <button 
              :class="['btn btn-sm', {'btn-primary' : userData.surveyString != '' && userData.surveyString, 'btn-gray' : userData.surveyString == '' || !userData.surveyString}, 'mr-3']"
              @click="downloadPdf" :disabled="userData.surveyString == '-' || userData.surveyString == '' || !userData.surveyString">
              <i class="fa fa-download"></i> {{ $t('ANNOUNCEMENT.MODALS.PROGRESS_DOWNLOAD_CERTIFICATE') }}
            </button>
          </div>
        </div>
        <UserCourseDetailsChapterList
          v-if="!chapterSelected"
          class="col-lg-9 col-md-8 col-sm-12 py-3 details"
          :user-data="userData"
          :loading-data="sendingExcelData"
          :itinerary="itinerary"
          :allow-export="allowExport"
          @select="setChapterSelected"
          @return="setChapterSelected(undefined)"
          @download="downloadExcel"
        />
        <UserCourseDetailsChapter
          v-else
          class="col-lg-9 col-md-8 col-sm-12 bg-details py-3 details"
          :chapter-data="chapterSelected"
          @return="setChapterSelected(undefined)"
        />
      </div>
    </BaseModal>
  </div>
</template>

<script>


import UserCourseDetailsChapterList from './UserCourseDetailsChapterList.vue'
import UserCourseDetailsChapter from './UserCourseDetailsChapter.vue'
import UserStatsModel from '../models/UserStatsModel'
import axios from 'axios'
import TaskQueueMixin from '../../../mixins/TaskQueueMixin';

export default {
  name: "UserCourseDetails",
  components: { UserCourseDetailsChapter, UserCourseDetailsChapterList },
  props: {
    identifier: { type: String, default: 'userCourseDetailModal' },
    userData: { type: UserStatsModel, default: () => (new UserStatsModel()) },
    courseId: { type: Number, default: 0 },
    course_data: { type: Object, default: () => ({}) },
    itinerary: { type: Boolean, default: false },
    allowExport: { type: Boolean, default: true }
  },
  data: () => ({
    chapterSelected: undefined,
    sendingExcelData: false,
  }),
  computed: {
    translationData: () => (courseTranslations || {}),
    courseData() {
      return this.itinerary ? this.course_data : course_data;
    },
  },
  watch: {
    userId() {
      this.chapterSelected = undefined
    },
    courseId() {
      this.chapterSelected = undefined
    }
  },
  mixins: [
    TaskQueueMixin
  ],
  methods: { 
    formatDate(changedDate) {
      if(!changedDate) return "";
      else{
        let newDate = changedDate.split("T");
        const [year, month, day] = newDate[0].split('-');
        const dateString = `${day}-${month}-${year}`;
        let newHour = newDate[1].split("+")[0];
        return dateString + " " + newHour;
      }
    },
    setChapterSelected(selected) {console.log('setChapterSelected', selected)
      this.chapterSelected = selected
    },
    async downloadPdf(){
      try{
        const result = await axios.get(`/admin/api/v1/course-stats/${this.courseId}/diploma/${this.userData.id}`)

        const link = document.createElement('a');
        link.href = `data:application/pdf;base64,${result.data?.data}`;
        link.download = result.data?.nombre;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      catch(e){ console.log(e) }
    },
    async downloadExcel() {
      if (this.sendingExcelData && !this.allowExport) return null
      this.sendingExcelData = true
      try {
        let data = {
          userId: this.userData.id,
        }

        const dateFrom = document.getElementById('dateFrom') ? document.getElementById('dateFrom').value : null;
        const dateTo = document.getElementById('dateTo') ? document.getElementById('dateTo').value : null;

        if(dateFrom){
          data.dateFrom = dateFrom;
        }

        if(dateTo){
          data.dateTo = dateTo;
        }

        await this.enqueueTask({
          url: `/admin/api/v1/course-stats/${this.courseId}/xlsx`,
          data: data,
          messages: {
              success: `${courseTranslations.export_success}<br/>(${this.$t('COURSE_STATS.EXPORT.ZIP_DIR')})`,
              error: courseTranslations.export_error
          }
        });

      } catch (e) { console.log(e) }
      finally {
        this.sendingExcelData = false
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.courseDataModal {
//  height: clamp(370px, 80svh, 500px);
  
  .details {
    height: 100%;
    
    image {
      max-height: 45%;
      object-fit: contain;
      background-color: #d3d7db;
    }
  }
  @media (max-width: 768px) {
    height: fit-content;
    
    .detailsImgWidth {
      height: 100%;
    }    
    .detailsImgHeight {
      height: 45%;
    }
  }
}
.courseDesc {
  font-size: 0.85rem;
  b {
    font-size: 1rem;
  }
}
.btn-gray {
  background-color: grey;
  color: white;
}
</style>