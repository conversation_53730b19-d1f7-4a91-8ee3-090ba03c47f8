import Vue                 from "vue";
import store               from "./store";
import Highcharts, {color} from "highcharts";
import Stock               from "highcharts/modules/stock";
import noData              from "highcharts/modules/no-data-to-display";
import HighchartsVue       from "highcharts-vue";
import "./filters/index";

import Bar<PERSON>hart        from './components/stats/BarChart';
import LineChart       from "./components/stats/LineChart";
import LineChartDouble from "./components/stats/LineChartDouble";
import <PERSON><PERSON>hart        from "./components/stats/PieChart";
import StackedBar      from "./components/stats/StackedBar";
import mySelect        from './components/html/select';
import myMultiSelect   from './components/html/multiselect';
import inputDate       from "./components/html/input-date";
import VueToast    from 'vue-toast-notification';

import "../../css/transitions.scss";
import VueHtml2pdf  from "vue-html2pdf";

import { utils, writeFileXLSX } from "xlsx";

Vue.use(VueToast);

Stock(Highcharts);
noData(Highcharts);

Vue.use(HighchartsVue);

new Vue({
	delimiters: ["${", "}"],
	components: {
		myMultiSelect,
		mySelect,
		inputDate,
		LineChart,
		LineChartDouble,
		PieChart,
		BarChart,
		StackedBar,
		VueHtml2pdf
	},
	store,
	data() {
		return {
			showFilters : false,
			data : undefined,
			responseData: {},
			sheets: [
				{
					name: "Personas Formadas",
					key: 'persons',
					tables: [
						{
							name : 'Por País', key: 'segmentedCountries',
							cells: [ { name: 'Pais', key: 'name'}, { name:'Personas Formadas' , key: 'count'} ]
						},
						{ name : 'Por División', key: 'segmentedDivision',
							cells: [ { name: 'División', key: 'name'}, { name:'Personas Formadas' , key: 'count'} ]
						},
						{ name : 'Por Colectivo', key: 'segmentedTotals',
							cells: [ { name: 'Colectivo', key: 'name'}, { name:'Personas Formadas' , key: 'count'} ]
						},
						{ name : 'Por Departamento (Estructura)', key: 'segmentedStructure',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'AMES' , key: 'ames'}, { name:'EMEA' , key: 'emea'}  ]
						},
						{ name : 'Por Departamento (Hotel)', key: 'segmentedHotel',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'AMES' , key: 'ames'}, { name:'EMEA' , key: 'emea'} ]
						},
					]
				},
				{
					name: "Cursos",
					key: 'courses',
					tables: [
						{ name : 'Por País [Cursos]', key: 'segmentedCountries',
							cells: [
								{ name: 'Pais', key: 'started.name'}, { name:'Cursos' , key: 'started.total'}
							]
						},
						{ name : 'Por País [Diplomas]', key: 'segmentedCountries',
							cells: [
								{ name: 'Pais', key: 'finished.name'}, { name:'Diplomas' , key: 'finished.total'}
							]
						},
						{ name : 'Por División', key: 'segmentedDivision',
							cells: [ { name: 'Cursos', key: 'started'}, { name:'Diplomas' , key: 'finished'} ]
						},
						{ name : 'Por Colectivo', key: 'segmentedTotals',
							cells: [ { name: 'Cursos', key: 'started'}, { name:'Diplomas' , key: 'finished'} ] },
						{ name : 'Por Departamento (Estructura - Cursos)', key: 'segmentedStructure',
							cells: [
								{ name: 'Departamento', key: 'started.name'}, { name:'Cursos' , key: 'started.total'}
							] },
						{ name : 'Por Departamento (Estructura - Diplomas)', key: 'segmentedStructure',
							cells: [
								{ name: 'Departamento', key: 'finished.name'}, { name:'Cursos' , key: 'finished.total'}
							] },
						{ name : 'Por Departamento (Hotel - Cursos)', key: 'segmentedHotel',
							cells: [
								{ name: 'Departamento', key: 'started.name'}, { name:'Cursos' , key: 'started.total'}
							] },
						{ name : 'Por Departamento (Hotel - Diplomas)', key: 'segmentedHotel',
							cells: [
								{ name: 'Departamento', key: 'finished.name'}, { name:'Cursos' , key: 'finished.total'}
							]
						},
					]
				},
				{
					name: "Horas",
					key: 'hours',
					tables: [
						{ name : 'Por País', key: 'segmentedCountries',
							cells: [ { name: 'Pais', key: 'name'}, { name:'Horas', key: 'horas'}, {name: 'Promedio', key: 'promedio'}] },
						{ name : 'Por División', key: 'segmentedDivision',
							cells: [ { name: 'División', key: 'name'}, { name:'Horas', key: 'horas'}, {name: 'Promedio', key: 'promedio'}] },
						{ name : 'Por Colectivo', key: 'segmentedTotals',
							cells: [ { name: 'Colectivo', key: 'name'}, { name:'Horas', key: 'horas'}, {name: 'Promedio', key: 'promedio'}] },
						{ name : 'Por Departamento (Estructura)', key: 'segmentedStructure',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'Horas', key: 'horas'}, {name: 'Promedio', key: 'promedio'}] },
						{ name : 'Por Departamento (Hotel)', key: 'segmentedHotel',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'Horas', key: 'horas'}, {name: 'Promedio', key: 'promedio'}] },
					]
				},
				{
					name: "Accesos",
					key: 'access',
					tables: [
						{ name : 'Por País', key: 'segmentedCountries',
							cells: [ { name: 'Pais', key: 'name'}, { name:'Totales', key: 'total'}, {name: 'Unicos', key: 'unicos'}]
						},
						{ name : 'Por División', key: 'segmentedDivision',
							cells: [ { name: 'División', key: 'name'}, { name:'Totales', key: 'total'}, {name: 'Unicos', key: 'unicos'}] },
						{ name : 'Por Colectivo', key: 'segmentedTotals',
							cells: [ { name:'Totales', key: 'total' }, { name: 'Unicos', key: 'unicos' }] },
						{ name : 'Por Departamento (Estructura)', key: 'segmentedStructure',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'Totales', key: 'total'}, {name: 'Unicos', key: 'unicos'}] },
						{ name : 'Por Departamento (Hotel)', key: 'segmentedHotel',
							cells: [ { name: 'Departamento', key: 'name'}, { name:'Totales', key: 'total'}, {name: 'Unicos', key: 'unicos'}] },
					]
				},
			],
			accordion: {
				persons: true,
				courses: false,
				hours: false,
				access: false,
			},
			courses : undefined,
			categories : undefined,
			bd_filters : {},
			divisions : [],
			countries : [],
			centers : [],
			filters : {},
			departaments : [],
			professionalCategories: [],
			genders : [],
			defaultColors : [],
			loadingData: false,
			pdfInProcess: false,
			htmlToPdfOptions: {
				margin: 0,
				filename: `segmentedStats.pdf`,
				image: {
					type: 'jpeg',
					quality: 1
				},
				enableLinks: false,
				html2canvas: {
					scale: 1
				},
				jsPDF: {
					unit: 'cm',
					format: 'a4',
					orientation: 'portrait'
				}
			},
		};
	},

	computed: {
		currentFilters() {
			let filters = [
				{keyList: 'countries', filterKey: 'country'},
				{keyList: 'courses', filterKey: 'course'},
				{keyList: 'centers', filterKey: 'center'},
				{keyList: 'departaments', filterKey: 'departament'},
				{keyList: 'genders', filterKey: 'gender'},
				{keyList: 'divisions', filterKey: 'division'}
			].reduce((acc, cur) => {
				return (this.filters[cur.filterKey]) ? { ...acc, [cur.filterKey]: this.getFilterByID(cur.keyList, cur.filterKey) }: acc;
			}, {});

			if (this.filters['category']?.length) {
				filters['category'] = this.filters['category'].map(ids => this.professionalCategories.find(pc => pc.id === ids)).filter(pc => pc?.id);
			}

			if (this.filters['dateTo'])
				filters['dateTo'] = {name: `Hasta: ${this.filters['dateTo']}`};

			if (this.filters['dateFrom'])
				filters['dateFrom'] = {name: `Desde: ${this.filters['dateFrom']}`};

			return filters;
		},

		currentBdFilters() {
			let currentBdFilters = {};
			(filterCategories || []).forEach(element => {
				if (this.bd_filters['category_' + element.id] && Object.keys(this.bd_filters['category_' + element.id]).length) {
					currentBdFilters[element.name] = [];
					this.bd_filters['category_' + element.id].forEach(filter_id => {
						const filter = this.searchFilterInCategory(element, filter_id);
						currentBdFilters[element.name].push(filter.name);
					});
				}
			});
			return currentBdFilters;
		},

		bdFilterIds () {
			let bdFiltersIds = [];
			filterCategories.forEach(element => {
				if(this.bd_filters['category_' + element.id] && Object.keys(this.bd_filters['category_' + element.id]).length) {
					this.bd_filters['category_' + element.id].forEach(filter_id => bdFiltersIds.push(filter_id));
				}
			});
			return bdFiltersIds;
		},

		showDownloadButton() { return !this.loadingData && this.data; },

		isLoading(){
			return this.loadingData;
		}
	},

	created() {
		this.filters = this.defaultFilters();
		this.bd_filters = this.defaultBdFilters();
		this.defaultColors = ['#7CB5EC', '#434348', '#90ED7D', '#F7A35C', '#8085E9', '#F15C80', '#E4D354', '#2B908F', '#F45B5B'];

		this.divisions = divisions || [];
		this.countries = countries || [];
		this.centers = centers || [];
		this.departaments = departaments || [];
		this.professionalCategories = professionalCategories || [];
		this.genders = genders || [];

		this.loadData();
	},

	methods: {
		loadData() {
			if (!this.loadingData) {
				this.loadingData = true;

				let filters = this.filters;
				filters['filters'] = this.bdFilterIds;

				this.resetData();
				this.loadByTab(['persons', 'courses', 'hours', 'access'], filters);
			}
		},

		loadByTab(keys, filters) {
			Promise.all(this.getDataAndSaveByKey(keys.shift(), filters)).finally(() => {
				if (keys.length) { this.loadByTab(keys, filters) }
				else {
					this.loadingData = false;
				}
			})
		},

		setChartValue(key, response, index) {
			if (index === 0) {
				if (key === 'courses') {
					this.data[key].doubleLineChart.started = this.doubledLineChart(response.started);
					this.data[key].doubleLineChart.finished = this.doubledLineChart(response.finished);
				} else this.data[key].doubleLineChart = this.doubledLineChart(response);
			}
			else if (index === 1) {
				if (key === 'courses') {
					this.data[key].pieChart = this.getSortedData(Object.keys(response[0]).map(key => ({name: key, y: response[0][key] | 0})), 'y')
				} else if (key === 'hours') {
					this.data[key].pieChart.totals = this.getSortedData(response, 'horas', false)
					this.data[key].pieChart.avg = this.getSortedData(response, 'promedio', false)
				} else if (key === 'access') {
					this.data[key].pieChart = this.getSortedData(response, 'totales');
					this.data[key].pieChartUnique = this.getSortedData(response, 'unicos')
				} else {
					this.data[key].pieChart = this.getSortedData(response, 'count');
				}
			}
			else if (index === 2) {
				if (key === 'courses') {
					this.data[key].distribution.started = this.getSortedData(response[0].started)
					this.data[key].distribution.finished = this.getSortedData(response[0].finished)
				} else if (key === 'hours') {
					this.data[key].distribution.total = this.getSortedData(response, 'horas', false);
					this.data[key].distribution.avg = this.getSortedData(response, 'promedio', false);
				} else if (key === 'access') {
					this.data[key].distribution = this.getSortedData(response);
					this.data[key].distributionUnique = this.getSortedData(response, 'unicos');
				} else {
					this.data[key].distribution = this.getSortedData(response, 'count');
				}
			}
			else if (index === 3) {
				let [data1, data2] = response;
				if (key === 'hours') {
					this.data[key].department.totalHotel = (data2.horas || 0);
					this.data[key].department.totalHotelAVG = (data2.promedio || 0);
					this.data[key].department.totalStructure = (data1.horas || 0);
					this.data[key].department.totalStructureAVG = (data1.promedio || 0);
				} else {
					if (!data2) data2 = data1;
					this.data[key].department.totalStructure = (data1.started || data1.total || data1.count || 0);
					this.data[key].department.totalHotel = (data2.finished || data2.unicos || data2.count || 0);
				}
			}
			else if (index === 4) {
				if (key === 'persons') this.data[key].department.structure = this.getStackedBars(response);
				else if (key === 'courses') {
					this.data[key].structure.started = this.getSortedData(response[0].started);
					this.data[key].structure.finished = this.getSortedData(response[0].finished);
				} else if (key === 'hours') {
					this.data[key].structure.data = this.getSortedData(response, 'horas', false);
					this.data[key].structure.avg = this.getSortedData(response, 'promedio', false);
				} else if (key === 'access') {
					this.data[key].structure.data = this.getSortedData(response);
					this.data[key].structure.uniques = this.getSortedData(response, 'unicos');
				} else {
					this.data[key].department.data = this.getSortedData(response);
				}
			}
			else if (index === 5) {
				if (key === 'persons') this.data[key].department.hotel = this.getStackedBars(response);
				else if (key === 'courses') {
					this.data[key].hotel.started = this.getSortedData(response[0].started);
					this.data[key].hotel.finished = this.getSortedData(response[0].finished);
				} else if (key === 'access') {
					this.data[key].hotel.data = this.getSortedData(response);
					this.data[key].hotel.uniques = this.getSortedData(response, 'unicos');
				} else if (key === 'hours') {
					this.data[key].hotel.data = this.getSortedData(response, 'horas', false);
					this.data[key].hotel.avg = this.getSortedData(response, 'promedio', false);
				}
			}
			else if (index === 6) {
				this.data[key].school = this.getSortedData(response, 'horas', false);
				this.data[key].schoolAVG = this.getSortedData(response, 'promedio', false);
			}
		},

		getDataAndSaveByKey(key, filters) {
			const arr = [
				this.$store.dispatch(`generalStatsModule/fetchSegmented_${key}`, {filters})
					.then((response) => { this.setChartValue(key, response, 0); })
					.catch(() => { this.setChartValue(key, key === 'courses' ? {started : {}, finished: {}} : {},0); }),
				// ------------------------------------------------------------------------------------------------------------
				this.$store.dispatch(`generalStatsModule/fetchSegmentedDivision_${key}`, {filters}).then((response) => {
					this.setChartValue(key, response, 1);
					this.responseData[key] = {  ...(this.responseData[key] || {}), segmentedDivision: (this.responseData[key]?.segmentedDivision || response)};
				}).catch(() => { this.setChartValue(key, key === 'courses' ? [{}] : [],1); }),
				// ------------------------------------------------------------------------------------------------------------
				this.$store.dispatch(`generalStatsModule/fetchSegmentedCountries_${key}`, {filters}).then((response) => {
					this.setChartValue(key, response, 2);
					this.responseData[key] = { ...(this.responseData[key] || {}), segmentedCountries: (this.responseData[key]?.segmentedCountries || response)};
				}).catch(() => { this.setChartValue(key, key === 'courses' ? [{started:[], finished: []}] : [], 2); }),
				// ------------------------------------------------------------------------------------------------------------
				this.$store.dispatch(`generalStatsModule/fetchSegmentedTotals_${key}`, {filters}).then((response) => {
					this.setChartValue(key, response, 3);
					this.responseData[key] = { ...(this.responseData[key] || {}), segmentedTotals: (this.responseData[key]?.segmentedTotals || response)};
				}).catch(() => {
					this.setChartValue(key, [{horas: 0, promedio: 0, count: 0}, {horas: 0, promedio: 0, count: 0}], 3);
				}),
				// ------------------------------------------------------------------------------------------------------------
				this.$store.dispatch(`generalStatsModule/fetchSegmentedStructure_${key}`, {filters}).then((response) => {
					this.setChartValue(key, response, 4);
					this.responseData[key] = { ...(this.responseData[key] || {}), segmentedStructure: (this.responseData[key]?.segmentedStructure || response)};
				}).catch(() => {
					let response = [];
					if (key === 'persons') response = [{}];
					else if (key === 'courses') response = [{started:[], finished: []}];
					this.setChartValue(key, response, 4);
				}),
				// ------------------------------------------------------------------------------------------------------------
				this.$store.dispatch(`generalStatsModule/fetchSegmentedHotel_${key}`, {filters}).then((response) => {
					this.setChartValue(key, response, 5);
					this.responseData[key] = { ...(this.responseData[key] || {}), segmentedHotel: (this.responseData[key]?.segmentedHotel || response)};
				}).catch(() => {
					let response = [];
					if (key === 'persons') response = [{}];
					else if (key === 'courses') response = [{started:[], finished: []}];
					this.setChartValue(key, response, 5);
				}),
			];
			// ------------------------------------------------------------------------------------------------------------
			if (key === 'hours') {
				arr.push(this.$store.dispatch(`generalStatsModule/fetchSchool_hours`, {filters}).then((response) => {
					this.setChartValue(key, response, 6);
					this.responseData[key] = { ...(this.responseData[key] || {}), schoolHours: (this.responseData[key]?.schoolHours || response)};
				}).catch(() => { this.setChartValue(key, [], 6); }))
			}
			return arr;
		},

		getSortedData(arr, key = 'total', int = true) {
			return (
				int ?
					arr.map(data => ({name: data.name, y: (+(data[key] || data.total || 0)) | 0})) :
					arr.map(data => ({name: data.name, y: this.formatNumber(+(data[key] || data.total || 0))}))
			).sort((a, b) => b.y - a.y)
		},

		formatNumber(num, digits = 2) {
			return +(Math.round(num * 100) / 100).toFixed(digits)
		},

		getStackedBars(response) {
			const keys = Object.keys(response[0]);
			const dataKeys = keys.filter(key => key !== 'name')
			const newSeries = {
				categories: [],
				series: dataKeys.reduce((acc, cur) => ([...acc, { name: cur, data: [] }]), [])
			};
			response.sort( (a, b) => ((b.ames | 0) + (b.emea | 0)) - ((a.ames | 0) + (a.emea | 0)) ).forEach(data => {
				newSeries.categories = [...newSeries.categories, data.name];
				newSeries.series.forEach(serie => {
					serie.data = [...serie.data, +data[serie.name]]
				});
			})
			return newSeries;
		},

		doubledLineChart(response) {
			let categories = [];
			const series = [
				{
					name: "Nuevos",
					color: '#009688',
					data: [],
					dataLabels: {
						enabled: true,
						color: '#009688',
						style: {
							textOutline: 'none'
						}
					},
				},
				{
					name: "Acumulados",
					data: [],
					color: '#00695C',
					dataLabels: {
						enabled: true,
						color: '#00695C',
						style: {
							textOutline: 'none'
						}
					},
				}];

			let prev = 0;

			Object.keys(response).forEach((key) => {
				categories = [...categories, key];
				const period = this.formatNumber(response[key].period);
				prev += period;
				series[0].data = [...series[0].data, period];
				series[1].data = [...series[1].data, prev];
			}, []);

			return  { categories, series }
		},

		toggleFilterDisplay() { this.showFilters = !this.showFilters; },

		applyFilters() { this.loadData(); },

		clearFilters() {
			this.filters = this.defaultFilters();
			this.bd_filters = this.defaultBdFilters();
			this.loadData();
		},

		removeFilter(filter) { this.filters[filter] = ''; },

		defaultBdFilters() {
			let bd_filters = {};
			(filterCategories || []).forEach(element => bd_filters['category_' + element.id] = []);
			return bd_filters;
		},

		removeBdFilter(categoryName) {
			filterCategories.forEach(element => {
				if(element.name === categoryName) this.bd_filters['category_' + element.id] = [];
			});
		},

		defaultFilters() {
			return {
        country    : '',
        center     : '',
        category   : [],
        department: '',
        gender     : '',
        division   : '',
        dateFrom   : '',
        dateTo     : ''
      };
		},

		searchFilterInCategory(category, filter_id) {
			return category.filters.find(filter => `${filter.id}` === `${filter_id}`);
		},

		getFilterByID(keyList, filterKey) {
			return this[keyList].find(item => item.id === this.filters[filterKey])
		},

		getDataChartLinear(data, color1, color2) {
			const keys = Object.keys(data);

			const seriesNew = keys.map((keyData) => {
				return parseInt(data[keyData].period);
			});

			const seriesAccumulated = keys.map((keyData) => {
				return parseInt(data[keyData].total);
			});

      const categories = keys.map((keyData) => {
        const month = keyData.substring(0, 2);
        const year = keyData.substring(3);
        return month + "-" + year;
      });

			return {
				categories: categories,
				series    : [
					{
						name      : seriesNames.news,
						color     : color1,
						data      : seriesNew,
						dataLabels: {
							enabled: true,
							color  : color1,
							style  : {
								textOutline: 'none'
							}
						},
					},
					{
						name      : seriesNames.accumulated,
						color     : color2,
						data      : seriesAccumulated,
						dataLabels: {
							enabled: true,
							color  : color2,
							style  : {
								textOutline: 'none'
							}
						},
					},
				],
			};
		},

		toggleAccordion(key) {
			this.accordion[key] =! this.accordion[key];
		},

		downloadFile () {
			// Nota del programador: esto es solo un ejemplo, luego lo adaptamos a como sería el final porque el
			// maquetado del PDF va a ser buen rollo, gracias por su comprensión...
			if (!this.pdfInProcess) {
				this.pdfInProcess = true;
				this.$toast.info('Generando PDF, por favor espere...');
				setTimeout(() => {
					this.$refs.html2Pdf.generatePdf();
					this.pdfInProcess = false;
				}, 1000)
			}
		},

		resetData() {
			this.data = Object.assign({}, {
				persons: {
					doubleLineChart : {},
					pieChart: [],
					distribution: [],
					department: {
						structure: {},
						hotel: {},
						totalHotel: 0,
						totalStructure: 0,
					},
				},
				courses: {
					doubleLineChart: {
						started: {},
						finished: {}
					},
					pieChart: [],
					distribution: {
						started: [],
						finished: []
					},
					department: {
						totalHotel: 0,
						totalStructure: 0,
					},
					structure: {
						started: [],
						finished: [],
					},
					hotel: {
						started: [],
						finished: [],
					},
				},
				hours: {
					doubleLineChart : {},
					pieChart: { total: [], avg: [] },
					distribution: {
						total: [],
						avg: []
					},
					school: [],
					schoolAVG: [],
					unicos: [],
					department: {
						totalHotel: 0,
						totalStructure: 0,
						totalHotelAVG: 0,
						totalStructureAVG: 0,
					},
					structure: {
						data: [],
						avg: [],
					},
					hotel: {
						data: [],
						avg: [],
					},

				},
				access: {
					doubleLineChart : {},
					pieChart: [],
					pieChartUnique: [],
					distribution: [],
					distributionUnique: [],
					department: {
						totalHotel: 0,
						totalStructure: 0,
					},
					structure: {
						data: [],
						uniques: [],
					},
					hotel: {
						data: [],
						uniques: [],
					},
				}
			});
			this.responseData = {};
		},

		generateExcelData() {
			const workbook = utils.book_new();
			let column = 0, row = 2;
			this.sheets.forEach(sheet => {
				column = 0;
				const data = [[]];
				const resData = this.responseData[sheet.key];

				if (resData) {
					sheet.tables.forEach(table => {
						const originalData = this.responseData[sheet.key];
						data[0][column] = table.name;

						table.cells.forEach(cell => {
							data[1] = [...(data[1] || [])];
							data[1][column] = cell.name;

							row = 2;
							data[row] = [...(data[row] || [])];
							if (originalData[table.key]) {
								if (cell.key.includes('.')) {
									const keySplited = cell.key.split('.');
									originalData[table.key][0][keySplited[0]].forEach(content => {
										data[row][column] = content[keySplited[1]] || '';
										row += 1;
										data[row] = [...(data[row] || [])];
									})
								} else {
									originalData[table.key].forEach(content => {
										data[row][column] = content[cell.key] || '';
										row += 1;
										data[row] = [...(data[row] || [])];
									})
								}
							}
							column += 1;
						});
						column += 1;
					});
				}

				utils.book_append_sheet(workbook, utils.aoa_to_sheet(data, {}), sheet.name);
			})

			writeFileXLSX(workbook, 'segmentedStats.xlsx');
		},
	},

	filters: {
		formatNumber: (number) => {
			return (!number) ? 0 : number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
		},
	},
}).$mount("#segmented-stats");
