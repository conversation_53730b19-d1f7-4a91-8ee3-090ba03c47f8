import Vue from 'vue';
import store from './store';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';
import axios from "axios";

Vue.use(VueToast);

new Vue({
    components: {},
    store,
    methods: {
        saveLtiChapter() {
            const form = document.getElementById('form-save-lti-chapter');
            const formData = new FormData(form);
            console.log('save lti');
            axios.post('/admin/api/v1/lti-chapter', formData).then(r => {
                console.log(r);
                if (r.status === 200) {
                    window.location.href = r.data;
                } else {
                    // failed
                    this.$toast.error(r.data);
                }
            }).catch(e => {
                const { status, data } = e.response;
                this.$toast.error(data);
            })
        }
    }
}).$mount('#save-lti-chapter');
