<template>
  <div class="setting-multiselect">
    <multiselect
      v-model="selectedValues"
      :options="formattedOptions"
      :multiple="true"
      :taggable="true"
      track-by="value"
      label="label"
      placeholder="Select options"
      class="form-multiselect"
    />
    <div class="button-group mt-2">
      <button type="button" @click="selectAll" class="btn btn-primary btn-sm">{{ $t("ADD_ALL") }}</button>
      <button type="button" @click="clearAll" class="btn btn-danger btn-sm">{{ $t("REMOVE_ALL") }}</button>
    </div>
    <p class="description" v-if="setting.description">{{ setting.description }}</p>
  </div>
</template>

<script>
import Multiselect from 'vue-multiselect';

export default {
  name: 'SettingMultiSelect',
  components: {
    Multiselect,
  },
  props: {
    setting: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      selectedValues: this.getSelectedOptions(),
    };
  },
  computed: {
    formattedOptions() {
      return this.setting.options.map(option => ({
        label: option,
        value: option,
      }));
    },
  },
  watch: {
    selectedValues(newValue) {
      const valuesOnly = newValue.map(option => option.value);
      this.setting.value = JSON.stringify(valuesOnly);
    },
  },
  methods: {
    getSelectedOptions() {
      const storedValues = this.setting.value ? JSON.parse(this.setting.value) : [];
      return storedValues.map(value => ({
        label: value,
        value,
      }));
    },
    selectAll() {
      this.selectedValues = [...this.formattedOptions];
    },
    clearAll() {
      this.selectedValues = [];
    },
  },
};
</script>

<style scoped>
@import 'vue-multiselect/dist/vue-multiselect.min.css';

.setting-multiselect {
  margin-bottom: 15px;
}

.button-group {
  margin-bottom: 10px;
}

.btn {
  margin-right: 5px;
}

.form-multiselect {
  width: 100%;
}

.description {
  font-size: 0.9em;
  color: #666;
}
</style>
