<template>
  <div class="setting-single-select">
    <multiselect
      v-model="selectedValue"
      :options="formattedOptions"
      :multiple="false"
      track-by="value"
      label="label"
      placeholder="Select an option"
      class="form-multiselect"
    />
    <p class="description" v-if="setting.description">{{ setting.description }}</p>
  </div>
</template>

<script>
import Multiselect from 'vue-multiselect';

export default {
  components: {
    Multiselect,
  },
  props: {
    setting: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      selectedValue: this.setting.value || '',
    };
  },
  computed: {
    formattedOptions() {
      return this.setting.options.map(option => {
        return typeof option === 'string'
          ? { label: option, value: option }
          : option;
      });
    },
  },
  watch: {
    selectedValue(newValue) {
      this.setting.value = newValue;
    },
  },
};
</script>

<style scoped>
@import 'vue-multiselect/dist/vue-multiselect.min.css';

.setting-single-select {
  margin-bottom: 15px;
}
.form-multiselect {
  width: 100%;
}
.description {
  font-size: 0.9em;
  color: #666;
}
</style>
