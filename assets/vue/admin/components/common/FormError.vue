<template>
  <div class="FormError">
    <p v-html="message" />
  </div>
</template>

<script>
export default {
  props: {
    message: {
      type: String,
      default: ''
    },
  },
};
</script>

 <style scoped lang="scss"> 
.FormError{
  font-size: var(--font-size-s);
  text-align: justify;
  padding: $spacing-xs 0;
  color: adjust-color($color-error, $lightness: -20%);
  border-bottom: 1px solid;
  animation: move .5s ;
  position: relative;
  transition: all .5s ease;
  left: 0;
  opacity: 1;

  p{
    margin: 0;
  }

  @keyframes move {
    0%{
      left: calc(var(--spacing-xl) * -1);
      opacity: 0;
    }
  }
}
</style>
