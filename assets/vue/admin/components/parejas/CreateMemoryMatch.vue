<template>
  <div class="new-title">
    <form>
      <div class="form-title-time">
        <div class="title">
          <BaseTextTarea
            :label="translationsVue.rouletteWord_configureFields_statement"
            :max="48"
            :value.sync="pareja.text"
            :placeholder="
              translationsVue.pairs_configureFields_placeholder_title
            "
            :required="false"
            :rows="9"
          ></BaseTextTarea>
        </div>
      </div>

      <div class="form-image">       
        <BaseInputFileImage
          :title="translationsVue.games_text_common_ilustre_question"
          size="s"
          :delete-image="deleteImage"
          :url-image="currentImage"
          @file-selected="fileSelected($event)"
        />
      </div>
    </form>

    <div class="mt-5 text-center">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button class="btn-sm btn btn-primary" @click="saveQuestion">
        {{ translationsVue.Save }}
      </button>
    </div>
  </div>
</template>
  
  <script>
import { get } from "vuex-pathify";
import BaseInputFileImage from "../base/BaseInputFileImagen";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";
import { formatDateMixin } from "../../../mixins/formatDateMixin";

export default {
  components: {
    BaseInputFileImage,
  },

  props: {
    pareja: {
      type: Object,
      default: () => ({}),
    },
  },

  mixins: [modalMixin, alertToastMixin, formatDateMixin],

  data() {
    return {
      image: null,
      preview: null,
      typeChapter,
      chapterId,
      time: "00:00:30",
      translationsVue,
      title: "",
      deleteImage: false,
    };
  },
  computed: {
    ...get("parejasModule", ["getRouteChapter"]),

    routeChapter() {
      return this.getRouteChapter();
    },

    currentImage() {
      return this.getImagePairs ?? this.preview;
    },

    getImagePairs() {
      return this.pareja?.image
        ? "uploads/games/parejas/" + this.pareja.image
        : null;
    },
  },

  created() {
    this.time = this.convertSecondToHoursMinutesAndSeconds(this.pareja.time);
  },

  methods: {
    fileSelected(file) {
      this.image = file;
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async saveQuestion() {
      if (this.pareja.text == "" && this.image == null) {
        this.$toast.open({
          message: this.translationsVue.games_validate_memory_match,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      } else {
        const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

        const formData = new FormData();
        formData.append("id", this.pareja.id);
        formData.append("title", this.pareja.text);
        formData.append("time", secondsTime);
        formData.append("idChapter", this.chapterId);
        formData.append("image", this.image);

        if (this.pareja?.id) {
          await this.$store.dispatch("parejasModule/editParejas", formData);
        } else {
          await this.$store.dispatch("parejasModule/createParejas", formData);
        }

        this.$emit("clears-inputs");

        // this.closeModal(`modal-edit-question${this.pareja.id}`);
        this.$refs["closeChapterContentModal"].click();
        this.alertSuccesSave();
        await this.fetchParejas();
        this.image = null;
        this.preview = null;
        this.deleteImage = !this.deleteImage;      
      }
    },

    async fetchParejas() {
      await this.$store.dispatch("parejasModule/fetchParejas", this.chapterId);
    },
  },
};
</script>
  
   <style scoped lang="scss"> 
.new-title {
  .preview-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  input[type="file"] {
    display: none;
  }

  form {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;

    .form-title-time {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
  }
}
</style>
  