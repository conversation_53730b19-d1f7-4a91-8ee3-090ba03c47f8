<template>
  <div class="GameBlock">
    <form ref="form" @submit.prevent="checkIfComplete">
      <div class="options">
        <div class="form-head">
          <div class="form-inputs">
            <div>
              <BaseTextTarea
                :label="translationsVue.quiz_configureFields_question"
                :max="255"
                :value.sync="text"
                :placeholder="
                  translationsVue.quiz_configureFields_question_placeholder
                "
                :required="true"
                :rows="5"
                :submitted="submitted"
              ></BaseTextTarea>
            </div>

            <div>
              <label for="title" class="form-label"
                >{{ translationsVue.games_text_common_time }}
              </label>

              <BaseInputTime
                v-model="time"
                :options="['minutes', 'seconds']"
                :maxMinutes="31"
                :time="time"
                @time-update="timeUpdate"
              />
            </div>
          </div>
         

          <div class="form-image">
            <BaseInputFileImage
              :urlImage="currentImage"
              :title="translationsVue.games_text_common_ilustre_question"
              :delete-image="deleteImage"
              size="s"
              @file-selected="fileSelected($event)"
            />
          </div>
        </div>

        <div class="form-footer">
          <div>
            <label for="title" class="form-label"
              ><label> {{ translationsVue.games_opciones }}</label>
            </label>
            <div class="form-check">
              <input
                class="form-check-input"
                type="radio"
                name="flexRadioDefault"
                v-model="selected"
                id="flexRadioDefault1"
                value="true"
              />
              <label class="form-check-label" for="flexRadioDefault1">
                {{ translationsVue.games_true }}
              </label>
            </div>
            <div class="form-check">
              <input
                class="form-check-input"
                type="radio"
                name="flexRadioDefault"
                id="flexRadioDefault2"
                v-model="selected"
                value="false"
              />
              <label class="form-check-label" for="flexRadioDefault2">
                {{ translationsVue.games_false }}
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="text-center mt-4">
        <button
          v-show="0"
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
          ref="closeChapterContentModal"
        ></button>

        <button
          type="submit"
          data-dismiss="modal"
          class="btn btn-primary btn-sm"
          @click="submitted = true"
        >
          {{ translationsVue.Save }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import BaseInputFileImage from "../base/BaseInputFileImagen";
import { formatDateMixin } from "../../../mixins/formatDateMixin";
import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";

export default {
  components: {
    BaseInputFileImage,
  },

  watch: {
    currentLine: function (val) {
      if (this.currentLine?.id) this.updateLetter();
      else this.clearCurrent();
    },
  },

  props: {
    chapterId: {
      type: Number,
      default: 0,
    },

    currentLine: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      options: [
        { text: "Falso", value: false },
        { text: "Verdadero", value: true },
      ],
      text: "",
      time: "00:00:30",
      route: undefined,
      selected: false,
      editionActive: false,
      selectFile: false,
      imageRoute: "",
      mensaje: "",
      mostrarImagen: false,
      mensajeActivo: false,
      preview: null,
      translationsVue,
      chapterType,
      deleteImage: false,
      submitted: false,
    };
  },

  mixins: [formatDateMixin, modalMixin, alertToastMixin],

  computed: {
    currentImage() {
      return this.imageRoute
        ? "/uploads/games/trueOrFalse/" + this.imageRoute
        : this.preview;
    },
  },

  methods: {
    fileSelected(file) {
      this.selectFile = true;
      this.route = file;
    },

    async checkIfComplete() {
      try {
        //  if (this.messagesValidations()) return;
        if (this.$refs.form.checkValidity() === false) return;

        await this.saveQuestion();
        this.deleteImage = true;
        /* this.closeModal("editQuestion");
        this.closeModal("modal-chapter-12"); */
        this.$refs["closeChapterContentModal"].click();

        this.alertSuccessSave();

        this.$emit("reload-blocks");
      } catch (error) {
        this.$emit("reload-blocks");
        //this.alertErrorSave();
      }
    },

    messagesValidations() {
      if (this.text === "" && !this.selectFile) {
        this.alertWarning(
          this.translationsVue.games_validation_truefalse_question_or_image
        );

        return true;
      } else if (this.time === "" || this.time < 1) {
        this.mensaje = "Debe digitar el tiempo para la pregunta";
        this.mensajeActivo = true;
        this.alertWarning(this.mensaje);

        return true;
      }

      return false;
    },

    async saveQuestion() {
      const time = this.convertDateHoursMinutesAndSeconds(this.time);

      const data = {
        id: this.currentLine.id,
        chapterId: this.chapterId,
        text: this.text,
        time: time,
        select: this.selected,
        route: this.route,
      };

      if (this.editionActive) {
        await this.$store.dispatch("trueOrFalseModule/editBlock", data);
      } else {
        await this.$store.dispatch("trueOrFalseModule/setBlock", data);
      }

     await this.fetchQuestions();
      this.clearCurrent();
      this.submitted = false;
      this.deleteImage = false;
     
    },

    updateLetter() {
      this.editionActive = true;
      this.text = this.currentLine.text;
      this.time = this.convertSecondToHoursMinutesAndSeconds(
        this.currentLine?.time ?? "30"
      );
      this.route = this.currentLine.route;
      this.selected = this.currentLine.result;
      this.imageRoute = this.currentLine.route;

      if (!this.time) this.time = "00:00:30";
    },

    clearCurrent() {
      this.text = "";
      this.route = undefined;
      this.selected = false;
      this.editionActive = false;
      this.time = "00:00:30";
      this.imageRoute = "";
      this.mensajeActivo = false;
      this.preview = null;
      
    },

    async fetchQuestions() {
      const data = {
        chapterId: this.chapterId,
      };

      await this.$store.dispatch("trueOrFalseModule/reloadBlock", data);
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.GameBlock {
  form {
    flex: 2;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 1rem;

    .options {
      .form-head {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        .form-inputs {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-image {
          height: 20rem;
        }
      }

      .form-footer {
        margin-top: 2.5rem;
      }
    }
  }

  .form-check {
    label {
      margin-left: 1.5rem;
    }
  }
}
</style>
