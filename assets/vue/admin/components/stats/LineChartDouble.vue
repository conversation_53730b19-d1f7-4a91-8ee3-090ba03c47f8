<template>
  <div>
    <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
  </div>
</template>

<script>
export default {
  name: "LineChartDouble",

  props: {
    title: {
      type: String,
      default: "",
    },
    seriesData: {
      type: Object,
      default: {},
    },
    tooltipTitle: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      chartOptions : {
        chart: {
          type: "line",
        },
        title: {
          text: this.title,
        },
        rangeSelector: {
          enabled: true,
          inputEnabled: false,
        },
        credits: {
          enabled: false,
        },
        xAxis: {
          categories: this.seriesData?.categories,
        },
        yAxis: {
          title: false,
        },
        plotOptions: {
          line: {
            color: {
              linearGradient: { x1: 0, x2: 0, y1: 0, y2: 1 },
              stops: [
                [0, "var(--color-primary-dark)"],
                [1, "var(--color-primary)"],
              ],
            },
            dataLabels: {
              enabled: true,
              style: {
                color: "var(--color-primary)",
              },
            },
            enableMouseTracking: false,
          },
        },
        series: this.seriesData?.series,
        lang: {
          noData: this.$t('NO_INFORMATION'),
        },
        noData: {
          style: {
            fontWeight: "bold",
            fontSize: "15px",
            color: "#303030",
          },
        },
      }
    }
  },
  watch: {
    seriesData: function (newVal) {
      this.chartOptions.xAxis.categories = newVal.categories;
      this.chartOptions.series = newVal.series;
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
