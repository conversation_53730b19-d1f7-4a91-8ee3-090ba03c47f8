<template>
  <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
</template>

<script>

export default {
  name      : "StackedBar",
  components: {},
  props     : {
    title     : {
      type   : String,
      default: "",
    },
    seriesData: {
      type   : Object,
      default: () => ({}),
    },
    tooltip   : {
      type   : String,
      default: "",
    },
    legend    : {
      type   : String,
      default: "{name}",
    },
    colors    : {
      type   : Array,
      default: () => ['#80CBC4', '#AED581'],
    },
    innerSize : false,
  },
  data() {
    return {
      chartOptions: {
        chart      : {
          plotBackgroundColor: null,
          plotBorderWidth    : null,
          plotShadow         : false,
          type               : 'bar',
        },
        credits    : {
          enabled: false,
        },
        title      : {
          text: this.title || '',
        },
        colors     : this.colors,
        xAxis      : {
          categories: this.seriesData?.categories || [],
          crosshair : true
        },
        yAxis : {
          title: ''
        },
        plotOptions: {
          series: {
            stacking: 'normal'
          }
        },
        series     : this.seriesData?.series || [],
        lang       : {
          noData: this.$t('NO_INFORMATION'),
        },
        noData     : {
          style: {
            fontWeight: 'bold',
            fontSize  : '15px',
            color     : '#303030'
          }
        }
      }
    };
  },
  watch: {
    seriesData: function (newVal) {
      this.chartOptions.xAxis.categories = newVal.categories;
      this.chartOptions.series = newVal.series;
    },
  },
};
</script>
<style></style>
