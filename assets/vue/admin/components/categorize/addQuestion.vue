<template>
  <div class="add-question">
    <!--    <p><b>{{ translationsVue.games_categorize }} </b></p>-->
    <div class="categorize-form">
      <div class="form-question-input">
        <div class="mb-3">
          <BaseTextTarea
            :label="translationsVue.quiz_configureFields_question"
            :max="39"
            :value.sync="question"
            :placeholder="
              translationsVue.quiz_configureFields_question_placeholder
            "
            :required="false"
            :rows="3"
          ></BaseTextTarea>
        </div>

        <div>
          <label for="title" class="form-label"
            >{{ translationsVue.games_text_common_time }}
          </label>

          <BaseInputTime
            v-model="time"
            :options="['minutes', 'seconds']"
            :maxMinutes="31"
            :time="time"
            @time-update="timeUpdate"
          />
        </div>
      </div>

      <div class="image-question">
        <label>{{ translationsVue.games_text_common_ilustre_question }}</label>
        <div
          :style="{ backgroundImage: 'url(' + preview + ')' }"
          :class="
            image == null
              ? 'preview-image preview-image-default'
              : 'preview-image'
          "
          @click="$refs.inputFile.click()"
        ></div>

        <div class="mb-3 mt-2">
          <input
            type="file"
            @change="loadImage($event)"
            accept="image/*"
            ref="inputFile"
          />

          <a class="btn-sm btn btn-primary" @click="$refs.inputFile.click()">
            <i class="fas fa-upload"></i>
            {{ translationsVue.games_text_common_select_image }}
          </a>

          <a class="btn-sm btn btn-danger" @click="removeImage()">
            <i class="fas fa-trash-alt"></i>
          </a>
        </div>
      </div>
    </div>

    <div class="optiones mt-3">
      <label>
        {{ translationsVue.categorize_configureFields_title_group }}</label
      >
      <div
        class="row mt-2 pr-4"
        v-for="(category, index) in categories"
        :key="category.id"
      >
        <div class="col-md-12">
          <div class="form-check row text-break">
            <input
              class="form-check-input"
              type="checkbox"
              :id="`inlineCheckbox${category.id}`"
              value="option1"
              v-model="category.selected"
              @input="checkSelected(index)"
            />
            <label
              class="form-check-label"
              :for="`inlineCheckbox${category.id}`"
              >{{ category.name }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-5" v-if="!processSave">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button class="btn btn-primary btn-sm" @click="addQuestion()">
        {{ translationsVue.Save }}
      </button>
    </div>
    <div v-else class="text-center">
      <Spinner />
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Spinner from "../base/Spinner";

import { modalMixin } from "../../../mixins/modalMixin";
import { alertToastMixin } from "../../../mixins/alertToastMixin";

export default {
  components: {
    Spinner,
  },

  props: {
    categories: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      question: "",
      selectedCategory: "",
      image: null,
      preview: "/assets/common/add_image_file.svg",
      translationsVue,
      chapterId,
      time: "00:00:30",
      processSave: false,
      typeChapter,
    };
  },

  mixins: [modalMixin, alertToastMixin],

  computed: {
    ...get("questionsGamesModule", ["isLoading", "getRouteChapter"]),

    routeChapter() {
      return this.getRouteChapter();
    },
  },

  methods: {
    checkSelected(index) {
      this.categories.forEach((ans, i) => {
        if (i != index) {
          ans.selected = false;
        }
      });
    },

    deletedCategory(index) {
      if (this.categories.length > 2) {
        this.categories.splice(index, 1);
      }
    },

    loadImage(event) {
      const input = event.target;

      if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        this.image = input.files[0];

        reader.readAsDataURL(input.files[0]);
      }
    },

    async addQuestion() {
      const verify = this.categories.filter((category) => category.selected);

      if (
        verify.length === 1 &&
        (this.question !== "" || this.image !== null)
      ) {
        await this.sendQuestionFormData();

        this.$refs["closeChapterContentModal"].click();
        // this.closeModal(`modal-chapter-new`);

        await this.fetchQuestions();

        this.alertSuccesSave();
      } else {
        this.$toast.open({
          message: this.translationsVue.games_validate_add_categorize,
          type: "info",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    async sendQuestionFormData() {
      const time = this.time;
      const seconds = time.split(":");
      const secondsTime =
        +seconds[0] * 60 * 60 + +seconds[1] * 60 + +seconds[2];

      const formData = new FormData();
      formData.append("question", this.question);
      formData.append("image", this.image);
      formData.append("answers", JSON.stringify(this.categories));
      formData.append("idChapter", this.chapterId);
      formData.append("time", secondsTime);

      this.processSave = true;
      await this.$store.dispatch(
        "questionsGamesModule/newCategories",
        formData
      );
      this.processSave = false;
      this.clearFormData();
    },

    async fetchQuestions() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchCategories",
        this.chapterId
      );
    },

    clearFormData() {
      this.question = "";
      this.selectedCategory = "";
      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";
      this.categories.forEach((ans, i) => {
        ans.selected = false;
      });
    },

    clearCheckCorrect() {
      console.log("clearCheckCorrect", this.categories);
      this.categories.forEach((ans, i) => {
        console.log("ans", ans);
        ans.correct = false;
      });
    },

    removeImage() {
      this.image = null;
      this.preview = "/assets/common/add_image_file.svg";
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.add-question {
  .categorize-form {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    .form-question-input {
      flex: 1;
    }
  }
  .preview-image {
    width: 250px;
    height: 181px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  .preview-image-default {
    background-size: 30%;
  }

  input[type="file"] {
    display: none;
  }

  .form-check-input:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }
}
</style>
