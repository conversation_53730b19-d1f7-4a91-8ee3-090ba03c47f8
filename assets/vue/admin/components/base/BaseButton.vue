<template>
  <button
    :type="type"
    class="BaseButton"
    :class="['theme--dark', `variation--${variation}`, `size--${size}`]"
    v-on="$listeners"
  >
    <slot />
  </button>
</template>

<script>
export default {
  name: 'BaseButton',
  props: {
    variation: {
      type: String,
      default: 'primary',
      validator: (variation) => ['primary', 'secondary', 'tertiary'].includes(variation),
    },

    size: {
      type: String,
      default: 'm',
      validator: (size) => ['xs', 's', 'm', 'l'].includes(size),
    },

    type: {
      type: String,
      default: 'button',
    },
  },
};
</script>

<style scoped lang="scss">
.BaseButton {
  border: var(--button-border);
  color: hsl(210, 14%, 97%);
  background: hsl(172, 38%, 57%);
  padding: var(--button-padding);
  transition: all .4s ease;
  font-size: var(--button-font-size);
  display: flex;
  align-items: center;

  ::before {
    transition: all .4s ease;
  }

  &:hover {
    cursor: pointer;
    color:  hsl(172, 38%, 57%);
    background-color:  hsl(210, 14%, 97%);
    border:  hsl(172, 38%, 57%);
    outline: none;

    ::before{
      color: hsl(172, 38%, 57%) !important;
    }
  }

  &:focus{
    outline: none;
  }
}

.theme--light.variation{
  &--primary{
    --button-color: hsl(210, 14%, 97%);
    --button-background-color: hsl(172, 38%, 57%);
    --button-color-hover: hsl(172, 38%, 57%);
    --button-background-color-hover: hsl(210, 14%, 97%);
    --button-border: 2px solid transparent;
    --button-border-hover: 2px solid transparent;
  }

  &--secondary{
    --button-color: hsl(172, 38%, 57%);
    --button-background-color: hsl(210, 14%, 97%);
    --button-color-hover: hsl(210, 14%, 97%);
    --button-background-color-hover: hsl(172, 38%, 57%);
    --button-border: 2px solid transparent;
    --button-border-hover: 2px solid transparent;
  }

}

.theme--dark.variation {
  &--primary {
    --button-color: hsl(210, 14%, 97%);
    --button-background-color: hsl(172, 38%, 57%);
    --button-color-hover: hsl(172, 38%, 57%);
    --button-background-color-hover: hsl(210, 14%, 97%);
    --button-border: 2px solid transparent;
    --button-border-hover: 2px solid transparent;
  }

  &--secondary {
    color: hsl(172, 38%, 57%);
    background-color: hsl(210, 14%, 97%);

    &:hover {
      cursor: pointer;
      color: hsl(210, 14%, 97%) !important;
      background-color: hsl(172, 38%, 57%) !important;
      border: hsl(210, 14%, 97%) !important;
      outline: none;

      ::before {
        color:  hsl(210, 14%, 97%) !important;
      }

      border: 2px solid transparent;
      border-hover: 2px solid transparent;
    }
  }
}

.size{
  &--xs{
    --button-padding: 0.25rem 0.75rem;
    --button-font-size: 0.875rem;
    span[class*=icon-]{
      font-size: 0.875rem;
      padding-left: 0;
    }
  }
  &--s{
    --button-padding: 0.5rem 1.5rem;
    --button-font-size:0.875rem;
    span[class*=icon-]{
      font-size: 0.875rem;
      padding-left: 0.75rem;
    }
  }

  &--m{
    --button-padding: 0.75rem 1.5rem;
    --button-font-size:1rem;
    span[class*=icon-]{
        font-size: 0.875rem;
        padding-left: 0.75rem;
    }
  }
}

</style>
