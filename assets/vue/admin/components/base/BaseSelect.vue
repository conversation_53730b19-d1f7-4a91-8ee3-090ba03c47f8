<template>
  <div class="BaseSelect">
    <p>{{ title }}</p>
    <select v-model="innerValue" @change="ChangeSelection($event)">
      <option>Seleccione una opción</option>
      <option v-for="option in options" :key="option.id" :value="option.id">
        {{ option.name }}
      </option>
    </select>
  </div>
</template>

<script>
import ModelMixin from "../mixins/ModelMixin";

export default {
  mixins: [ModelMixin],
  props: {
    title: {
      type: String,
      default: "",
    },

    options: {
      type: Array,
      required: true,
    },
  },

  methods: {
    ChangeSelection(event) {
      //console.log("Cambiando elmento del select", event.target.value);
      this.$emit("change-selection", event.target.value);
    },
  },
};
</script>

 <style scoped lang="scss"> 
$glass: #fdfdfd;
$glass-icon: var(--color-primary);
$gradient: linear-gradient(35deg, red, purple);
$option: #181313;

.BaseSelect {
  select {
    /* Reset */
    appearance: none;
    border: 0;
    outline: 0;
    font: inherit;
    /* Personalize */
    /*  width: 50%; */
    min-width: 20rem;
    height: 2.5em;
    padding: 0 4em 0 1em;
    background: url("../../assets/images/expanded_arrow.png") no-repeat right
        0.8em center / 1.4em,
      linear-gradient(to left, $glass-icon 3em, $glass 3em);
    color: $color-primary;
    border-radius: 0.25em;

    box-shadow: 0 0 0.2em 0 rgba(17, 17, 17, 0.5);
    cursor: pointer;
    /* <option> colors */
    option {
      color: $color-primary;
      background-color: $option;
    }

    &:focus {
      outline: none;
    }

    &::-ms-expand {
      display: none;
    }
  }
}
</style>
