<template>
  <div class="OpinionDetails">
    <BaseModal
      identifier="opinionModal"
      :title="opinion.courseName"
    >
      <div class="Opinion">
        <div class="Opinion--header">
          <img
            class="avatar userAvatar align-self-center align-self-sm-start"
            :src="`/uploads/users/avatars/${opinion.avatar ?? 'default.svg'}`"
            alt=" "
          />
          <div class="d-flex flex-column w-100">
            <span class="username w-100">
              {{ opinion.firstName }} {{ opinion.lastName }}</span>
            <span class="date w-100 mt-1">{{ datePublish }}</span>
          </div>
        </div>
        <StarsType class="mt-3" :question="$t('OPINION.RATING')" :answers="opinion.rating" />
        <TextType class="mt-3" :question="$t('OPINION.TEXT')" :answers="opinion.text" />
        <div v-if="extras.length">
          <component
            class="mt-3"
            v-for="extra in extras"
            :key="'extra_' + extra.id"
            :is="getComponent(extra)"
            :question="extra.question"
            :answers="extra.answers"
          />
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import { get } from 'vuex-pathify'
import BaseModal from '../../../base/BaseModal.vue'
import StarsType from './inputs/StarsType.vue'
import TextType from './inputs/TextType.vue'
import SwitchType from './inputs/SwitchType.vue'
import RadioType from './inputs/RadioType.vue'
import CheckboxType from './inputs/CheckboxType.vue'

export default {
  name: "OpinionDetails",
  components: { CheckboxType, RadioType, TextType, StarsType, SwitchType, BaseModal },
  props: {
    opinion: { type: Object, default: () => ({}) },
  },
  computed: {
    userLocale: get("localeModule/userLocale"),
    
    datePublish() {
      // formatear fecha en formato español y mostrar la hora
      const date = new Date(this.opinion.createdAt);
      const options = {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      };
      
      if (this.userLocale === 'es') return date.toLocaleDateString("es-ES", options);
      
      return date.toLocaleDateString("en-EN", options);
    },
    
    extras() {
      return this.opinion?.extra || []
    }
  },
  methods: {
    getComponent(extra) {
      return {
        nps: StarsType,
        text: TextType,
        checkbox: CheckboxType,
        switch: SwitchType,
        radio: RadioType
      }[extra.type || 'text'] || TextType
    }
  }
}
</script>

<style scoped lang="scss">
.OpinionDetails {
  .Opinion {
    .userAvatar {
      height: auto !important;
      aspect-ratio: 1;
    }
    
    .username {
      font-weight: 500;
    }
    
    .date {
      font-size: 0.9rem;
      color: var(--color-neutral-mid-darker);
    }
    
    &--header {
      display: grid;
      grid-template-columns: 60px 1fr 100px;
      gap: 0.5rem;
      width: 100%;
      padding: 0 1rem;
    }
  }
  
  ::v-deep {
    .modal-header {
      background-color: var(--color-neutral-darker);
      
      .close,
      .modal-title {
        color: #fff;
        text-shadow: none;
      }
      
      .close {
        margin: 0;
      }
    }
    
    .modal-body {
      max-height: min(600px, 65svh);
      overflow-y: auto;
    }
    
    .disabled {
      background-color: var(--color-neutral-lighter);
      color: var(--color-neutral-mid-darker);
    }
  }
}
</style>