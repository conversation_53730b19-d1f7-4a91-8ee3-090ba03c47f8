<template>
  <div class="RadioType">
    <p class="my-0">{{ question }}</p>
    <div class="item mt-1" v-for="(answer, index) in answers" :key="'rb_' + index + answer.id">
      <i class="icon far" :class="{'fa-dot-circle': answer.checked, 'fa-circle': !answer.checked}"/>
      <span>{{ answer.value }}</span>
    </div>
  </div>
</template>

<script>
import typesMixin from './typesMixin'
import RadioButton from '../../../../common/components/RadioButton.vue'

export default {
  name: "RadioType",
  components: { RadioButton },
  mixins: [typesMixin],
}
</script>

 <style scoped lang="scss"> 
.RadioType {
  .item {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  .icon {
    font-size: 1.5rem;
    color: var(--color-neutral-mid);
  }
  
  .icon:not(.fa-circle) {
    color: var(--color-primary);
  }
}
</style>