
<template>
  <div class="task-by-user" >
    <button
      class="btn btn-info btn-sm text-white"
      type="button"
      data-bs-toggle="offcanvas"
      :data-bs-target="`#offcanvasBottom${user.id}`"
      aria-controls="offcanvasBottom"
      @click="fetchTaskUser()"
      :title="translationsVue.taskCourse_labelInPlural"
    >
      <i class="fas fa-tasks text-white"></i>
    </button>

    <div
      class="offcanvas offcanvas-bottom"
      tabindex="-1"
      :id="`offcanvasBottom${user.id}`"
      aria-labelledby="offcanvasBottomLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasBottomLabel">
          {{ translationsVue.taskCourse_labelInPlural }}
        </h5>
        <button
          type="button"
          class="btn-close text-reset btn-close-white"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body small">
        <div class="info-user" v-if="taskByUser">
          <img
            class="avatar"
            :src="user.avatar"
            onerror="this.onerror=null; this.src='/uploads/users/avatars/default.svg'"
            alt=""
          />
          <div class="user">
            <a :href="user.urlUser" class="name"
              >{{ user.firstName }} {{ user.lastName }}
            </a>
            <p class="email">
              <a :href="`mailto:${user.email}`" target="_blank">
                {{ user.email }}</a
              >
            </p>
          </div>
        </div>

        <div class="accordion accordion-flush" id="accordionFlushExample">
          <div class="accordion-item" v-for="(task, index) in taskByUser" :key="task.id">
            <h2
              class="accordion-header"
              :id="`flush-headingOn${task.task.id}${user.id}`"
            >
              <button
                class="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                :data-bs-target="`#flush-collapseOne${task.task.id}${user.id}`"
                aria-expanded="false"
                :aria-controls="`flush-collapseOne${task.task.id}${user.id}`"
              >
               <span class="h4" style="margin-right: 0.5rem"> {{index + 1 }}</span> 
                <span
                  :style="{
                    background: colorButton(task.lastHistory.state),
                    minWidth: '6rem',
                  }"
                  class="badge"
                >
                  {{ stateTask(task.lastHistory.state) }}
                </span>
                
                <span style="margin-left: 0.5rem">{{ task.task.title }}</span>
              </button>
            </h2>
            <div
              :id="`flush-collapseOne${task.task.id}${user.id}`"
              class="accordion-collapse collapse"
              :aria-labelledby="`flush-headingOn${task.task.id}${user.id}`"
              data-bs-parent="#accordionFlushExample"
            >
              <div class="accordion-body">
                <div class="header-title">
                  <div class="row">
                    <div class="col-md-10 col-sms-12">
                      <h6 class="card-subtitle mb-2 text-muted">
                        {{
                          translationsVue.taskCourse_configureFields_dateDelivery
                        }}
                        : {{ task.task.dateDelivery }}
                      </h6>
                      <h6
                        class="card-subtitle mb-2 text-muted"
                        v-if="task.lastHistory.state > 0"
                      >
                        {{
                          translationsVue.taskCourse_configureFields_senTaskUser
                        }}
                        : {{ task.lastHistory.updatedAt }}
                      </h6>
                    </div>

                    <div class="col-md-2 col-sm-12 text-right">
                      <div class="btn-group" v-if="task.lastHistory.state > 0">
                        <button
                          :style="{
                            background: colorButton(task.lastHistory.state),
                            border: 'none',
                          }"
                          type="button"
                          class="btn btn-info dropdown-toggle text-white"
                          data-bs-toggle="dropdown"
                          aria-expanded="false"
                        >
                          {{ stateTask(task.lastHistory.state) }}
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(task.lastHistory.id, 2)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_2
                              }}</a
                            >
                          </li>
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(task.lastHistory.id, 3)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_3
                              }}</a
                            >
                          </li>
                          <li>
                            <a
                              class="dropdown-item"
                              @click="changeStateTask(task.lastHistory.id, 4)"
                              >{{
                                translationsVue.taskCourse_configureFields_state_4
                              }}</a
                            >
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="text-card" v-html="task.task.description"></div>

                  <div class="history-task">
                    <HistoryTask
                      :history-task-user="task.historyTask"
                      @fetch-task-User="fetchTaskUser()"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import HistoryTask from "./HistoryTask";

const colorState = {
  state0: "#A0A6AB",
  state1: "#31D2F2",
  state2: "#c370d3",
  state3: "#EA4335",
  state4: "#19863A",
};

export default {
  components: {
    HistoryTask,
  },

  props: {
    user: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      translationsVue,
    };
  },

  computed: {
    ...get("callModule", ["isLoading", "getTaskByUser"]),

    taskByUser() {
      return this.getTaskByUser();
    },
  },

  methods: {
    async fetchTaskUser() {
      const data = {
        idUser: this.user.id,
        announcement: idAnnouncement,
      };
     
      await this.$store.dispatch("callModule/fetchTaskByUser", data);
    },

    colorButton(state) {
      return colorState[`state${state}`];
    },

    stateTask(state) {
      if (state === 1) {
        return this.translationsVue.taskCourse_configureFields_state_1;
      } else if (state === 2) {
        return this.translationsVue.taskCourse_configureFields_state_2;
      } else if (state === 3) {
        return this.translationsVue.taskCourse_configureFields_state_3;
      } else if (state === 4) {
        return this.translationsVue.taskCourse_configureFields_state_4;
      }

      return this.translationsVue.taskCourse_configureFields_state_0;
    },
    async changeStateTask(idHistory, state) {
      const request = {
        idHistory: idHistory,
        state: state,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.fetchTaskUser();
    },
  },
};
</script>

 <style scoped lang="scss"> 
@import "/assets/css/config/global.scss";;

.task-by-user {
  .text-card {
    margin-top: 1rem;
    font-weight: 300;
  }

  .info-user {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 1rem;
    gap: 0.5rem;
    img {
      width: 7rem;
      height: 7rem;
      border-radius: 50%;
      border: solid 1px #e7e7e7;
    }

    .user {
      .name {
        font-size: 24px;
      }

      .email {
        font-size: 16px;
      }
    }
  }
}

.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 80vh !important;
  max-height: 100%;
}

.offcanvas-header {
  background: $color-app;
  color: #fff !important;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
  color: rgb(255 255 255);
}

.btn-info {
  color: #fff;
}

.offcanvas-body {
  text-align: left;
}
</style>