<template>
<div class="TrainingTable table-responsive">
  <table class="table table-sm table-condensed"> 
    <thead>
    <tr>
      <th>{{ $t('ANNOUNCEMENT.COURSE_TITLE') }}</th>
      <th v-if="showCategory">{{ $t('LIBRARY.CATEGORY') }}</th>
      <th v-if="showItineraryType">{{ $t('ITINERARY.TYPE') }}</th>
      <th v-else class="w170">{{ $t(showResultsTab ? 'TYPE' : 'USERS.FORMATION_TYPE') }}</th>
      <th>{{ $t('USERS.FORMATION_INFO') }}</th>
      <th v-if="currentTag==='announcement'">{{ $t('ANNOUNCEMENT.STATS_REPORT.STATUS') }}</th>
      <th>{{ $t('TOTAL_TIME') }}</th>
      <th class="text-center" v-if="showResultsTab">{{ $t('RESULTS') }}</th>
      <th class="text-center" v-else>{{ $t('DETAILS') }}</th>
    </tr>
    </thead>
    <tbody>
    <tr v-for="(item, index) in paginatedList"
        :key="'TrainingTableItem-' + tag + index"
    >
      <td>
        <div class="d-inline-flex justify-content-between align-items-center gap-3">
          <img class="itemImage" :src="item.thumbnail || ''" alt=" ">
          <div class="d-flex align-items-center gap-2">
            <span>{{ item.title }}</span>
            <i v-if="item.orphaned"
               class="fas fa-unlink text-muted"
               :title="$t('COURSE.ORPHANED_TOOLTIP')"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               style="font-size: 0.8em; opacity: 0.7;"></i>
          </div>
          <i v-if="item.icon" :class="item.icon" class="ms-2"></i>
        </div>
      </td>
      <td class="text-nowrap" v-if="showCategory">{{ item.category }}</td>
      <td class="text-nowrap" v-if="showItineraryType">{{ item.typeItinerary }}</td>
      <td v-else>
        <div class="d-flex justify-content-between align-items-center gap-3 pr-4 text-nowrap">
          <span>{{ item.informationType }}</span>
          <img class="informationIcon" v-if="item.iconUrl" :src="item.iconUrl" alt=" "/>
          <i v-else class="text-primary fa" :class="item.icon"></i>
        </div>
      </td>
      <td>
        <div class="d-flex align-items-center gap-3">
          <p v-if="item.status !== 'NO_STARTED'" class="my-0 d-inline-flex flex-column dates">
            <span class="text-nowrap">{{ formatDate(item.dateStart) }}</span>
            <span class="text-nowrap" :class="{'text-warning': item.status === 'IN_PROCESS'}">{{ formatDate(item.dateEnd) }}</span>
          </p>
          <span class="badge" :class="badgeClass[item.status]">{{ badgeText[item.status] }}</span>
        </div>
      </td>
      <td v-if="currentTag==='announcement'">{{ item.announcementStatus }}</td>
      <td class="text-nowrap">{{ item.status !== 'NO_STARTED' ? getTimeText(item.timeSpent) : '--' }}</td>
      <td class="text-center" v-if="showResultsTab">
        <span v-if="item.status !== 'NO_STARTED' && !item.hideView"
              class="text-primary cursor-pointer"
              @click="showResults(index)">
          {{ $t('VIEW') }}
        </span>
        <span v-else>--</span>
      </td>
      <td class="text-center" v-else>
        <span v-if="item.status !== 'NO_STARTED'"
              class="text-primary cursor-pointer"
              data-bs-toggle="modal"
              data-bs-target="#courseDetails"
              @click="showDetails(index)">
          {{ $t('VIEW') }}
        </span>
        <span v-else>--</span>
      </td>
    </tr>
    </tbody>
  </table>
  <div class="d-flex justify-content-between align-items-center flex-wrap footer pb-3">
    <p class="my-0 mx-sm-0 mx-auto"><b>{{ items.length }}</b> {{ $t('COURSES.HOME.TITLE') }}</p>
    <pagination
        v-show="pageSize < items.length"
        :items="items"
        :page-size="pageSize"
        @items-page="paginatedList=$event"
        class="mx-sm-0 mx-auto"
        :prop-current-page="currentPage"
        @current-page="onCurrentPage"
    />
    <p class="my-0 mx-sm-0 mx-auto">{{ $t('TOTAL_TIME') }}: <b>{{ totalTime }}</b></p>
  </div>
</div>
</template>

<script>
import { dateFormat } from "highcharts";
import Pagination from "../Pagination";
export default {
  name: "TrainingTable",
  components: {Pagination},
  props: {
    tag: {
      type: String,
      default: ''
    },
    showCategory: {
      type: Boolean,
      default: false
    },
    showItineraryType: {
      type: Boolean,
      default: false
    },
    showResultsTab: {
      type: Boolean,
      default: false
    },
    items: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      paginatedList: [],
      pageSize: 10,
      badgeClass: {
        FINISHED: 'badge-success',
        IN_PROCESS: 'badge-warning',
        NO_STARTED: 'badge-light',
      },
      badgeText: {
        FINISHED: this.$t('USERS.STATUS_FINISHED'),
        IN_PROCESS: this.$t('USERS.STATUS_IN_PROCESS'),
        NO_STARTED: this.$t('NO_STARTING'),
      },
      currentPage: 1,
      resetCurrentPage: false,
      currentTag: null,
    }
  },
  computed: {
    totalTime() {
      const total = this.items.reduce((acc, cur) => {
        if (cur.status === 'NO_STARTED') return acc
        const [hours, minutes, seconds] = cur.timeSpent.split(':')
        return [acc[0] + (+hours), acc[1] + (+minutes), acc[2] + (+seconds)]
      },[0, 0, 0])

      const secsToMin = parseInt(`${total[2] / 60}`)
      const secs = total[2] - (secsToMin * 60)

      const minToHour = parseInt(`${(total[1] + secsToMin) / 60}`)
      const minutes = (total[1] + secsToMin) - (minToHour * 60)
      const hours = total[0] + minToHour

      return this.getTimeText([hours, minutes, secs].join(':'))
    }
  },
  watch: {
    tag() {
      this.currentTag = this.tag.split("-")[2];
      this.resetCurrentPage = true;
    }
  },
  mounted() {
    this.paginatedList = this.items.slice(0, this.pageSize);
  },
  methods: {
    formatDate(changedDate) {
      if(!changedDate) return "";
      else{
        let newDate = changedDate.split("T");
        const [year, month, day] = newDate[0].split('-');
        const dateString = `${day}-${month}-${year}`;
        let newHour = newDate[1].split("+")[0];
        return dateString + " " + newHour;
      }
    },
    onCurrentPage(page) {
      if (this.resetCurrentPage) {
        this.currentPage = 1;
        this.resetCurrentPage = false;
      }
      else this.currentPage = page;
    },
    showDetails(index) {
      this.$emit('show-details', this.paginatedList[index]);
    },
    showResults(index) {
      this.$emit('show-results', this.paginatedList[index]);
    },
    getTimeText(time) {
      const [hours, minutes, seconds] = time.split(':').map((val) => isNaN(val) ? 0 : parseInt(val))

      const hourText = `${hours} ${this.$tc('TIME.HOURS', [hours])}`
      const minuteText = `${minutes} ${this.$tc('TIME.MINUTES', [minutes])}`
      const secondText = `${seconds} ${this.$tc('TIME.SECONDS', [seconds])}`

      if (hours) {
        if (minutes && seconds) return `${hourText}, ${minuteText} ${this.$t('TIME.AND')} ${secondText}`
        if (minutes) return `${hourText} ${this.$t('TIME.AND')} ${minuteText}`
        if (seconds) return `${hourText} ${this.$t('TIME.AND')} ${secondText}`
        return `${hourText}`
      }
      if (minutes) {
        return (seconds) ? `${minuteText} ${this.$t('TIME.AND')} ${secondText}` : `${minuteText}`
      }
      return (seconds) ? `${secondText}` : '--'
    }
  }
}
</script>

 <style scoped lang="scss"> 
.TrainingTable {
  .w170 {
    width: 170px;
  }

  .itemImage {
    width: 3rem;
    height: 3rem;
    object-position: center;
    object-fit: cover;
  }

  .footer {
    p {
      font-size: 0.8rem;
    }
  }
  .informationIcon {
    height: 1.2rem;
    aspect-ratio: 1;
  }
  i.fa {
    width: 1.2rem;
    height: 1.2rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
  .teleformation {
    background-image: url("../../../../images/announcement/icon_teleformation.svg");
  }
  .presential {
    background-image: url("../../../../images/announcement/icon_presential.svg");
  }
  .mixed {
    background-image: url("../../../../images/announcement/icon_mixed.svg");
  }
  .virtual {
    background-image: url("../../../../images/announcement/icon_virtual.svg");
  }
}
</style>
