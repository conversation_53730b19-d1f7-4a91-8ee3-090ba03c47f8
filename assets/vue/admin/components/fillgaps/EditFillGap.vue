<template>
  <div class="new-question">
    <div class="card mt-3" style="padding: 1rem">
      <div class="alert alert-info alert-dismissible fade show" role="alert">
        <strong>{{ translationsVue.games_fillgap_title }}</strong>
        {{ translationsVue.games_fillgap_message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>

      <div class="row mb-3">
        <div class="col-md-6">
          <p>{{ translationsVue.chapter_configureFields_type }}</p>
          <select
            class="form-select"
            aria-label="Default select example"
            v-model="type"
          >
            <option selected>Seleccione una opción</option>
            <option value="1">
              {{ translationsVue.fillgaps_configureFields_type_list }}
            </option>
            <option value="2">
              {{ translationsVue.fillgaps_configureFields_type_drag }}
            </option>
          </select>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label for="title" class="form-label"
              >{{ translationsVue.games_text_common_time }}
            </label>
            <input
              type="time"
              id="appt"
              name="appt"
              class="form-control"
              v-model="time"
              min="00:00:00"
              max="23:00:00"
            />
          </div>
        </div>
        <div class="col-md-12">
          <div class="row mt-4">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="question" class="form-label">
                  <b>{{ translationsVue.games_fillgap_word }}</b>
                </label>
                <input
                  type="text"
                  class="form-control"
                  name="question"
                  required
                  v-model="question"
                />
              </div>
            </div>

            <div class="col-md-4 mt-4">
              <div class="mb-3">
                <button class="btn btn-secondary btn-sm" @click="createFrase">
                  {{ translationsVue.games_fillgap_add_filler }}
                </button>
                <button class="btn btn-secondary btn-sm" @click="createHueco">
                  {{ translationsVue.games_fillgap_add_gap }}
                </button>
              </div>
            </div>
            <div class="mb-3 mt-4">
              <div class="card">
                <div class="card-header">
                  <span class="text-primary">
                    <b
                      >{{ translationsVue.games_fillgap_result_question }}
                    </b></span
                  >
                </div>
                <div class="card-body">
                  <div class="row" v-if="fillgap && fillgap.length > 0">
                    <div class="col-md-10">
                      <span v-for="(fill, index) in fillgap" :key="index">
                        <span v-if="!fill.hollow"> {{ fill.text }} </span>
                        <span
                          class="badge rounded-pill bg-info text-white"
                          v-if="fill.hollow"
                        >
                          {{ fill.text }}
                        </span>
                      </span>
                    </div>
                    <div class="col-md-2">
                      <button
                        class="btn btn-secondary btn-sm"
                        @click="resetFilGap()"
                      >
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div v-else>
                    <span>--------</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <dialog>
        <div class="row">
          <div class="form-group">
            <label>{{ translationsVue.games_text_common_word }}</label>
            <input type="text" class="form-control" v-model="option" required />
          </div>

          <div class="col align-self-end text-right mb-2">
            <button class="btn btn-danger btn-sm" @click="closeModal()">
              {{ translationsVue.cancelar }}
            </button>
            <button class="btn btn-primary btn-sm" @click="addOption()">
              {{ translationsVue.games_add_category }}
            </button>
          </div>
        </div>
      </dialog>

      <div class="row ml-2">
        <p><b>Opciones</b></p>
        <ul class="list-group ml-auto">
          <li
            class="list-group-item"
            v-for="(hol, index) in options"
            :key="hol.id"
          >
            {{ index + 1 }}- {{ hol.text }}
          </li>
        </ul>

        <div class="mt-3">
          <button class="btn btn-primary btn-sm" @click="openModal">
            <i class="fas fa-plus"></i>
            {{ translationsVue.game_fillgap_new_option }}
          </button>
        </div>
      </div>
    </div>

    <div class="mt-5 text-center">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button class="btn-sm btn btn-primary" @click="saveQuestion">
        {{ translationsVue.Save }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    routeChapter: {
      type: String,
      default: "",
    },

    fillGap: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      answer: 1,
      correct: false,
      question: "",
      random: false,
      answers: [
        {
          id: 1,
          answer: "",
          correct: true,
        },
      ],
      chapteType: typeChapter,
      allowText: true,
      allowHollow: true,
      fillgap: [],
      hollow: [],
      options: [],
      option: "",
      translationsVue,
      time: "00:00:30",
      type: "1",
      chapterId,
    };
  },

  computed: {
    optionsValidated() {
      return this.options.filter((opt) => opt.text !== "");
    },

    structureQuestion() {
      const structure = this.fillgap.map((fill) => {
        const type = this.type === "1" ? "select" : "drag";
        const text = !fill.hollow ? fill.text : `[id${fill?.idHollow}-${type}]`;

        return text;
      });

      return structure.join(" ");
    },

    textPlain() {
      return this.fillgap
        .map((fill) => {
          return fill.text;
        })
        .join(" ");
    },
  },

  created() {
    this.options =
      this.fillGap?.answers?.map((opt) => {
        return {
          id: opt.id,
          text: opt.answer,
        };
      }) || [];

    this.fillgap = this.fillGap.extra?.fillgap;
    this.type = this.fillGap?.label === "select" ? "1" : "2";
    this.time = this.fillGap?.time;
  },

  methods: {
    createFrase() {
      if (this.question === "") {
        return;
      }

      this.fillgap.push({
        id: this.fillgap.length + 1,
        text: this.question,
        hollow: false,
      });

      this.question = "";
      this.allowHollow = true;
    },

    createHueco() {
      if (this.question === "") {
        return;
      }
      this.allowHollow = false;
      const index = this.hollow.length + 1;

      this.fillgap.push({
        id: this.fillgap.length + 1,
        text: this.question,
        hollow: true,
        idHollow: this.options.length + 1,
      });

      this.hollow.push({
        id: index,
        text: this.question,
      });

      const existOption = this.optionsValidated.find(
        (opt) => opt.text === this.question
      );

      if (!existOption) {
        this.options.push({
          id: this.options.length + 1,
          text: this.question,
        });
      }

      this.question = "";
    },

    addAnswer() {
      this.answer = this.answer + 1;
      this.answers.push({
        id: this.answer,
        answer: "",
        correct: false,
      });
    },

    deleteAnswer(index) {
      this.answers.splice(index, 1);
    },

    async saveQuestion() {
      const hollow = this.fillgap.filter((fill) => fill.hollow);
      const fillgap = this.fillgap.filter((fill) => !fill.hollow);

      if (
        this.optionsValidated.length >= 2 &&
        hollow.length > 0 &&
        fillgap.length > 0
      ) {
        const time = this.time;
        const seconds = time.split(":");
        const secondsTime =
          +seconds[0] * 60 * 60 + +seconds[1] * 60 + +seconds[2];

        const holes = this.hollow.map((hol) => {
          return {
            id: hol.id,
            text: hol.text,
            options: this.options.map((opt) => {
              const isCorrect = opt.text === hol.text;
              return {
                id: opt.id,
                text: opt.text,
                correct: isCorrect,
              };
            }),
          };
        });

        const answers = this.optionsValidated.map((op) => {
          return {
            id: op.id,
            answer: op.text,
          };
        });

        const extra = {
          fillgap: this.fillgap,
          textPlain: this.textPlain,
        };

        const data = {
          id: this.fillGap?.id || null,
          question: this.structureQuestion,
          type: this.type,
          options: JSON.stringify(answers),
          time: secondsTime,
          idChapter: this.chapterId,
          holes: holes,
          extra: JSON.stringify(extra),
        };
        await this.$store.dispatch("fillgapsModule/updateFillGap", data);

        this.$refs["closeChapterContentModal"].click();
        await this.fetchFillGaps();
      } else {
        this.$toast.open({
          message: this.translationsVue.games_fillgap_validate_save,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    addOption() {
      const existOption = this.optionsValidated.find(
        (opt) => opt.text === this.option
      );
      if (this.option === "") {
        this.closeModal();
        return;
      } else if (existOption) {
        this.closeModal();
        return;
      }

      this.options.push({
        id: this.options.length + 1,
        text: this.option,
      });

      this.option = "";
      this.closeModal();
    },

    deletedOption(index) {
      this.options.splice(index, 1);
    },

    resetFilGap() {
      this.fillgap = [];
      this.hollow = [];
      this.question = "";
      this.allowHollow = true;
      this.options = [];
    },

    openModal() {
      const dialog = this.$el.querySelector("dialog");
      dialog.showModal();
    },

    closeModal() {
      const dialog = this.$el.querySelector("dialog");
      dialog.close();
    },

    getTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time - hours * 3600) / 60);
      const seconds = Math.floor(time - hours * 3600 - minutes * 60);

      const minutesFormat = minutes < 10 ? "0" + minutes : minutes;
      const secondsFormat = seconds < 10 ? "0" + seconds : seconds;
      const hoursFormat = hours < 10 ? "0" + hours : hours;

      this.time = hoursFormat + ":" + minutesFormat + ":" + secondsFormat;
    },

    async fetchFillGaps() {
      await this.$store.dispatch(
        "fillgapsModule/getFillgapsChapter",
        this.chapterId
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.new-question {
  .preview-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  input[type="file"] {
    display: none;
  }

  .trash-question {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;

    .trash {
      cursor: pointer;
    }
  }

  dialog {
    border: none;
    max-width: 50%;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: auto;
    padding: 2rem;
  }
}
</style>
