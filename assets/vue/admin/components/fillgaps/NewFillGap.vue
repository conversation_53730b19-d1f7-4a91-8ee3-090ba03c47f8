<template>
  <div class="new-question">
    <div class="card mt-3" style="padding: 1rem">
      <div class="alert alert-info alert-dismissible fade show" role="alert">
        <strong>{{ translationsVue.games_fillgap_title }}</strong>
        {{ translationsVue.games_fillgap_message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>

      <div class="row mb-3">
        <div class="col-md-12">
          <label>{{ translationsVue.chapter_configureFields_type }}</label>
          <select
            class="form-select"
            aria-label="Default select example"
            v-model="type"
          >
            <option selected>Seleccione una opción</option>
            <option value="1">
              {{ translationsVue.fillgaps_configureFields_type_list }}
            </option>
            <option value="2">
              {{ translationsVue.fillgaps_configureFields_type_drag }}
            </option>
          </select>
        </div>

        <div class="col-md-6 mt-3">
          <div class="mb-3">
            <label for="appt" class="form-label"
              >{{ translationsVue.games_text_common_time }}
            </label>

            <BaseInputTime
              v-model="time"
              :options="['minutes', 'seconds']"
              :maxMinutes="31"
              :time="time"
              @time-update="timeUpdate"
            />
          </div>
        </div>
        <div class="col-md-12">
          <div class="row mt-4">
            <div class="">
              <div class="card-header">
                <span class="text-primary">
                  <b>{{
                    currentPage
                      ? translationsVue.games_fillgap_add_fillgap
                      : translationsVue.games_fillgap_add_filler
                  }}</b>
                </span>
              </div>
              <div class="card-body">
                <textarea
                  v-show="!currentPage"
                  type="text"
                  class="form-control"
                  name="question"
                  required
                  v-model="question"
                  :placeholder="translationsVue.common_write"
                  rows="4"
                />
                <div v-show="currentPage" class="phrase-container">
                  <span
                    v-for="(word, index) in words"
                    :key="'word'.index"
                    @click="toggleFillGap(index)"
                    class="cursor-pointer"
                    :class="{ selected: word.selected }"
                  >
                    {{ word.text }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row ml-2" v-show="currentPage">
        <p v-if="options.length > 0">
          <b>{{ translationsVue.fillgaps_configureFields_fillgaps }}</b>
        </p>
        <ul class="list-group ml-auto">
          <li
            class="list-group-item d-flex justify-content-between align-content-center"
            v-for="hol in options"
            :key="'fillgap-' + hol.id"
          >
            <span>{{ hol.id }} - {{ hol.text }}</span>
          </li>
        </ul>
      </div>
    </div>

    <div class="mt-5 text-center">
      <button
        v-show="0"
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        ref="closeChapterContentModal"
      ></button>

      <button
        class="btn-sm btn btn-primary"
        v-show="!currentPage"
        @click="setPage(1)"
        v-bind="
          countWordInQuestion < 3 ? { disabled: true } : { disabled: false }
        "
      >
        {{ translationsVue.Next }}
      </button>
      <button
        class="btn-sm btn btn-secondary"
        v-show="currentPage"
        @click="setPage(0)"
      >
        {{ translationsVue.back }}
      </button>
      <button
        class="btn-sm btn btn-primary"
        v-show="currentPage"
        @click="saveQuestion"
      >
        {{ translationsVue.Save }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    fillGapData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      question: "",
      currentPage: 0,
      isUpdate: false,
      words: [],
      fillgap: [],
      options: [],
      translationsVue,
      time: "00:00:30",
      type: "1",
      chapterId,
      dispatchURL: "fillgapsModule/createFillGap",
    };
  },

  computed: {
    optionsValidated() {
      return this.options.filter((opt) => opt.text !== "");
    },

    structureQuestion() {
      return this.fillgap
        .map((fill) => {
          const type = this.type === "1" ? "select" : "drag";
          return !fill.hollow ? fill.text : `[id${fill?.idHollow}-${type}]`;
        })
        .join(" ");
    },

    textPlain() {
      return this.fillgap.map((fill) => fill.text).join(" ");
    },

    questionText() {
      return this.question.replace(/\s+/g, " ").trim();
    },

    countWordInQuestion() {
      var trimmedSentence = this.question.trim();

      var words = trimmedSentence.split(/\s+/);

      return words.length;
    },
  },

  created() {
    this.resetFilGap();
    if (this.fillGapData.extra) {
      this.isUpdate = true;
      this.dispatchURL = "fillgapsModule/updateFillGap";
      this.type = this.fillGapData?.label === "select" ? "1" : "2";
      if (!this.fillGapData.extra.newVersion) this.cleanBlankSpaces();
      this.initDataNewVersion();
    }
  },

  methods: {
    cleanBlankSpaces() {
      this.fillGapData.extra.textPlain.replace(/\s+/g, " ").trim();
      this.fillGapData.extra.fillgap.forEach((fillgap) => {
        fillgap.text = fillgap.text.replace(/\s+/g, " ").trim();
      });
    },

    initDataNewVersion() {
      this.question = this.fillGapData.extra.textPlain;
      this.words = this.fillGapData.extra.fillgap.reduce((acc, cur) => {
        const words = cur.text
          .split(" ")
          .map((word) => ({ text: word, selected: cur.hollow }));
        return [...acc, ...words];
      }, []);
      this.regenerateFragments();
      this.currentPage = 1;
    },

    resetFilGap() {
      this.question = "";
      this.fillgap = [];
      this.options = [];
      this.currentPage = 0;
    },

    setPage(num) {
      this.currentPage = num;
      if (this.currentPage === 1 && this.textPlain !== this.questionText)
        this.splitPhrase();
    },

    splitPhrase() {
      this.words = this.questionText
        .split(" ")
        .map((word) => ({ text: word, selected: false }));
      this.regenerateFragments();
    },

    saveQuestion() {
      const hollow = this.fillgap.filter((fill) => fill.hollow);
      const fillgap = this.fillgap.filter((fill) => !fill.hollow);

      if (hollow.length >= 2 && fillgap.length >= 1) {
        const time = this.time;
        const seconds = time.split(":");

        this.$store
          .dispatch(this.dispatchURL, {
            id: this.fillGapData.id || null,
            idChapter: this.chapterId,
            time: +seconds[0] * 3600 + +seconds[1] * 60 + +seconds[2],
            type: `${this.type}` === "1" ? "select" : "drag",
            options: this.options,
            question: this.structureQuestion,
            extra: JSON.stringify({
              fillgap: this.fillgap,
              textPlain: this.textPlain,
              newVersion: true,
            }),
          })
          .then(() => {
            if (!this.isUpdate) this.resetFilGap();
            this.$refs["closeChapterContentModal"].click();
          });
      } else {
        const { games_fillgap_validate_save } = this.translationsVue;
        this.$toast.open({
          message: games_fillgap_validate_save,
          type: "warning",
          duration: 5000,
          position: "top-right",
        });
      }
    },

    getFillgapsChapter() {
      this.$store.dispatch("fillgapsModule/getFillgapsChapter", this.chapterId);
    },

    toggleFillGap(index) {
      console.log("Es la palabra seleccionada");
      this.words[index].selected = !this.words[index].selected;
      console.log(this.words[index].selected);
      this.regenerateFragments();
    },

    regenerateFragments() {
      this.fillgap = [];
      let fillgapIndex = -1;

      

      this.words.forEach((word, index) => {
        if (index && word.selected === this.words[index - 1].selected) {
          this.fillgap[fillgapIndex].text =
            `${this.fillgap[fillgapIndex].text} ${word.text}`.trim();
        } else {
          fillgapIndex += 1;
          this.fillgap = [
            ...this.fillgap,
            {
              id: fillgapIndex,
              text: word.text,
              ...(word.selected
                ? { hollow: true, idHollow: 0 }
                : { hollow: false }),
            },
          ];
        }
      });

      this.options = [];

      this.fillgap.forEach((fillgap, index) => {
        if (fillgap.hollow) {
          fillgap.idHollow = this.options.length + 1;
         
          this.options = [
            ...this.options,
            { id: fillgap.idHollow, text: fillgap.text },
          ];
        }
      });
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.new-question {
  .phrase-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem 0;
    user-select: none;

    span {
      margin: 0 0.15rem;
    }

    .selected {
      background-color: var(--color-primary);
      color: white;
      padding: 0 0.25rem;
      border-radius: 3px;
      margin: 0;
    }
  }
}
</style>
