import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    called: undefined,
    notified: undefined,
    results: undefined,
    filters: undefined,
    loading: false, // todo add funcionality
    searching: false, // todo add funcionality
    calling: false, // todo add funcionality
    notifying: false,

});

const state = () => getDefaultState();

export const getters = {
    getCalled: (state) => () => state.called,
    isLoading: (state) => () => state.loading,
    isSearching: (state) => () => state.searching,
    isCalling: (state) => () => state.calling,

};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchFilters({ commit }, announcement) {
        const url = `/admin/announcements/${announcement}/user_filters`;
        let bd_data;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.get(url);
            bd_data = data.data;
            commit('SET_FILTERS', bd_data);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return bd_data;
    },


    async fetchCalled({ commit }, announcement) {
        const url = `/admin/announcements/${announcement}/called`;
        let called;

        try {
            commit('SET_LOADING', true);
            const {data} = await axios.get(url);
            called = data.data.called;
            commit('SET_CALLED', called);
        } finally {
            commit('SET_LOADING', false);
        }

        return called;
    },

    async fetchSearch({ commit }, search) {
        const url = `/admin/announcements/${search.announcement}/search_users_filters`;
        let results;
        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.post(
                url,
                {
                    q: search.searchQuery,
                    filters:search.filters,
                }
            );
            results = data.data.results;
            commit('SET_RESULTS', results);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return results;
    },

    async fetchCallEverybody({ commit }, search) {
        const url = `/admin/announcements/${search.announcement}/call_filters`;
        let error;
        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.post(
                url,
                {
                    q: search.searchQuery,
                    filters:search.filters,
                }
            );
            error = data.error;
        } finally {
            commit('SET_SEARCHING', false);
        }

        return error;
    },

    async fetchCall({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/call/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchUnCall({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/uncall/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchNotify({ commit }, call) {
        const url = `/admin/announcements/${call.announcement}/notify/${call.user}`;
        let result;

        try {
            commit('SET_NOTIFYING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_NOTIFIED', result);
        } finally {
            commit('SET_NOTIFYING', false);
        }

        return result;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
