import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
    thread: [],
    gifdir: undefined,
});

const state = () => getDefaultState();

export const getters = {
    getGif: (state) => () => state.gifdir,
};

export const mutations = {
    ...make.mutations(state),

    ADD_GIFDIR(state, payload) {
        state.gifdir = payload;
    },

    PURGE_GIFDIR(state) {
        state.gifdir = undefined;
        },

};

export const actions = {

    async fetchThread({commit}, id) {
        const url = `/admin/forum/${id}`;
        let thread;

        try {
            const {data} = await axios.get(url);
            thread = data;
            commit('SET_THREAD', thread);
        } finally {
        }

        return thread;
    },

    sendComment(context, comment) {
        const url = '/admin/forum/postcomment';
        return axios.post(url, comment);
    },

    async addGif({ commit }, payload) {
        commit('ADD_GIFDIR', payload);
        return true;
    },

    async purgeGif({ commit }) {
        commit('PURGE_GIFDIR');
        return true;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
