import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    start: undefined,
    chapter: undefined,
    timeout: 10000, //in miliseconds
    interval: undefined,
    sending: false, // todo add funcionality
});

const state = () => getDefaultState();

export const getters = {
    isSending: (state) => () => state.sending,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchTime({ commit, state }) {
        const url = `/api/chapter/${state.chapter}/update`;
        let sended;
        const time = Date.now();
        const interval = Math.round((time - state.start) / 1000);
        console.log('Interval', interval);
        if(!state.sending && interval > 0){
            try {
                commit('SET_SENDING', true);
                const {data} = await axios.post(
                    url,
                    {
                        time: interval
                    },
                    {headers: { Authorization: 'Bearer ' + localStorage.getItem('token')},}
                );
            } finally {
                commit('SET_SENDING', false);
                commit('SET_START', time);
                console.log('New start', state.start);
                sended = true;
            }
        }
        return sended;
    },
    initStart: ({commit, state, dispatch}, chapter) => {
        commit('SET_START', Date.now());
        commit('SET_CHAPTER', chapter);
        console.log('Chapter', chapter);
        let interval = setInterval(() => {
            const sended = dispatch('fetchTime');
        }, state.timeout);
        commit('SET_INTERVAL', interval);
        return state.start;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
