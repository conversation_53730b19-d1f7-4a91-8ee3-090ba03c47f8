import axios  from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
	loading      : false,
	routeChapter : '',
	adivinaImagen: undefined
});

const state = () => getDefaultState();

export const getters = {
	isLoading: (state) => () => state.loading,

	getRouteChapter: (state) => () => state.routeChapter,

	getAdivinaImagen: (state) => () => state.adivinaImagen,
};

export const mutations = {
	...make.mutations(state),
};

export const actions = {
	async fetchAdivina({commit}, idChapter) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.get(`/admin/adivina-image/${idChapter}`);
			commit('SET_ADIVINA_IMAGEN', data?.data);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async newAdivinaimagen({commit}, formData) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post('/admin/new/adivina-image', formData);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async editAdivinaimagen({commit}, formData) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post('/admin/edit/adivina-image/', formData);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async deleteAdivinaImagen({commit}, formData) {
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post('/admin/delete/adivina-image', formData);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	}
};

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions,
};
