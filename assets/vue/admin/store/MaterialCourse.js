import axios from "axios";
import { make } from "vuex-pathify";

const getDefaultState = () => ({
  loading: false,
  uploadFile: undefined,
  detail: undefined,
  materialsCourse: undefined,
  routeCourse: undefined,
  routeTask: undefined,
  filesTask: undefined,
  historyTask: undefined,
  fileBasePath: ''
});

const state = () => getDefaultState();

export const getters = {
  getUpload: (state) => () => state.uploadFile,
  getDetail: (state) => () => state.detail,
  getMaterialsCourse: (state) => () => state.materialsCourse,
  getRouteCourse: (state) => () => state.routeCourse,
  getRouteTask: (state) => () => state.routeTask,
  getFilesTask: (state) => () => state.filesTask,
  getHistoryTask: (state) => () => state.historyTask
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  async newMaterialCourse({ commit }, request) {
    try {
      const url = `/admin/material-course`;
      const options = {
        headers: { "Content-Type": "multipart/form-data" },
      };

      const formData = new FormData();
      formData.append("course", request.course);
      formData.append("announcement", request?.announcement);
      formData.append("typeMaterial", request.typeMaterial);
      formData.append("count-files", request.files.length);

      for (let i = 0; i < request.files.length; i += 1) {
        formData.append(`file${i}`, request.files[i]);
      }

      const { data } = await axios.post(url, formData, options);
      commit("SET_MATERIALS_COURSE", data?.data?.materials);
      commit("SET_ROUTE_COURSE", data?.data?.route);

    } catch (e) {
      throw e;
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchMaterialCourse({ commit }, request) {
    commit("SET_LOADING", true);
    try {
        const url = `/admin/fetch-material-course/${request.idCourse}`;
        const { data, basePath } = await axios.get(url);
        commit("SET_MATERIALS_COURSE", data?.data.map((material) => {
          const newMaterial = { ...material }
          newMaterial.disableDownload = (newMaterial.typeMaterial === '2')
          newMaterial.disableVisible = (![1,2,4].includes(+newMaterial.typeMaterial))
          return newMaterial
        }));
        commit("SET_FILE_BASE_PATH", data?.basePath);
    
      /*  window.Vue.$toast.success(`${data?.data}`); */
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al cargar los archivos");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteMaterialCourse({ commit }, request) {
    try {
      const url = `/admin/delete-material-course`;
      const { data } = await axios.post(url, request);
      commit("SET_MATERIALS_COURSE", data?.data);
      commit("SET_ROUTE_COURSE", data?.data?.route);
      /*  window.Vue.$toast.success(`${data?.data}`); */
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al cargar los archivos");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async allowDonwload({ commit }, request) {
    try {
      const url = `/admin/update-material-course`;
      await axios.post(url, request);
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al actualizar el registro");
      throw e;
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async setMaterialCourseActive({}, { id, isActive }) {
    const url = `/admin/material-course/${id}/active`;
    const result = await axios.put(url, { isActive });
    return result.data;
  },

  async setMaterialCourseVisibitily({}, { id, visibitily }) {
    const url = `/admin/material-course/${id}/visibitily`;
    const result = await axios.put(url, { visibitily });
    return result.data;
  },

  async newFileTaskCourse({ commit }, request) {
    try {
      const url = `/admin/new/task-course`;
      const options = {
        headers: { "Content-Type": "multipart/form-data" },
      };

      console.log("data", request);
      const formData = new FormData();
      formData.append("idTask", request.idTask);
      formData.append("files", request.files);
      formData.append("typeMaterial", request.typeMaterial);
      formData.append("count-files", request.files.length);

      for (let i = 0; i < request.files.length; i += 1) {
        formData.append(`file${i}`, request.files[i]);
      }

      const { data } = await axios.post(url, formData, options);
      commit("SET_FILES_TASK", data?.data?.filesTask);
      commit("SET_ROUTE_TASK", data?.data?.route);

      window.Vue.$toast.success(`${data?.data?.message}`);
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al agregar el material");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchFilesTask({ commit }, request) {
    try {
      const url = `/admin/fetch-files-task/${request.idTask}`;
      const { data } = await axios.get(url);
      commit("SET_FILES_TASK", data?.data);

      /*  window.Vue.$toast.success(`${data?.data}`); */
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al cargar los archivos");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async allowDonwloadFileTask({ commit }, request) {
    try {
      const url = `/admin/update-file-task`;
      await axios.post(url, request);
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al actualizar el registro");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteFileTask({ commit }, request) {
    try {
      const url = `/admin/delete-file-task`;
      await axios.post(url, request);

      /*  window.Vue.$toast.success(`${data?.data}`); */
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al eliminar el archivo");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchHistoryTask({ commit }, request) {
    try {
      const url = `/admin/history-task/${request.idTask}`;
      const { data } = await axios.get(url);
      commit("SET_HISTORY_TASK", data?.data);

      /*  window.Vue.$toast.success(`${data?.data}`); */
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al cargar los archivos");
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async downloadFileTaskUser(context, file) {
    await axios
      .get(`/admin/file-taskuser/${file.id}`, { responseType: "blob" })
      .then((response) => {
        const blob = new Blob([response], { type: response.type });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = file.fileOriginalName;
        link.click();
        URL.revokeObjectURL(link.href);
      })
      .catch((e) => {
        window.Vue.$toast.error(e.message);
      });
  },

  async commentTask({ commit }, request) {
    try {
      const url = `/admin/comment-task`;
      const { data } = await axios.post(url, request);

      console.log("data", data);
      window.Vue.$toast.success(`${data?.data?.message}`);
    } catch (e) {
      window.Vue.$toast.error("Ocurrio un error al enviar el comentario");
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
