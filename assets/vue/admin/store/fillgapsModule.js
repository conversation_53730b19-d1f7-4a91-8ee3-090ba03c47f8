import axios  from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({
	loading        : false,
	fillgapsChapter: undefined,
	routeChapter   : undefined,
});

const state = () => getDefaultState();

export const getters = {
	isLoading: (state) => () => state.loading,

	getFillgapsChapter: (state) => () => state.fillgapsChapter,

	getRouteChapter: (state) => () => state.routeChapter,
};

export const mutations = {
	...make.mutations(state),
};

export const actions = {
	setBlock(context, data) {
		const url = "/admin/chapter/fillgaps/set-block";
		return axios.post(url, data);
	},

	editBlock(context, data) {
		console.log('modulo', data)
		const url = "/admin/chapter/fillgaps/edit-block";
		return axios.post(url, data);
		;
	},

	reloadBlock(context, data) {
		;
		const url = "/admin/chapter/fillgaps/reload-block";
		return axios.post(url, data);
	},

	deleteLine(context, data) {
		const url = "/admin/chapter/fillgaps/delete-block";
		return axios.post(url, data);
	},

	async getFillgapsChapter({commit}, chapterId) {
		const url = `/admin/fillgaps/${chapterId}`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.get(
				url);

			commit('SET_FILLGAPS_CHAPTER', data?.data);
			commit('SET_ROUTE_CHAPTER', data?.route);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async createFillGap({commit}, requestData) {
		const url = `/admin/fillgaps/create`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post(
				url, requestData);

			commit('SET_FILLGAPS_CHAPTER', data?.data);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async updateFillGap({commit}, requestData) {
		const url = `/admin/fillgaps/update`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post(
				url, requestData);

			commit('SET_FILLGAPS_CHAPTER', data?.data);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	},

	async deleteFillGap({commit}, requestData) {
		const url = `/admin/fillgaps/delete`;
		try {
			commit('SET_LOADING', true);
			const {data} = await axios.post(
				url, requestData);

			commit('SET_FILLGAPS_CHAPTER', data?.data);
		} catch (e) {
			console.log(e)
		} finally {
			commit('SET_LOADING', false);
		}
	}


};

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions,
};
