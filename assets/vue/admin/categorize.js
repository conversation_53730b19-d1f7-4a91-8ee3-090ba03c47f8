import Vue from 'vue';
import '../../vue/registerBaseComponents';
import categorizegame from './views/Categorize';
import store from './store';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';




window.Vue = new Vue({
    components: { categorizegame },
    store,
}).$mount('#categorize')

Vue.use(VueToast, {
    duration: 5000,
    position: 'top-right',
  });
