<template>
  <div class="Lettersoup">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${typeChapter}`"
        @click="adicionar"
      >
        {{ translationsVue.add }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${typeChapter}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="staticBackdropLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.pairs_configureFields_title }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${typeChapter}`"
              ref="closeModal"
              @click="submitted = false"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitForm" id="form-question">
              <div class="form-question-time">
                <div>
                  <BaseTextTarea
                    :label="
                      translationsVue.rouletteWord_configureFields_statement
                    "
                    :max="170"
                    :value.sync="question"
                    :placeholder="
                      translationsVue.pairs_configureFields_placeholder_title
                    "
                    :required="true"
                    :rows="3"
                    :submitted="submitted"
                  ></BaseTextTarea>
                </div>
                <div>
                  <label for="time">{{
                    translationsVue.games_text_common_time
                  }}</label>

                  <BaseInputTime
                    v-model="time"
                    :options="['minutes', 'seconds']"
                    :maxMinutes="31"
                    :time="time"
                    @time-update="timeUpdate"
                  />
                </div>
              </div>

              <div class="form-words">
                <div
                  class="words"
                  v-for="(word, index) in words"
                  :key="word.id"
                >
                  <div class="inputWord">
                    <BaseTextTarea
                      :label="
                        translationsVue.games_text_common_word +
                        ' ' +
                        (index + 1) +
                        ':'
                      "
                      :min="2"
                      :max="12"
                      :value.sync="word.word"
                      :placeholder="translationsVue.games_help_write_word"
                      :required="true"
                      :rows="1"
                      :submitted="submitted"
                      :preventSpace="true"
                    ></BaseTextTarea>
                  </div>

                  <div class="button-delete" v-if="index > 0">
                    <a class="btn btn-danger btn-sm" @click="removeWord(index)">
                      <i class="fas fa-trash-alt"></i>
                    </a>
                  </div>
                </div>
              </div>

              <div>
                <div class="mt-2">
                  <a
                    class="btn btn-sm btn-primary"
                    @click="addNewWord"
                    v-show="words.length <= 13"
                  >
                    <i class="fas fa-plus"></i>
                  </a>
                </div>

                <div class="text-center mt-5">
                  <button
                    v-show="0"
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                    ref="closeChapterContentModal"
                  ></button>

                  <button
                    v-if="!proccesSave"
                    type="submit"
                    data-dismiss="modal"
                    class="btn btn-primary ml-1 btn-sm"
                    @click="submitted = true"
                  >
                    {{ translationsVue.Save }}
                  </button>
                  <loader :isLoaded="proccesSave"></loader>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div>
      <loader :isLoaded="!showCalled"></loader>
      <div>
        <div v-if="lines" class="col-md-12">
          <table class="table datagrid">
            <thead>
              <tr>
                <th>
                  <span>{{ translationsVue.question_label_in_singular }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.games_words }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.games_text_common_time }}</span>
                </th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <Resume
                v-for="(block, index) in questions"
                :key="index"
                :block="block"
                @deleteLine="deleteLine"
                @modifyLine="modifyLine"
              />
            </tbody>
          </table>
        </div>

        <!-- Modal Edit -->
        <div
          class="modal fade"
          id="editQuestion"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="editQuestionLabel"
          aria-hidden="true"
        >
          <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="editQuestionLabel">
                  {{ translationsVue.pairs_configureFields_title }}
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  @click="closeModalEdit()"
                ></button>
              </div>
              <div class="modal-body">
                <form @submit.prevent="submitForm" id="form-question">
                  <div class="form-question-time">
                    <div>
                      <label for="title" class="form-label"
                        >{{
                          translationsVue.rouletteWord_configureFields_statement
                        }}
                        : <sup class="text-danger">*</sup></label
                      >

                      <BaseTextTarea
                        :label="
                          translationsVue.rouletteWord_configureFields_statement
                        "
                        :max="170"
                        :value.sync="question"
                        :placeholder="
                          translationsVue.pairs_configureFields_placeholder_title
                        "
                        :required="true"
                        :rows="3"
                        :submitted="submitted"
                      ></BaseTextTarea>
                    </div>
                    <div>
                      <label for="time">{{
                        translationsVue.games_text_common_time
                      }}</label>
                      <BaseInputTime
                        v-model="time"
                        :options="['minutes', 'seconds']"
                        :maxMinutes="31"
                        :time="time"
                        @time-update="timeUpdate"
                      />
                    </div>
                  </div>

                  <div class="form-words">
                    <div
                      class="words"
                      v-for="(word, index) in words"
                      :key="word.id"
                    >
                      <div class="inputWord">
                        <BaseTextTarea
                          :label="
                            translationsVue.games_text_common_word +
                            ' ' +
                            (index + 1) +
                            ':'
                          "
                          :min="2"
                          :max="12"
                          :value.sync="word.word"
                          :placeholder="translationsVue.games_help_write_word"
                          :required="true"
                          :rows="1"
                          :submitted="submitted"
                          :preventSpace="true"
                        ></BaseTextTarea>
                      </div>

                      <div class="button-delete">
                        <a
                          class="btn btn-danger btn-sm"
                          @click="removeWord(index)"
                          v-if="index > 0"
                        >
                          <i class="fas fa-trash-alt"></i>
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div class="mt-2">
                      <a
                        class="btn btn-sm btn-primary"
                        @click="addNewWord"
                        v-show="words.length <= 13"
                      >
                        <i class="fas fa-plus"></i>
                      </a>
                    </div>

                    <div class="text-center mt-5">
                      <button
                        v-show="0"
                        type="button"
                        class="btn btn-secondary"
                        data-bs-dismiss="modal"
                        ref="closeChapterContentModal"
                      ></button>

                      <button
                        v-if="!proccesSave"
                        type="submit"
                        class="btn btn-primary ml-1 btn-sm"
                      >
                        {{ translationsVue.Save }}
                      </button>
                      <loader :isLoaded="proccesSave"></loader>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <BaseModalDelete
          identifier="deleteModal"
          :title="translationsVue.quiz_configureFields_question_delete"
          @delete-element="deleteAnswer"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Resume from "../components/lettersoup/Resume";
import Options from "../components/lettersoup/Options";
import { get } from "vuex-pathify";
import Loader from "../components/Loader";

import { alertToastMixin } from "../../mixins/alertToastMixin";
import { formatDateMixin } from "../../mixins/formatDateMixin";
import { modalMixin } from "../../mixins/modalMixin";

export default {
  components: {
    Options,
    Resume,
    Loader,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [alertToastMixin, formatDateMixin, modalMixin],

  data() {
    return {
      called: true,
      lines: this.blocks,
      addLine: false,
      question: "",
      word: "",
      id: 0,
      time: "00:00:30",
      mensajeActivo: false,
      modificarRegistro: false,
      modificarPalabras: false,
      titulo: "",
      mostrarRespuestas: false,
      caracteresMinimosWord: 2,
      caracteresMaximosWord: 8,
      typeChapter,
      translationsVue,
      dataDelete: undefined,
      words: [
        {
          id: 1,
          word: "",
        },
      ],
      proccesSave: false,
      submitted: false,
    };
  },

  computed: {
    ...get("callModule", ["isLoading"]),

    ...get("lettersoupModule", ["getQuestions"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    wordsJoin() {
      return this.words
        .filter((word) => word.word !== "")
        .map((word) => word.word)
        .join(",");
    },

    questions() {
      return this.getQuestions();
    },
  },

  created() {
    this.fetchQuestions();
  },

  methods: {
    adicionar() {
      this.clearCurrent();
      this.titulo = "Adicionar pregunta";
      this.addLine = true;
      this.modificarRegistro = false;
    },

    async submitForm() {
      const form = document.getElementById("form-question");
      if (!form.checkValidity()) {
        return;
      }

      try {
        this.saveQuestion();
        this.alertSuccesSave();
        await this.fetchQuestions();
        this.$refs["closeChapterContentModal"].click();
        this.$refs["closeModal"].click();
        this.reloadBlocks();

        this.proccesSave = false;
      } catch (error) {
        this.alertErrorSave();
      }
    },

    async saveQuestion() {
      this.proccesSave = true;
      this.submitted = false;
      if (this.modificarRegistro) {
        await this.actionUpdateQuestion();
      } else {
        await this.actionSaveQuestion();
      }
    },

    async actionSaveQuestion() {
      await this.$store.dispatch(
        "lettersoupModule/setBlock",
        this.dataForsubmitForm()
      );
    },

    async actionUpdateQuestion() {
      await this.$store.dispatch(
        "lettersoupModule/editBlock",
        this.dataForsubmitForm()
      );
    },

    dataForsubmitForm() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);
      const data = {
        id: this.id,
        chapterId: this.chapterId,
        title: this.question,
        words_array: this.wordsJoin,
        time: secondsTime,
        gametype: "lettersoup",
      };

      if (!this.modificarRegistro) {
        const { id, ...dataWithoutId } = data;
        return dataWithoutId;
      }

      return data;
    },

    async fetchQuestions() {
      const data = {
        chapterId: this.chapterId,
        gametype: "lettersoup",
      };

      await this.$store.dispatch("lettersoupModule/reloadBlock", data);
    },

    cerrarVentanaEdit() {
      this.addLine = false;
      this.mensajeActivo = false;
      this.modificarRegistro = false;
    },

    cancelar() {
      this.clearCurrent();
      this.addLine = false;
      this.mensajeActivo = false;
      this.modificarRegistro = false;
    },

    clearCurrent() {
      this.question = "";
      this.word = "";
      this.time = "00:00:30";
      this.words = [
        {
          id: 1,
          word: "",
        },
      ];
    },

    async reloadBlocks() {
      const data = {
        chapterId: this.chapterId,
        gametype: "lettersoup",
      };
      this.called = false;

      this.$store
        .dispatch("lettersoupModule/reloadBlock", data)
        .then((response) => {
          this.called = true;
          this.lines = response.data.data;
        });
    },

    deleteLine(value) {
      const data = {
        id: value,
      };

      this.dataDelete = data;
      this.openModal("deleteModal");
    },

    async deleteAnswer() {
      await this.$store
        .dispatch("lettersoupModule/deleteLine", this.dataDelete)
        .then((response) => {
          this.reloadBlocks();
        });

      this.closeModal("deleteModal");
    },

    modifyLine(data) {
      this.words = [];
      this.id = data.id;
      this.question = data.question;
      this.word = data.word;
      this.time = this.convertSecondToHoursMinutesAndSeconds(data.time);
      this.addLine = true;
      this.modificarRegistro = true;
      this.titulo = "Modificar pregunta";

      this.words = data.word.split(",").map((word, index) => {
        return {
          id: index + 1,
          word: word,
        };
      });

      /*  const modalEdit = document.getElementById("editQuestion");
      modalEdit.classList.add("show");
      modalEdit.style.display = "block";

      modalEdit.setAttribute("aria-modal", "true");
      modalEdit.setAttribute("role", "dialog");
      modalEdit.removeAttribute("aria-hidden");
      modalEdit.setAttribute("style", "display: block; padding-right: 17px;"); */
    },
    modifyWord(words_array) {
      this.word = words_array.join(",");
      this.modificarPalabras = false;
    },

    adicionarWord(words_array) {
      this.word = words_array.join(",");
    },

    closeModalEdit() {
      this.submitted = false;
      this.question = "";
      this.words = [
        {
          id: 1,
          word: "",
        },
      ];
      this.word = "";
      this.time = "00:00:30";

      this.closeModal("editQuestion");
    },

    addNewWord() {
      this.words.push({ id: this.words.length + 1, word: "" });
    },

    removeWord(index) {
      this.words.splice(index, 1);
    },

    clearInputs() {
      this.question;
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Lettersoup {
  background: #fff;
  padding-top: 2rem;
  form {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 1rem;

    .form-question-time {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .form-words {
      .words {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        flex-wrap: wrap;
        align-items: center;

        .inputWord {
          flex: 1;
        }
      }
    }
  }
}
</style>
