<template>
  <div class="material-course" v-if="process == 0">   
    <BaseSelect
     class="base-select"
      :title="translationsVue.material_course_configureFields_type"
      :options="options"
      v-model="typeFile"
    >
    </BaseSelect>

    <BaseInputFile
      tag="file"
      v-bind="validationInputFile"
      :placeholder="translationsVue.material_course_placeholder_file"
      @files-selected="fileSelected($event)"
      id="input-file"
    />

    <div class="actions">
      <button class="btn btn-secondary" data-bs-dismiss="modal">
        {{ translationsVue.cancelar }}
      </button>
      <button class="btn btn-primary" @click="saveFileTaskCourse()" id="save-file">
        {{ translationsVue.Save }}
      </button>
    </div>
  </div>
  <div v-else class="spinner">
    <BaseSpinner />
    <p class="wait">{{ translationsVue.component_video_preparing_file }}</p>
  </div>
</template>

<script>
import { get } from "vuex-pathify";

import BaseSelect from "../components/base/BaseSelect";
import BaseInputFile from "../components/base/BaseInputFile";
import BaseSpinner from "../components/base/Spinner";

export default {
  name: "taskCourse",
  components: {
    BaseSelect,
    BaseInputFile,
    BaseSpinner,
  },

  props:{
    idAnnouncement:{
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      idCourse,
      idTask,
      translationsMessage,
      translationsVue,
      typeFile: 5,
      files: null,
      process: 0,
    };
  },

  computed: {
    ...get("materialCourse", ["isLoading", "getRouteTask"]),

    routeTask() {
      return this.getRouteTask();
    },

    options() {
      let typeMaterial = [];
      for (let i = 1; i <= 6; i++) {
        typeMaterial.push({
          id: i,
          name: this.translationsVue[
            `material_course_configureFields_type_${i}`
          ],
        });
      }
      return typeMaterial;
    },

    validationInputFile() {
      if (this.typeFile === 1) {
        return {
          accept: "application/pdf",
          multiple: "multiple",
        };
      } else if (this.typeFile === 2) {
        return { accept: "video/*" };
      } else if (this.typeFile === 3) {
        return {
          accept: ".zip, .rar",
          multiple: "multiple",
        };
      } else if (this.typeFile === 4) {
        return {
          accept: "image/*",
          multiple: "multiple",
        };
      } else if (this.typeFile === 5) {
        return {
          accept:
            ".doc, .docx, .docx, .docm, .dot, .dotx, .odt, dotm, .cvs, .dbf, .xla, .xls, .xlsb, .xlsm, .xlsx, .pptx, .pptm, .ppt, .mpt, .mpx, .mpp, .vsdx, .vsdm, .vssx, .vssm, .vstx, .vstm, .accdb",
          multiple: "multiple",
        };
      } else if (this.typeFile === 6) {
        return {
          accept: ".txt",
          multiple: "multiple",
        };
      }
    },  
  },

  watch: {
    files() {
      const bottom = document.getElementById("save-file");
      bottom.removeAttribute("disabled");

      bottom.classList.remove("btn-secondary");
    },
  },

  mounted() {
    this.disabledButtomSave();
  },

  methods: {
    fileSelected(event) {
      this.files = event;
    },

    disabledButtomSave() {
      const bottom = document.getElementById("save-file");
      bottom.setAttribute("disabled", "disabled");

      bottom.classList.add("btn-secondary");
    },

    activateButtonSave() {
      const bottom = document.getElementById("save-file");
      bottom.removeAttribute("disabled");

      bottom.classList.remove("btn-secondary");
    },

    async saveFileTaskCourse() {
        const data = {
          idTask: this.idTask,
          files: this.files,
          typeMaterial: this.typeFile,
        };
;
      this.disabledButtomSave();
      this.process = 1;
      if (this.typeFile >= 1 && this.typeFile <= 6 && this.files !== null) {
        await this.$store.dispatch("materialCourse/newFileTaskCourse", data);
      }
      this.process = 0;

      location.reload();
    //  window.location.href = this.routeTask;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.material-course {
  .base-select{
    margin-bottom: 2rem;
  }
  .actions {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    justify-content: flex-end;
    margin-top: 3rem;
  }
}

.spinner {
  .wait {
    text-align: center;
    font-size: 2rem;
  }
}
</style>