<template>
  <div class="Wordle">
    <div class="col align-self-end text-right mb-2">
      <button
          type="button"
          class="btn btn-primary"
          data-bs-toggle="modal"
          :data-bs-target="`#modal-chapter-${typeChapter}`"
      >
        {{ translationsVue.question_configureFields_add_question }}
      </button>
    </div>

    <div
        class="modal fade"
        :id="`modal-chapter-${typeChapter}`"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-labelledby="staticBackdropLabel"
        aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="staticBackdropLabel">
              {{ translationsVue.enigma_configureFields_title_creation }}
            </h5>
            <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                :id="`close-modal-chapter-${typeChapter}`"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitForm">
              <div class="question">
                <BaseInput
                    :label="translationsVue.quiz_configureFields_question"
                    :max="150"
                    :value.sync="question"
                    :placeholder="translationsVue.quiz_configureFields_question_placeholder"
                    :required="true"
                    :submitted="submitted"
                ></BaseInput>
              </div>
              <div class="question">
                <BaseInput
                    :label="translationsVue.games_text_common_word"
                    :max="6"
                    :min="3"
                    :value.sync="word"
                    :placeholder="translationsVue.games_help_write_word"
                    :required="true"
                    :submitted="submitted"
                    :preventSpace="true"
                    :validateSpecialCharacters="true"
                    :acceptSpecialCharactersEnie="true"
                    :upperCaseCharacters="true"
                >
                </BaseInput>
              </div>
              <div class="question">
                <label for="time">{{
                    translationsVue.games_text_common_time
                  }}</label>

                <BaseInputTime
                    v-model="time"
                    :options="['minutes', 'seconds']"
                    :maxMinutes="31"
                    :time="time"
                    @time-update="timeUpdate"
                />
              </div>
              <div class="question">
                <label class="labelMensaje" v-if="mensajeActivo">{{
                    mensaje
                  }}</label>
              </div>
              <div class="text-center mt-4">
                <button
                    v-show="0"
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                    ref="closeChapterContentModalNew"
                ></button>

                <button
                    v-if="!procesSave"
                    type="submit"
                    aria-label="Close"
                    class="btn btn-primary ml-1 btn-sm"
                    @click="submitted = true"
                >
                  {{ translationsVue.Save }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div>
      <loader :isLoaded="!showCalled"></loader>
      <div v-if="showCalled">
        <div v-if="questions && questions.length > 0" class="col-md-12">
          <table class="table datagrid">
            <thead>
            <tr>
              <th>
                <span>{{ translationsVue.question_label_in_singular }}</span>
              </th>
              <th>
                <span>{{ translationsVue.games_text_common_word }}</span>
              </th>
              <th>
                <span>{{ translationsVue.games_text_common_time }}</span>
              </th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <Resume
                v-for="(block, index) in questions"
                :key="index"
                :block="block"
                @deleteLine="deleteLine"
                @modifyLine="modifyLine"
            />
            </tbody>
          </table>
        </div>
        <!-- Modal Edit -->
        <div
            class="modal fade"
            id="editQuestion"
            data-bs-backdrop="static"
            data-bs-keyboard="false"
            tabindex="-1"
            aria-labelledby="staticBackdropLabel"
            aria-hidden="true"
        >
          <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="editQuestionLabel">
                  {{ translationsVue.enigma_configureFields_title_creation }}
                </h5>
                <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                    @click="closeModalEdit()"
                ></button>
              </div>
              <div class="modal-body">
                <form @submit.prevent="submitForm">
                  <div class="question">
                    <BaseTextTarea
                        :label="translationsVue.quiz_configureFields_question"
                        :max="150"
                        :value.sync="question"
                        :placeholder="
                        translationsVue.quiz_configureFields_question_placeholder
                      "
                        :required="true"
                        :rows="4"
                        :submitted="submitted"
                    ></BaseTextTarea>
                  </div>
                  <div class="question">
                    <BaseTextTarea
                        :label="translationsVue.games_text_common_word"
                        :min="3"
                        :max="6"
                        :value.sync="word"
                        :placeholder="translationsVue.games_help_write_word"
                        :required="true"
                        :rows="2"
                        :submitted="submitted"
                        :preventSpace="true"
                        :validateSpecialCharacters="true"
                        :acceptSpecialCharactersEnie="true"
                    ></BaseTextTarea>
                  </div>
                  <div class="question">
                    <label for="time">{{
                        translationsVue.games_text_common_time
                      }}</label>
                    <BaseInputTime
                        v-model="time"
                        :options="['minutes', 'seconds']"
                        :maxMinutes="31"
                        :time="time"
                        @time-update="timeUpdate"
                    />
                  </div>

                  <div class="text-center mt-4">
                    <button
                        v-show="0"
                        type="button"
                        class="btn btn-secondary"
                        data-bs-dismiss="modal"
                        ref="closeChapterContentModal"
                    ></button>

                    <button
                        v-if="!procesSave"
                        type="submit"
                        aria-label="Close"
                        class="btn btn-primary ml-1 btn-sm"
                        @click="submitted = true"
                    >
                      {{ translationsVue.Save }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Resume from "../components/wordle/Resume";
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import NewWordle from "../components/wordle/NewWordle";

import { alertToastMixin } from "../../mixins/alertToastMixin";
import { formatDateMixin } from "../../mixins/formatDateMixin";
import { modalMixin } from "../../mixins/modalMixin";
import BaseInput from "../../base/BaseInput";

export default {
  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [alertToastMixin, formatDateMixin, modalMixin],

  data() {
    return {
      called: true,
      lines: this.blocks,
      addLine: false,
      question: "",
      word: "",
      id: 0,
      time: "00:00:30",
      mensajeActivo: false,
      modificarRegistro: false,
      titulo: "",
      typeChapter,
      translationsVue,
      dataDelete: undefined,
      procesSave: false,
      submitted: false,
    };
  },

  components: {
    BaseInput,
    Resume,
    Loader,
    NewWordle,
  },

  computed: {
    ...get("callModule", ["isLoading"]),

    ...get("wordleModule", ["getQuestions"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    questions() {
      return this.getQuestions();
    },
  },

  async created() {
    await this.fetchQuestions();
  },

  methods: {
    async submitForm() {
      const form = document.querySelector("form");
      if (!form.checkValidity()) {
        this.submitted = true;
        return;
      }

      try {
        await this.saveQuestion();
        this.alertSuccesSave();
        this.$refs["closeChapterContentModal"].click();
        this.$refs["closeChapterContentModalNew"].click();
        this.clearFormInput();
        await this.fetchQuestions();
        this.processSave = false;
      } catch (e) {
        this.alertErrorSave();
      }
    },

    async saveQuestion() {
      this.processSave = true;
      this.submitted = false;
      if (this.modificarRegistro) {
        await this.actionUpdateQuestion();
      } else {
        await this.actionsSaveQuestion();
      }
    },

    async actionsSaveQuestion() {
      await this.$store.dispatch(
          "wordleModule/setBlock",
          this.dataForsubmitForm()
      );
    },

    async actionUpdateQuestion() {
      await this.$store.dispatch(
          "wordleModule/editBlock",
          this.dataForsubmitForm()
      );
    },

    dataForsubmitForm() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      const data = {
        id: this.id,
        chapterId: this.chapterId,
        title: this.question,
        words_array: this.word,
        time: secondsTime,
        gametype: "wordle",
      };

      if (!this.modificarRegistro) {
        const { id, ...dataWithoutId } = data;
        return dataWithoutId;
      }

      return data;
    },

    async fetchQuestions() {
      const data = {
        chapterId: this.chapterId,
        gametype: "wordle",
      };

      await this.$store.dispatch("wordleModule/reloadBlock", data);
    },

    clearFormInput() {
      this.question = "";
      this.word = "";
      this.time = "00:00:30";
      this.modificarRegistro = false;
    },

    cerrarVentanaEdit() {
      this.addLine = false;
      this.mensajeActivo = false;
      this.modificarRegistro = false;
    },

    async reloadBlocks() {
      const data = {
        chapterId: this.chapterId,
        gametype: "wordle",
      };
      this.called = false;

      await this.$store
          .dispatch("wordleModule/reloadBlock", data)
          .then((response) => {
            this.called = true;
            this.lines = response.data.data;
          });
    },

    deleteLine(value) {
      const data = {
        id: value,
      };

      this.dataDelete = data;
      this.deleteAnswer();
    },

    async deleteAnswer() {
      await this.$store
          .dispatch("wordleModule/deleteLine", this.dataDelete)
          .then((response) => {
            this.reloadBlocks();
          });
    },

    modifyLine(data) {
      this.id = data.id;
      this.question = data.question;
      this.time = this.convertSecondToHoursMinutesAndSeconds(data.time);
      this.word = data.word;
      this.addLine = true;
      this.modificarRegistro = true;
      this.titulo = "Modificar pregunta";

      // this.openModal("editQuestion");
    },

    closeModalEdit() {
      this.question = "";
      this.word = "";
      this.time = "00:00:30";

      // this.closeModal("editQuestion");
    },

    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Wordle {
  background: #fff;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.question {
  display: flex;
  flex-direction: column;
}

.labelMensaje {
  width: 60%;
  padding: 0.5rem;
  align-content: center;
  color: red;
}

.modal-body {
  padding-top: 0;
}
</style>