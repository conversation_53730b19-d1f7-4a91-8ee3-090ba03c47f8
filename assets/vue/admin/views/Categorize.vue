<template>
  <div class="categorize">
    <Categories
      :categories="categoriesOptions"
      @question-button-available="questionButtonAvailable = $event"
    ></Categories>

    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-new`"
        :disabled="!questionButtonAvailable"
      >
        <i class="fas fa-plus"></i>
        {{ translationsVue.question_configureFields_add_question }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-new`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="modal-contentLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modal-contentLabel">
              {{ translationsVue.question_configureFields_add_question }}
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-new`"
            ></button>
          </div>
          <div class="modal-body">
            <AddQuestion
              :categories="categoriesCopy"
              v-if="categoriesOptions.length >= 2"
            ></AddQuestion>
          </div>
        </div>
      </div>
    </div>

    <table class="table" v-if="categories">
      <thead>
        <tr>
          <th scope="col">
            {{ translationsVue.material_course_configureFields_type_4 }}
          </th>
          <th scope="col">{{ translationsVue.question_label_in_singular }}</th>
          <th scope="col">{{ translationsVue.games_text_common_time }}</th>
          <th scope="col">{{ translationsVue.state }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="category in categories" :key="category.id">
          <td>
            <a
              data-bs-toggle="modal"
              :data-bs-target="`#question-image${category.id}`"
            >
              <img
                :src="`/uploads/games/categorize/${category.image}`"
                onerror="this.onerror=null; this.src='/assets/chapters/noimg.svg'"
                width="30px"
              />
            </a>

            <BaseViewImagen
              :identifier="`question-image${category.id}`"
              :image="`/uploads/games/categorize/${category.image}`"
            />
          </td>
          <td>{{ category.question }}</td>
          <td>
            {{ convertSecondToHoursMinutesAndSeconds(category.time) }}
          </td>
          <td>
            <div
              :class="
                category.state == false
                  ? 'icon tooltip-container icon-background-warning'
                  : 'icon tooltip-container icon-background-success'
              "
            >
              <i
                :class="
                  category.state == false ? 'fas fa-info' : 'fas fa-check'
                "
              ></i>

              <span class="tooltip tooltip-top" v-if="category.state == false">
                Esta pregunta debe tener una opción correcta
              </span>
            </div>
          </td>
          <td class="text-right">
            <button
              type="button"
              class="btn-sm btn btn-danger"
              data-bs-toggle="modal"
              :data-bs-target="`#deleteModal${category.id}`"
            >
              <i class="fas fa-trash-alt"></i>
            </button>

            <button
              type="button"
              class="btn btn-primary btn-sm"
              data-bs-toggle="modal"
              :data-bs-target="`#modal-edit-question${category.id}`"
            >
              <i class="fas fa-edit"></i>
            </button>
          </td>

          <BaseModal
            :identifier="`modal-edit-question${category.id}`"
            :title="translationsVue.question_configureFields_add_question"
          >
            <EditQuestion
              :categories="categoriesCopy"
              v-if="categoriesOptions.length >= 2"
              :question="category"
            ></EditQuestion>
          </BaseModal>

          <BaseModalDelete
            :identifier="`deleteModal${category.id}`"
            :title="translationsVue.quiz_configureFields_question_delete"
            @delete-element="deleteCategorize(category.id)"
          />
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Loader from "../components/Loader";
import Categories from "../components/categorize/Categories";
import AddQuestion from "../components/categorize/addQuestion";
import EditQuestion from "../components/categorize/editQuestion";

import { formatDateMixin } from "../../mixins/formatDateMixin";

export default {
  components: {
    Loader,
    Categories,
    AddQuestion,
    EditQuestion,
  },

  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [formatDateMixin],

  data() {
    return {
      selected: false,
      categoriesCopy: [],
      typeChapter,
      translationsVue,
      questionButtonAvailable: false,
    };
  },

  computed: {
    ...get("questionsGamesModule", [
      "isLoading",
      "getCategoriesOptions",
      "getCategories",
      "getRouteChapter",
    ]),

    categoriesOptions() {
      const categories = this.getCategoriesOptions();
      this.categoriesCopy = [];

      const result = categories.map((category) => {
        return {
          id: category.id,
          name: category.name,
          image: category.image,
          selected: false,
        };
      });

      this.categoriesCopy = Object.assign([], result);

      return result;
    },

    categories() {
      return this.getCategories();
    },

    routeChapter() {
      return this.getRouteChapter();
    },
  },

  async created() {
    await this.$store.dispatch(
      "questionsGamesModule/fetchCategoriesOptions",
      this.chapterId
    );
    await this.$store.dispatch(
      "questionsGamesModule/fetchCategories",
      this.chapterId
    );
    this.categoriesCopy = Object.assign([], this.categoriesOptions);
  },

  methods: {
    async deleteCategorize(idQuestion) {
      const formData = new FormData();
      formData.append("id", idQuestion);

      await this.$store.dispatch(
        "questionsGamesModule/deleteCategories",
        formData
      );

      await this.fetchCategories();
    },

    async fetchCategories() {
      await this.$store.dispatch(
        "questionsGamesModule/fetchCategories",
        this.chapterId
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.categorize {
  background: #fff;

  .tooltip-container {
    position: relative;
    display: inline-block;
    color: white;
    width: 1.7rem;
    height: 1.5rem;
    border-radius: 0.2rem;
    text-align: center;
    padding: 3.2px;
  }
}

modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 2rem !important;
}

img {
  width: 50px;
  cursor: zoom-in;
}

.btn-close, :deep(.btn-close) {
  color: #212121;
  opacity: 1;
  filter: none;
}
</style>
