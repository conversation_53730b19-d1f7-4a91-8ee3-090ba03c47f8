import Vue from 'vue';
import axios from 'axios';
import Setting from './components/settings/Setting.vue';
import AddRemove from "../common/components/select/AddRemove.vue";
import {getI18nApi} from "../common/i18n";


/**
 * Call user locale information and init i18n, if successful, @call startVueApp
 */
getI18nApi().then((data) => {
    startVueApp(data);
})


function startVueApp({ i18n, locales, locale}) {
    new Vue({
        delimiters: ['${', '}'],
        components: {
            Setting, AddRemove
        },
        i18n,
        data() {
            return {
                loading: true,
                settingGroups: [],
                timezones: [],
                selectedTimezones: []
            };
        },
        async mounted() {
            await axios.get('/admin/settings/list')
                .then(response => {
                    this.settingGroups = response.data.data.groups;
                    const dTimezones = response.data.data.timezones;
                    const sTimezones = response.data.data.selectedTimezones;
                    this.selectedTimezones = sTimezones.map(t => ({id: t, name: t}));
                    this.timezones = dTimezones.map(t => ({id: t, name: t}));
                }).finally(() => {
                    this.loading = false;
                });
        },
        computed: {
            settings(){
                return this.settingGroups.reduce((acc, group) => {
                    return acc.concat(group.settings);
                }, []);
            }
        },
        methods: {
            async save() {
                const timezones = this.selectedTimezones.map(t => t.id);
                await axios.post('/admin/settings/save', {settings: this.settings, timezones})
                    .then(response => {
                        window.location.reload();
                    });
            }

        },
    }).$mount('.content-wrapper');
}
