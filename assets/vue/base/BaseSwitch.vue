<template>
  <div class="BaseSwitch" :class="[`theme--${theme}`]">
    <input
      :id="name"
      v-model="innerValue"
      :name="name"
      :disabled="disabled"
      type="checkbox"
      @change="$emit('change', innerValue)"
    />

    <label :for="name" />

    <span v-if="positive" class="negative">{{ negative }}</span>
    <span v-if="negative" class="positive">{{ positive }}</span>
  </div>
</template>

<script>
export default {
  name: "BaseSwitch",
  props: {
    tag: {
      type: String,
      default: "switcher",
    },

    disabled: {
      type: Boolean,
      default: false,
    },

    value: {
      type: null,
      default: undefined,
    },

    theme: {
      type: String,
      default: "light",
      validator: (theme) => ["light", "dark"].includes(theme),
    },
  },

  computed: {
    innerValue: {
      get() {
        return this.value;
      },

      set(newValue) {
        this.$emit("input", newValue);
      },
    },

    name() {
      return this.tag;
    },

    positive() {
      return this.getOption(0);
    },

    negative() {
      return this.getOption(1);
    },
  },

  methods: {
    toggleOption() {
      this.innerValue = !this.innerValue;
    },

    getOption(option) {
      const options = this.$attrs?.options;
      const optionKey = option + 1;
      if (!options || !(optionKey in options)) return undefined;

      return options[optionKey];
    },
  },
};
</script>

 <style scoped lang="scss"> 
.BaseSwitch {
  display: flex;
  align-items: center;

  &.theme-- {
    &light {
      --local-color: var(--color-neutral-mid);
      --local-negative-color: var(--color-neutral-dark);
      --local-positive-color: var(--color-primary-light);
    }
    &dark {
      --local-color: var(--color-neutral-light);
      --local-negative-color: #fff;
      --local-positive-color: var(--color-primary-lightest);
    }
  }

  span {
    // text-transform: uppercase;
    font-size: 0.8rem;
    color: var(--local-color);
  }

  span.negative {
    order: 1;
    margin-right: 0.8rem;
  }

  span.positive {
    order: 3;
    margin-left: 0.8rem;
  }

  input[type="checkbox"] {
    height: 0;
    width: 0;
    position: absolute;
    order: 2;

    &:focus + label {
      box-shadow: 0 0 2px 1px var(--color-primary);
    }
  }

  label {
    order: 2;
    cursor: pointer;
    text-indent: -9999px;
    width: 35px;
    height: 17px;
    background: var(--color-neutral-mid);
    display: block;
    border-radius: 100px;
    position: relative;
    margin: 0;

    &:after {
      content: "";
      position: absolute;
      top: 2px;
      left: 2px;
      width: 13px;
      height: 13px;
      background: #fff;
      border-radius: 90px;
      transition: 0.3s;
    }
  }

  input:not(:disabled):checked + label {
    background: var(--color-primary);
  }

  input:disabled + label {
    color: var(--color-neutral-mid) !important;
  }

  input:disabled {
    &:checked + label {
      background: var(--color-primary) !important;
      opacity: 0.5;
    }
  }

  input:checked + label:after {
    left: calc(100% - 2px);
    transform: translateX(-100%);
  }

  input:checked + label + .negative + .positive {
    // color: var(--color-primary-lighter);
    color: var(--color-neutral-dark);
    font-weight: 600;
  }

  input:not(:checked) + label + .negative {
    color: var(--color-neutral-light);
    font-weight: 600;
  }

  label:active:after {
    width: 23px;
  }
}
</style>
