import Vue   from 'vue';
import axios from 'axios';
import $     from 'jquery'
import 'bootstrap';

import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

import mySelect from '../vue/admin/components/html/select';
import Loader from "../vue/admin/components/Loader";
import TaskQueueMixin from '../vue/mixins/TaskQueueMixin';


Vue.use(VueToast);


Vue.directive('init', {
	bind: function (el, binding, vnode) {
		vnode.context[binding.arg] = binding.value;
	}
});

let vueVar = new Vue({
	delimiters: ['${', '}'],
	components: {
		Loader,
		mySelect 
	},
	mixins: [TaskQueueMixin],
	data() {
		return {
			filters     : {
				filename   				: undefined,
				country    				: undefined,
				center     				: undefined,
				category   				: undefined,
				departament				: undefined,
				start      				: undefined,
				end        				: undefined,
				gender     				: undefined,
				active	   				: undefined,
				courseStartedIntime		: undefined,
				courseFinishedIntime	: undefined,
				//course_full			: undefined,
				//course_intime			: undefined,
			},
			onlyActivesList: [
				{ id: 0, name: 'Inactivos' },				
				{ id: 1, name: 'Activos' },
				{ id: 2, name: 'Todos' },
			],
			customFilter: {},
			modalDetails: {},
			registers   : [],
			intervalID  : undefined,
			countries   : {},
			centers     : {},
			categories  : {},
			departaments: {},
			genders     : {},
			showLoader  : false,
			filterExportedExcel:[],
			locale      : 'es',
			toastLocales: {
				'es' : { error: 'Se ha producido un error', success: 'Solicitud realizada con exito', info: 'Procesando su solicitud, por favor espere', fileSuccess: 'Sus archivos han sido generados con exito', notFound: 'No se encontro el reporte' },
				'en' : { error: 'An error has occurred', success: 'Successful request', info: 'Processing your request, please wait', fileSuccess: 'Your files have been successfully generated', notFound: 'Report not found' },
				'pt' : { error: 'Ocorreu um erro.', success: 'Pedido de sucesso', info: 'Processamento do seu pedido, por favor, espere', fileSuccess: 'Os seus ficheiros foram gerados com sucesso', notFound: 'Relatório não encontrado' },
			}
		}
	},
	computed: {
		toastText () { return this.toastLocales[this.locale] || this.toastLocales['en']; }
	},
	created() {
		this.initFilters();
		this.loadData();
	},
	methods   : {
		dateFormat(date) {
			return `${date.getDate()}`.padStart(2, '0')  + '-' + `${(date.getMonth() + 1)}`.padStart(2, '0') + '-' +  date.getFullYear();
		},

		firstDateOfMonth(date) {
			return this.dateFormat(new Date(date.getFullYear(), date.getMonth(), 1));
		},

		initFilters() {
			this.filters = {
				filename   		 		: 'Export Data ' + this.dateFormat(new Date()),
				filenameuser   	 		: 'User Export ' + this.dateFormat(new Date()),
				country   		 		: '',
				center    		 		: '',
				category  		 		: '',
				departament		 		: '',
				gender    		 		: '',
				custom	  		 		: '',
				start     		 		: '', //this.firstDateOfMonth(date),
				end       		 		: '', //this.dateFormat(date),
				active			 		: 1,
				courseStartedIntime 	: 0,
				courseFinishedIntime 	: 0,
				//course_full		 	: 0,
				//course_intime	 		: 0,
			};
			if (this.filters['active']){
				//console.log( this.filters['active'] );
				this.filters['active'] = this.onlyActivesList.find(item => item.id === this.filters['active']).id;
				//console.log( this.filters['active'] );
				
			}

			['#country', '#center', '#category', '#departament', '#gender'].forEach((id) => {
				const element = $(`${id}`);
				element.find('option').attr("selected", false);
				element.val(null).trigger('change');
			});
			$('.clear-button').trigger('click');

			//active users filter
			$('.BaseSelect select').val(this.filters['active']);
			$('.BaseSelect select').on('change', function() {
				
				vueVar.updateActiveUsersfilter(this.value);
			});
		},	
		updateActiveUsersfilter(pfilterValue) {
			console.log(pfilterValue);
			//vueVar.filters['active'] = pfilterValue;
		},
		loadData() {
			this.showLoader = true;

			const translations = JSON.parse(document.getElementById("translations").value);

			axios.post('/admin/stats/export-data-info').then(response => {
				this.registers = response?.data?.data || [];

				this.registers.forEach(register => {
					register.statusText = this.getStatusText(register.status, translations);
				});

				if (this.registers.some(register => register.status === 0 || register.status === 1)) {
					if (this.intervalID) clearInterval(this.intervalID);
					this.intervalID = setInterval(this.loadData, 20000);
				} else if (this.intervalID) {
					this.$toast.success(this.toastText.fileSuccess);
					clearInterval(this.intervalID);
					this.intervalID = undefined;
				}
			}).finally(() => { 
				this.showLoader = false; 
				$(function () { $('[data-toggle="tooltip"]').tooltip() }); 
			});
		},

		getStatusText(status, translations) {
			const statusMap = {
				'-2': translations.review,
				'-1': translations.failure,
				'0': translations.pending,
				'1': translations.in_progress,
				'2': translations.success
			};
			return statusMap[status] || translations.error;
		},

		openModal(clearFilters = true) {
			if (clearFilters) this.initFilters();
			$('#exportModal').modal('show');
		},

		sendForm() {
			if (this.filters.end != null && this.filters.end != "" && this.filters.end < this.filters.start) {
				let newEnd = this.filters.start;
				this.filters.start = this.filters.end;
				this.filters.end = newEnd;
			}

			if(Object.keys(this.customFilter).length > 0){
				this.filters.custom = Object.values(this.customFilter);
			}

			this.showLoader = true;
			$('#exportModal').modal('hide');

			this.enqueueTask({
				url: '/admin/stats/export',
				data: this.prepareData(this.filters),
				messages: {
					success: this.toastText.success,
					error: this.toastText.error
				},
				onSuccess: () => {
					this.registers = [];
					this.loadData();
				},
				onError: () => {
					this.showLoader = false;
				}
			});
		},

		async sendFormUser() {
			if (this.filters.end < this.filters.start) {
				let newEnd = this.filters.start;
				this.filters.start = this.filters.end;
				this.filters.end = newEnd;
			}

			if(Object.keys(this.customFilter).length > 0){
				this.filters.custom = Object.values(this.customFilter);
			}

			this.showLoader = true;

			await this.enqueueTask({
				url: '/admin/user_data_export',
				data: this.prepareData(this.filters),
				messages: {
					success: this.toastText.success,
					error: this.toastText.error
				},
				onSuccess: () => {
					setTimeout(() => {
						location.reload();
					}, 1500);
				}
			});
	
		},

		abortExportRequest(index) {
			this.showLoader = true;
			this.$toast.info( this.toastText.info);
			axios.post('/admin/stats/abort-export', this.prepareData({id: this.registers[index].id}))
				.then(() => {
					this.$toast.success( this.toastText.success);
					this.registers = [];
					this.loadData();
				}).catch(() => { this.$toast.error( this.toastText.notFound); this.showLoader = false; });
		},

		downloadFile(index, id = null, filename = null) {
			this.showLoader = true;
			this.$toast.info( this.toastText.info);
			axios.post('/admin/stats/stats-download-file', this.prepareData({id: id || this.registers[index].id}), {responseType: 'blob'})
				.then(response => {
					this.$toast.success( this.toastText.success );
					this.parseAndDownloadFile(response, filename || this.registers[index].filename);
				}).catch(() => { this.$toast.error( this.toastText.notFound ); })
				.finally(() => { this.showLoader = false; });
		},

		prepareData(object) {
			const params = new URLSearchParams();
			Object.keys(object).forEach(key => params.append(key, object[key]))
			return params;
		},

		parseAndDownloadFile(response, filename = null) {
			const fileURL = window.URL.createObjectURL(new Blob([response.data]))
			let fileLink = document.createElement('a');

			fileLink.href = fileURL;
			fileLink.setAttribute('download', `${filename || this.filters.filename}.xlsx`);
			document.body.appendChild(fileLink);

			fileLink.click();
		},

		async viewDetails(index) {
			this.filterExportedExcel = undefined;
			this.modalDetails = {...this.registers[index] };
			const customFilter = this.registers[index]?.meta?.customFilters;					
			if(customFilter != undefined){
				const customFilterSplit = customFilter.split(',');
				await this.nameExportedFilters(customFilterSplit);		
			}			
			$('#detailsModal').modal('show');
		},

		async nameExportedFilters(filters){
			try {
				const data = {filters};
				const response = await axios.post('/admin/stats/name-filter-export', data);
				this.filterExportedExcel = response?.data?.data;
			  } catch (error) {
				this.$toast.error( this.toastText.error);
			  }
		},

		countryName(id)     { return this.getNameByID(this.countries, id); },
		centerName(id)      { return this.getNameByID(this.centers, id); },
		categoryName(id)    { return this.getNameByID(this.categories, id); },
		departamentName(id) { return this.getNameByID(this.departaments, id); },
		genderName(id)      { return this.getNameByID(this.genders, id); },

		getNameByID(arr, id) {
			let found = (arr || []).find(element => `${element.id}` === `${id}`);
			return found ? found.name : id;
		}
	}, filters: {
		date  : (text) => {
			if (!text) return '-';
			let dateArray = text.split(' ');
			if (dateArray.length) {
				dateArray = dateArray[0].split('-');
				if (dateArray.length === 3)
					return `${dateArray[0]}-${dateArray[1]}-${dateArray[2]}`;
			}
			return '-';
		}
	}
}).$mount('#export-data');
