import $ from 'jquery';
import "../css/player.scss";

$(function() {
  $(".play").on("click", function(e) {
    e.preventDefault();
    const PathImage = 'uploads/images/chapter/'; 
    const url = $(this).data("url");
    const title = $(this).data("title");
    const image = PathImage + $(this).data("image");

    $("#player-wrapper #player-actions #player-title").html(title);
    if (url !== undefined) $("#player iframe").attr("src", url)
    if(image !== undefined) $("#player iframe").attr("style", `background-image:url("${image}")`);

    $("#player-wrapper")
      .css("display", "flex")
      .hide()
      .fadeIn();
  });

  $("#player #player-actions .fa-close").on("click", function(e) {
    e.preventDefault();

    const iframeSrc = $("#player iframe").attr("src"); 
    $("#player-wrapper").fadeOut();
    $("#player iframe").attr("src", "");

    const reladPattern = /roleplay/gi;

    if(iframeSrc.match(reladPattern)) {
      window.location.reload();
    }
  });

  window.addEventListener("message", (event) => {
    if (event.data === "close-modal") {
      event.preventDefault();
      $("#player-wrapper").fadeOut();
      $("#player iframe").attr("src", "");
    }
  });
});
