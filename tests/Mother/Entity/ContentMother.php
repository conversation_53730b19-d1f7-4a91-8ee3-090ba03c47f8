<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Chapter;
use App\Entity\Content;

class ContentMother
{
    public const string DEFAULT_TITLE = 'Content';
    public const string DEFAULT_CONTENT = '<strong>DEFAULT</strong>';

    public static function create(
        ?int $id = null,
        ?string $title = null,
        ?string $content = null,
        ?int $position = 1,
        ?Chapter $chapter = null,
    ): Content {
        $entity = new Content();

        if (null !== $id) {
            $entity->setId($id);
        }

        $entity->setTitle($title ?? self::DEFAULT_TITLE);
        $entity->setContent($content ?? self::DEFAULT_CONTENT);
        $entity->setPosition($position);
        $entity->setChapter($chapter);

        return $entity;
    }
}
