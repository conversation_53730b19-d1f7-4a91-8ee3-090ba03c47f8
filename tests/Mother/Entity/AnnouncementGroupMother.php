<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\SessionsAnnouncement;
use App\Entity\TypeMoney;

class AnnouncementGroupMother
{
    public static function create(
        ?int $id = null,
        ?string $companyProfile = null,
        ?string $code = null,
        ?string $companyCif = null,
        ?string $denomination = null,
        ?string $fileNumber = null,
        ?array $announcementUsers = null,
        ?Announcement $announcement = null,
        ?AnnouncementTutor $announcementTutor = null,
        ?int $numSessions = null,
        ?string $place = null,
        ?SessionsAnnouncement $sessionsAnnouncement = null,
        ?array $announcementGroupSessions = null,
        ?array $classroomvirtuals = null,
        ?array $taskCourseGroups = null,
        ?array $announcementNotificationGroups = null,
        ?int $groupNumber = null,
        ?string $cost = null,
        ?TypeMoney $typeMoney = null
    ): AnnouncementGroup {
        $announcementGroup = new AnnouncementGroup();

        if (null !== $id) {
            $announcementGroup->setId($id);
        }

        $announcementGroup->setCompanyProfile($companyProfile)
            ->setCode($code)
            ->setCompanyCif($companyCif)
            ->setDenomination($denomination)
            ->setFileNumber($fileNumber)
            ->setNumSessions($numSessions)
            ->setPlace($place)
            ->setGroupNumber($groupNumber)
            ->setTypeMoney($typeMoney)
            ->setAnnouncement($announcement)
            ->setAnnouncementTutor($announcementTutor)
            ->setSessionsAnnouncement($sessionsAnnouncement);

        if (null !== $cost) {
            $announcementGroup->setCost($cost);
        }

        if (null !== $announcementUsers) {
            foreach ($announcementUsers as $announcementUser) {
                $announcementGroup->setAnnouncementUsers($announcementUser);
            }
        }

        if (null !== $announcementGroupSessions) {
            foreach ($announcementGroupSessions as $announcementGroupSession) {
                $announcementGroup->addAnnouncementGroupSession($announcementGroupSession);
            }
        }

        if (null !== $classroomvirtuals) {
            foreach ($classroomvirtuals as $classroomvirtual) {
                $announcementGroup->addClassroomvirtual($classroomvirtual);
            }
        }

        if (null !== $taskCourseGroups) {
            foreach ($taskCourseGroups as $taskCourseGroup) {
                $announcementGroup->addTaskCourseGroup($taskCourseGroup);
            }
        }

        if (null !== $announcementNotificationGroups) {
            foreach ($announcementNotificationGroups as $announcementNotificationGroup) {
                $announcementGroup->addAnnouncementNotificationGroup($announcementNotificationGroup);
            }
        }

        return $announcementGroup;
    }
}
