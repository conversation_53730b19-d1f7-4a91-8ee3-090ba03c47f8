<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Course;
use App\Entity\Season;

class SeasonMother
{
    public const string DEFAULT_NAME = 'Season 1';

    public static function create(
        ?int $id = null,
        ?string $name = null,
        int $sort = 1,
        ?string $type = null,
        ?Course $course = null,
    ): Season {
        $season = new Season();
        if (null !== $id) {
            $season->setId($id);
        }
        $season->setName($name ?? self::DEFAULT_NAME);
        $season->setSort($sort);
        $season->setType($type ?? Season::TYPE_SEQUENTIAL);
        $season->setCourse($course);

        return $season;
    }
}
