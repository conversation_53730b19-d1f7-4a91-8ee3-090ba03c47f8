<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Filter;
use App\Entity\User;

class UserMother
{
    public const string DEFAULT_FIRST_NAME = 'Jon';
    public const string DEFAULT_LAST_NAME = 'Doe';
    public const string DEFAULT_EMAIL = '<EMAIL>';
    public const string DEFAULT_PASSWORD = '12345678';

    /**
     * @param Filter[] $filtersForManage
     */
    public static function create(
        ?int $id = null,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $email = null,
        ?string $password = null,
        bool $isActive = true,
        bool $validated = true,
        bool $open = true,
        bool $starTeam = false,
        array $roles = [],
        array $remoteRoles = [],
        ?User $createdBy = null,
        ?array $filtersForManage = null,
    ): User {
        $user = new User();

        if (null !== $id) {
            $user->setId($id);
        }
        $user->setFirstName($firstName ?? self::DEFAULT_FIRST_NAME);
        $user->setLastName($lastName ?? self::DEFAULT_LAST_NAME);
        $user->setEmail($email ?? self::DEFAULT_EMAIL);
        $user->setPassword($password ?? self::DEFAULT_PASSWORD);
        $user->setIsActive($isActive);
        $user->setValidated($validated);
        $user->setOpen($open);
        $user->setStarTeam($starTeam);
        $user->setRoles($roles);
        $user->setRemoteRoles($remoteRoles);

        if (null !== $createdBy) {
            $user->setCreatedBy($createdBy);
        }

        if (null !== $filtersForManage) {
            foreach ($filtersForManage as $filter) {
                $user->addManagerFilter($filter);
            }
        }

        return $user;
    }
}
