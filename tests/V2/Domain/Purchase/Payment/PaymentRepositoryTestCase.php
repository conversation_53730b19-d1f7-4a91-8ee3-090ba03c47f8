<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase\Payment;

use App\Tests\V2\Mother\Purchase\Payment\PaymentMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\PaymentCollection;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class PaymentRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): PaymentRepository;

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws \Exception
     */
    public function testPut(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1),
            amount: MoneyMother::create(amount: 1000)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2),
            amount: MoneyMother::create(amount: 2000)
        );
        $payment3 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(3),
            amount: MoneyMother::create(amount: 3000)
        );

        $repository = $this->getRepository();

        $repository->put($payment1);
        $repository->put($payment2);
        $repository->put($payment3);

        $found = $repository->findOneBy(
            PaymentCriteria::createById($payment1->getId())
        );
        $this->assertEquals($payment1, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createById($payment2->getId())
        );
        $this->assertEquals($payment2, $found);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws \Exception
     */
    public function testFindOneBy(): void
    {
        $purchaseId1 = UuidMother::create();
        $purchaseId2 = UuidMother::create();
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);
        $amount1 = MoneyMother::create(amount: 1000);
        $amount2 = MoneyMother::create(amount: 2000);

        $payment1 = PaymentMother::create(
            purchaseId: $purchaseId1,
            userId: $userId1,
            amount: $amount1,
            status: PaymentStatus::Pending
        );
        $payment2 = PaymentMother::create(
            purchaseId: $purchaseId2,
            userId: $userId2,
            amount: $amount2,
            status: PaymentStatus::Paid
        );
        $payment3 = PaymentMother::create(
            purchaseId: $purchaseId1,
            userId: $userId1,
            amount: $amount1,
            status: PaymentStatus::Failed
        );

        $repository = $this->getRepository();

        $repository->put($payment1);
        $repository->put($payment2);
        $repository->put($payment3);

        $this->assertEquals(
            $payment2,
            $repository->findOneBy(
                PaymentCriteria::createEmpty()
                    ->filterById($payment2->getId())
            )
        );

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByPurchaseId($purchaseId2)
        );
        $this->assertEquals($payment2, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByUserId($userId2)
        );
        $this->assertEquals($payment2, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByStatus(PaymentStatus::Paid)
        );
        $this->assertEquals($payment2, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByMinAmount($amount2)
        );
        $this->assertEquals($payment2, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByMaxAmount($amount1)
        );
        $this->assertTrue(
            $found->getId()->equals($payment1->getId())
            || $found->getId()->equals($payment3->getId()),
            'Found payment should be either payment1 or payment3'
        );

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterByPaymentIntent($payment1->getPaymentIntent())
        );
        $this->assertEquals($payment1, $found);
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testFindOneByThrowsExceptionWhenNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(PaymentNotFoundException::class);
        $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterById(UuidMother::create())
        );
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        PaymentCollection $input,
        PaymentCriteria $criteria,
        int $expectedCount,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $payment) {
            $repository->put($payment);
        }

        // First, verify that all payments were stored correctly
        $allPayments = $repository->findBy(PaymentCriteria::createEmpty());
        $this->assertCount($input->count(), $allPayments);

        // Then test the specific criteria
        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        $expectedResultsIds = array_map(
            fn ($payment) => $payment->getId(),
            $expectedResults
        );

        $foundIds = array_map(
            fn ($payment) => $payment->getId(),
            $result->all()
        );

        $this->assertCount(
            0,
            array_diff($expectedResultsIds, $foundIds),
            'Expected results not found in results'
        );
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function provideFindBy(): \Generator
    {
        $purchaseId1 = UuidMother::create();
        $purchaseId2 = UuidMother::create();
        $purchaseId3 = UuidMother::create();
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);
        $userId3 = IdMother::create(3);
        $amount1 = MoneyMother::create(amount: 1000);
        $amount2 = MoneyMother::create(amount: 2000);
        $amount3 = MoneyMother::create(amount: 3000);

        $payment1 = PaymentMother::create(
            purchaseId: $purchaseId1,
            userId: $userId1,
            amount: $amount1,
            status: PaymentStatus::Pending
        );
        $payment2 = PaymentMother::create(
            purchaseId: $purchaseId2,
            userId: $userId2,
            amount: $amount2,
            status: PaymentStatus::Paid
        );
        $payment3 = PaymentMother::create(
            purchaseId: $purchaseId3,
            userId: $userId3,
            amount: $amount3,
            status: PaymentStatus::Failed
        );

        yield '3 payments get all' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$payment1, $payment2, $payment3],
        ];

        yield '3 payments get payment 2 by id' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterById($payment2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$payment2],
        ];

        yield '3 payments get by purchase id 1' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByPurchaseId($purchaseId1),
            'expectedCount' => 1,
            'expectedResults' => [$payment1],
        ];

        yield '3 payments get by user id 2' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByUserId($userId2),
            'expectedCount' => 1,
            'expectedResults' => [$payment2],
        ];

        yield '3 payments get by payment intent' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByPaymentIntent($payment1->getPaymentIntent()),
            'expectedCount' => 1,
            'expectedResults' => [$payment1],
        ];

        yield '3 payments get by status paid' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByStatus(PaymentStatus::Paid),
            'expectedCount' => 1,
            'expectedResults' => [$payment2],
        ];

        yield '3 payments get by min amount 2000' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMinAmount($amount2),
            'expectedCount' => 2,
            'expectedResults' => [$payment2, $payment3],
        ];

        yield '3 payments get by max amount 2000' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMaxAmount($amount2),
            'expectedCount' => 2,
            'expectedResults' => [$payment1, $payment2],
        ];

        yield '3 payments get by amount range 1500-2500' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1500))
                ->filterByMaxAmount(MoneyMother::create(amount: 2500)),
            'expectedCount' => 1,
            'expectedResults' => [$payment2],
        ];

        $startDate = new \DateTimeImmutable('2023-01-01');
        $endDate = new \DateTimeImmutable('2023-12-31');
        $paymentWithDate = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(10),
            amount: MoneyMother::create(amount: 5000),
            status: PaymentStatus::Paid,
            createdAt: new \DateTimeImmutable('2023-06-15')
        );

        yield '1 payment get by date range' => [
            'input' => new PaymentCollection([$paymentWithDate]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByStartDate($startDate)
                ->filterByEndDate($endDate),
            'expectedCount' => 1,
            'expectedResults' => [$paymentWithDate],
        ];
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws \Exception
     */
    public function testDelete(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2)
        );
        $payment3 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(3)
        );

        $repository = $this->getRepository();

        $repository->put($payment1);
        $repository->put($payment2);
        $repository->put($payment3);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterById($payment2->getId())
        );
        $this->assertEquals($payment2, $found);

        $repository->delete($payment2);

        $this->expectException(PaymentNotFoundException::class);
        $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterById($payment2->getId())
        );
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws \Exception
     */
    public function testDeleteDoesNotAffectOtherPayments(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2)
        );
        $payment3 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(3)
        );

        $repository = $this->getRepository();

        $repository->put($payment1);
        $repository->put($payment2);
        $repository->put($payment3);

        $repository->delete($payment2);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterById($payment1->getId())
        );
        $this->assertEquals($payment1, $found);

        $found = $repository->findOneBy(
            PaymentCriteria::createEmpty()
                ->filterById($payment3->getId())
        );
        $this->assertEquals($payment3, $found);
    }

    public function testCountByWithEmptyRepository(): void
    {
        $repository = $this->getRepository();

        $count = $repository->countBy(PaymentCriteria::createEmpty());

        $this->assertSame(0, $count, 'Empty repository should return count of 0');
        $this->assertIsInt($count, 'countBy should return an integer');
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testCountByBasicFunctionality(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2)
        );
        $payment3 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(3)
        );

        $repository = $this->getRepository();
        $repository->put($payment1);
        $repository->put($payment2);
        $repository->put($payment3);

        $totalCount = $repository->countBy(PaymentCriteria::createEmpty());
        $this->assertSame(3, $totalCount, 'Should count all payments');

        $countById = $repository->countBy(
            PaymentCriteria::createById($payment2->getId())
        );
        $this->assertSame(1, $countById, 'Should count single payment by ID');
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public function testCountByConsistencyWithFindBy(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1),
            amount: MoneyMother::create(amount: 1000)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2),
            amount: MoneyMother::create(amount: 2000)
        );

        $repository = $this->getRepository();
        $repository->put($payment1);
        $repository->put($payment2);

        $criteria = PaymentCriteria::createEmpty()
            ->filterByMinAmount(MoneyMother::create(amount: 1500));

        $foundPayments = $repository->findBy($criteria);
        $countResult = $repository->countBy($criteria);

        $this->assertSame(
            $foundPayments->count(),
            $countResult,
            'countBy result should match findBy collection count'
        );
    }

    /**
     * @throws PaymentRepositoryException
     */
    #[DataProvider('provideCountByCriteria')]
    public function testCountByWithVariousCriteria(
        PaymentCollection $input,
        PaymentCriteria $criteria,
        int $expectedCount,
        string $testDescription
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $payment) {
            $repository->put($payment);
        }

        $actualCount = $repository->countBy($criteria);

        $this->assertSame(
            $expectedCount,
            $actualCount,
            $testDescription
        );
        $this->assertIsInt($actualCount, 'countBy should always return an integer');
        $this->assertGreaterThanOrEqual(0, $actualCount, 'Count should never be negative');
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function provideCountByCriteria(): \Generator
    {
        $purchaseId1 = UuidMother::create();
        $purchaseId2 = UuidMother::create();
        $purchaseId3 = UuidMother::create();
        $userId1 = IdMother::create(1);
        $userId2 = IdMother::create(2);
        $userId3 = IdMother::create(3);
        $amount1 = MoneyMother::create(amount: 1000);
        $amount2 = MoneyMother::create(amount: 2000);
        $amount3 = MoneyMother::create(amount: 3000);

        $payment1 = PaymentMother::create(
            purchaseId: $purchaseId1,
            userId: $userId1,
            amount: $amount1,
            status: PaymentStatus::Pending
        );
        $payment2 = PaymentMother::create(
            purchaseId: $purchaseId2,
            userId: $userId2,
            amount: $amount2,
            status: PaymentStatus::Paid
        );
        $payment3 = PaymentMother::create(
            purchaseId: $purchaseId3,
            userId: $userId3,
            amount: $amount3,
            status: PaymentStatus::Failed
        );

        yield 'count all payments' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty(),
            'expectedCount' => 3,
            'testDescription' => 'Should count all payments with empty criteria',
        ];

        yield 'count by specific ID' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createById($payment2->getId()),
            'expectedCount' => 1,
            'testDescription' => 'Should count single payment by ID',
        ];

        yield 'count by purchase ID' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByPurchaseId($purchaseId1),
            'expectedCount' => 1,
            'testDescription' => 'Should count payments by purchase ID',
        ];

        yield 'count by user ID' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByUserId($userId2),
            'expectedCount' => 1,
            'testDescription' => 'Should count payments by user ID',
        ];

        yield 'count by payment intent' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByPaymentIntent($payment1->getPaymentIntent()),
            'expectedCount' => 1,
            'testDescription' => 'Should count payments by payment intent',
        ];

        yield 'count by status' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByStatus(PaymentStatus::Paid),
            'expectedCount' => 1,
            'testDescription' => 'Should count payments by status',
        ];

        yield 'count by min amount' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMinAmount($amount2),
            'expectedCount' => 2,
            'testDescription' => 'Should count payments with amount >= 2000',
        ];

        yield 'count by max amount' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMaxAmount($amount2),
            'expectedCount' => 2,
            'testDescription' => 'Should count payments with amount <= 2000',
        ];

        yield 'count by amount range' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 1500))
                ->filterByMaxAmount(MoneyMother::create(amount: 2500)),
            'expectedCount' => 1,
            'testDescription' => 'Should count payments in amount range 1500-2500',
        ];

        yield 'count with no matching criteria' => [
            'input' => new PaymentCollection([$payment1, $payment2, $payment3]),
            'criteria' => PaymentCriteria::createEmpty()
                ->filterByMinAmount(MoneyMother::create(amount: 5000)),
            'expectedCount' => 0,
            'testDescription' => 'Should return 0 when no payments match criteria',
        ];
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testCountByAfterPaymentDeletion(): void
    {
        $payment1 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(1)
        );
        $payment2 = PaymentMother::create(
            purchaseId: UuidMother::create(),
            userId: IdMother::create(2)
        );

        $repository = $this->getRepository();
        $repository->put($payment1);
        $repository->put($payment2);

        $countBefore = $repository->countBy(PaymentCriteria::createEmpty());
        $this->assertSame(2, $countBefore, 'Should count 2 payments before deletion');

        $repository->delete($payment1);

        $countAfter = $repository->countBy(PaymentCriteria::createEmpty());
        $this->assertSame(1, $countAfter, 'Should count 1 payment after deletion');

        $countDeleted = $repository->countBy(
            PaymentCriteria::createById($payment1->getId())
        );
        $this->assertSame(0, $countDeleted, 'Deleted payment should not be counted');
    }
}
