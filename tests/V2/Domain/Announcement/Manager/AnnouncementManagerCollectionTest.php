<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Announcement\Manager;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\V2\Domain\Announcement\Manager\AnnouncementManager;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class AnnouncementManagerCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new AnnouncementManagerCollection($items);
    }

    protected function getExpectedType(): string
    {
        return AnnouncementManager::class;
    }

    protected function getItem(): object
    {
        return AnnouncementManagerMother::create();
    }
}
