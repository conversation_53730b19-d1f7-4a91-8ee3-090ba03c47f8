<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Criteria\SortDirection;
use PHPUnit\Framework\TestCase;

class SortDirectionTest extends TestCase
{
    /**
     * Test that the enum has the expected cases.
     */
    public function testEnumCases(): void
    {
        // Assert
        $this->assertSame('ASC', SortDirection::ASC->name);
        $this->assertSame('DESC', SortDirection::DESC->name);

        // Verify that there are only two cases
        $cases = SortDirection::cases();
        $this->assertCount(2, $cases);
        $this->assertContains(SortDirection::ASC, $cases);
        $this->assertContains(SortDirection::DESC, $cases);
    }
}
