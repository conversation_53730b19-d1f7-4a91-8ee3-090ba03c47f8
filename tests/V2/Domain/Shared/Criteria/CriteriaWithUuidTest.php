<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class CriteriaWithUuidTest extends CriteriaIdTestCase
{
    protected function getCriteriaClass(): CriteriaId
    {
        return new class extends CriteriaWithUuid {
        };
    }

    protected function createId(): Identifier
    {
        return UuidMother::create();
    }

    protected function getIds(array $ids): Collection
    {
        return new UuidCollection($ids);
    }
}
