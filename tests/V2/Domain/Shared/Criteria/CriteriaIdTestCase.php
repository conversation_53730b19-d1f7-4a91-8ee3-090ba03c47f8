<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use App\V2\Domain\Shared\Identifier;
use PHPUnit\Framework\TestCase;

abstract class CriteriaIdTestCase extends TestCase
{
    protected CriteriaId $testCriteriaClass;

    abstract protected function getCriteriaClass(): CriteriaId;

    abstract protected function createId(): Identifier;

    abstract protected function getIds(array $ids): Collection;

    protected function setUp(): void
    {
        $this->testCriteriaClass = $this->getCriteriaClass();
    }

    public function testCreateById(): void
    {
        $id = $this->createId();

        $criteria1 = $this->testCriteriaClass::createById($id);
        $criteria2 = $this->testCriteriaClass::createEmpty()->filterById($id);

        $this->assertEquals($criteria2->getId(), $criteria1->getId());
        $this->assertSame($id, $criteria1->getId());
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testCreateByIds(): void
    {
        $ids = $this->getIds([$this->createId(), $this->createId()]);

        $criteria1 = $this->testCriteriaClass::createByIds($ids);
        $criteria2 = $this->testCriteriaClass::createEmpty()->filterByIds($ids);

        $this->assertEquals($criteria2->getIds(), $criteria1->getIds());
        $this->assertSame($ids, $criteria1->getIds());
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testCreateByIdsThrowsExceptionWithEmptyCollection(): void
    {
        $this->expectException(CriteriaException::class);

        $emptyIds = $this->getIds([]);
        $this->testCriteriaClass::createByIds($emptyIds);
    }

    public function testFilterByIdOverwritesPreviousId(): void
    {
        $firstId = $this->createId();
        $secondId = $this->createId();
        $this->assertNotEquals($firstId, $secondId);

        $criteria = $this->testCriteriaClass::createEmpty()
            ->filterById($firstId)
            ->filterById($secondId);

        $this->assertSame($secondId, $criteria->getId());
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testFilterByIdsOverwritesPreviousIds(): void
    {
        $firstIds = $this->getIds([$this->createId()]);
        $secondIds = $this->getIds([$this->createId(), $this->createId()]);

        $this->assertNotEquals($firstIds, $secondIds);

        $criteria = $this->testCriteriaClass::createEmpty()
            ->filterByIds($firstIds)
            ->filterByIds($secondIds);

        $this->assertSame($secondIds, $criteria->getIds());
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testFilterByEmptyIdsThrowsException(): void
    {
        $this->expectException(CriteriaException::class);

        $emptyIds = $this->getIds([]);
        $this->testCriteriaClass::createEmpty()->filterByIds($emptyIds);
    }

    public function testEmptyCriteriaHasNoIdOrIds(): void
    {
        $criteria = $this->testCriteriaClass::createEmpty();

        $this->assertNull($criteria->getId());
        $this->assertNull($criteria->getIds());
    }
}
