<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Subscription;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\Tests\V2\Mother\Subscription\SubscriptionMother;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Exception\SubscriptionRepositoryException;
use App\V2\Domain\Subscription\SubscriptionCollection;
use App\V2\Domain\Subscription\SubscriptionCriteria;
use App\V2\Domain\Subscription\SubscriptionRepository;
use PHPUnit\Framework\TestCase;

abstract class SubscriptionRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): SubscriptionRepository;

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws SubscriptionNotFoundException
     * @throws SubscriptionRepositoryException
     */
    public function testPutAndFindById(): void
    {
        $subscription = SubscriptionMother::create();
        $repository = $this->getRepository();

        $repository->put($subscription);

        $found = $repository->findOneBy(
            SubscriptionCriteria::createById($subscription->getId())
        );

        $this->assertEquals($subscription, $found);
    }

    /**
     * @throws InvalidUuidException
     * @throws SubscriptionNotFoundException
     * @throws SubscriptionRepositoryException
     */
    public function testFindByName(): void
    {
        $subscription = SubscriptionMother::create(name: 'Premium Plan');
        $repository = $this->getRepository();

        $repository->put($subscription);

        $found = $repository->findOneBy(
            SubscriptionCriteria::createEmpty()->filterByName('Premium Plan')
        );

        $this->assertEquals($subscription, $found);
    }

    /**
     * @throws InvalidUuidException
     * @throws SubscriptionRepositoryException
     */
    public function testFindBySearch(): void
    {
        $subscription1 = SubscriptionMother::create(name: 'Premium Plan', description: 'Full access');
        $subscription2 = SubscriptionMother::create(name: 'Basic Plan', description: 'Limited access');
        $repository = $this->getRepository();

        $repository->put($subscription1);
        $repository->put($subscription2);

        $found = $repository->findBy(
            SubscriptionCriteria::createEmpty()->filterBySearch('Premium')
        );

        $this->assertCount(1, $found);
        $this->assertEquals($subscription1, $found->first());
    }

    /**
     * @throws InvalidUuidException
     * @throws SubscriptionRepositoryException
     */
    public function testFindBySearchInDescription(): void
    {
        $subscription1 = SubscriptionMother::create(name: 'Plan A', description: 'Premium features');
        $subscription2 = SubscriptionMother::create(name: 'Plan B', description: 'Basic features');
        $repository = $this->getRepository();

        $repository->put($subscription1);
        $repository->put($subscription2);

        $found = $repository->findBy(
            SubscriptionCriteria::createEmpty()->filterBySearch('Premium')
        );

        $this->assertCount(1, $found);
        $this->assertEquals($subscription1, $found->first());
    }

    /**
     * @throws InvalidUuidException
     * @throws SubscriptionRepositoryException
     */
    public function testFindAll(): void
    {
        $subscription1 = SubscriptionMother::create();
        $subscription2 = SubscriptionMother::create();
        $repository = $this->getRepository();

        $repository->put($subscription1);
        $repository->put($subscription2);

        $found = $repository->findBy(SubscriptionCriteria::createEmpty());

        $this->assertCount(2, $found);
        $this->assertInstanceOf(SubscriptionCollection::class, $found);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws SubscriptionNotFoundException
     * @throws SubscriptionRepositoryException
     */
    public function testDelete(): void
    {
        $subscription = SubscriptionMother::create();
        $repository = $this->getRepository();

        $repository->put($subscription);
        $repository->delete($subscription);

        $this->expectException(SubscriptionNotFoundException::class);
        $repository->findOneBy(SubscriptionCriteria::createById($subscription->getId()));
    }

    /**
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws SubscriptionRepositoryException
     */
    public function testFindByIdNotFound(): void
    {
        $repository = $this->getRepository();

        $this->expectException(SubscriptionNotFoundException::class);
        $repository->findOneBy(SubscriptionCriteria::createById(UuidMother::create()));
    }
}
