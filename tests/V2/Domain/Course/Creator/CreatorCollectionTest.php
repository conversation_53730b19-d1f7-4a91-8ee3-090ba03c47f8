<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Course\Creator;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\Course\Creator\CreatorMother;
use App\V2\Domain\Course\Creator\Creator;
use App\V2\Domain\Course\Creator\CreatorCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class CreatorCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new CreatorCollection($items);
    }

    protected function getExpectedType(): string
    {
        return Creator::class;
    }

    protected function getItem(): object
    {
        return CreatorMother::create();
    }
}
