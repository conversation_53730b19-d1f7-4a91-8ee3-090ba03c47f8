<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\LTI\LtiToolCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;

class LtiToolCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new LtiToolCollection($items);
    }

    protected function getExpectedType(): string
    {
        return LtiTool::class;
    }

    /**
     * @throws InvalidUuidException
     */
    protected function getItem(): object
    {
        return LtiToolMother::create();
    }
}
