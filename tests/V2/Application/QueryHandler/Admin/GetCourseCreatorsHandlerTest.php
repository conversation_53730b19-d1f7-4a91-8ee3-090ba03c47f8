<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Repository\CourseRepository as LegacyCourseRepository;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\Tests\V2\Mother\Course\Creator\CreatorMother;
use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection;
use App\V2\Application\Query\Admin\GetCourseCreators;
use App\V2\Application\QueryHandler\Admin\GetCourseCreatorsHandler;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorHydrationCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Course\Creator\Exceptions\GetCourseCreatorsException;
use App\V2\Domain\Course\Exceptions\CourseNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class GetCourseCreatorsHandlerTest extends TestCase
{
    private function getHandler(
        ?CourseCreatorRepository $courseCreatorRepository = null,
        ?LegacyCourseRepository $legacyCourseRepository = null,
        ?CourseCreatorHydratorCollection $hydratorCollection = null,
    ): GetCourseCreatorsHandler {
        return new GetCourseCreatorsHandler(
            courseCreatorRepository: $courseCreatorRepository ?? $this->createMock(CourseCreatorRepository::class),
            legacyCourseRepository: $legacyCourseRepository ?? $this->createMock(LegacyCourseRepository::class),
            hydratorCollection: $hydratorCollection ?? $this->createMock(CourseCreatorHydratorCollection::class),
        );
    }

    /**
     * @throws GetCourseCreatorsException
     * @throws CourseNotFoundException
     * @throws InfrastructureException
     * @throws Exception
     */
    #[DataProvider('provideExceptions')]
    public function testExceptions(
        bool $courseNotFoundException,
        bool $hydratorException,
        \Exception $expectedException,
    ): void {
        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $courseCreatorRepository->method('findBy')
            ->willReturn(new CourseCreatorCollection([]));

        $legacyCourseRepository = $this->createMock(LegacyCourseRepository::class);
        $legacyCourseRepository->method('findOneBy')
            ->willReturn($courseNotFoundException ? null : CourseMother::create(id: 1));

        $hydratorCollection = $this->createMock(CourseCreatorHydratorCollection::class);
        $hydratorCollection->method('hydrate')
            ->willReturnCallback(function () use ($hydratorException) {
                if ($hydratorException) {
                    throw new HydratorException();
                }
            });

        $query = new GetCourseCreators(
            criteria: CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(new Id(1)),
            withCreators: true,
        );

        $handler = $this->getHandler(
            courseCreatorRepository: $courseCreatorRepository,
            legacyCourseRepository: $legacyCourseRepository,
            hydratorCollection: $hydratorCollection,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle($query);
    }

    public static function provideExceptions(): \Generator
    {
        yield 'course not found' => [
            'courseNotFoundException' => true,
            'hydratorException' => false,
            'expectedException' => new CourseNotFoundException(),
        ];

        yield 'hydrator exception' => [
            'courseNotFoundException' => false,
            'hydratorException' => true,
            'expectedException' => GetCourseCreatorsException::fromPrevious(new HydratorException()),
        ];
    }

    /**
     * @throws CollectionException
     * @throws CourseNotFoundException
     * @throws Exception
     * @throws GetCourseCreatorsException
     * @throws InfrastructureException
     */
    public function testHandleWithCreators(): void
    {
        $creator1 = CreatorMother::create(
            id: new Id(1),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'creator1',
            lastName: 'test',
        );

        $creator2 = CreatorMother::create(
            id: new Id(2),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'creator2',
            lastName: 'test',
        );

        $creator3 = CreatorMother::create(
            id: new Id(3),
            email: EmailMother::create(email: '<EMAIL>'),
            firstName: 'creator3',
            lastName: 'test',
        );

        $hydratorCollection = $this->createMock(CourseCreatorHydratorCollection::class);
        $hydratorCollection->expects($this->once())
            ->method('hydrate')
            ->willReturnCallback(function (
                CourseCreatorCollection $collection,
                CourseCreatorHydrationCriteria $criteria
            ) use (
                $creator1,
                $creator2,
                $creator3
            ) {
                foreach ($collection->all() as $courseCreator) {
                    $courseCreator->setCreator(
                        match ($courseCreator->getUserId()->value()) {
                            $creator1->getId()->value() => $creator1,
                            $creator2->getId()->value() => $creator2,
                            $creator3->getId()->value() => $creator3,
                            default => $this->fail('Unknown course creator'),
                        }
                    );
                }
            });

        $courseCreator1 = CourseCreatorMother::create(userId: new Id(1), courseId: new Id(1));
        $courseCreator2 = CourseCreatorMother::create(userId: new Id(2), courseId: new Id(1));
        $courseCreator3 = CourseCreatorMother::create(userId: new Id(3), courseId: new Id(1));

        $legacyCourseRepository = $this->createMock(LegacyCourseRepository::class);
        $legacyCourseRepository->method('findOneBy')
            ->willReturn(CourseMother::create(id: 1));

        $courseCreatorRepository = $this->createMock(CourseCreatorRepository::class);
        $courseCreatorRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseCreatorCollection([$courseCreator1, $courseCreator2, $courseCreator3]));

        $query = new GetCourseCreators(
            criteria: CourseCreatorCriteria::createEmpty()
                ->filterByCourseId(new Id(1)),
            withCreators: true,
        );

        $handler = $this->getHandler(
            courseCreatorRepository: $courseCreatorRepository,
            legacyCourseRepository: $legacyCourseRepository,
            hydratorCollection: $hydratorCollection,
        );

        $result = $handler->handle($query);
        $this->assertCount(3, $result);

        $this->assertNotNull($courseCreator1->getCreator());
        $this->assertEquals($creator1, $courseCreator1->getCreator());

        $this->assertNotNull($courseCreator2->getCreator());
        $this->assertEquals($creator2, $courseCreator2->getCreator());

        $this->assertNotNull($courseCreator3->getCreator());
        $this->assertEquals($creator3, $courseCreator3->getCreator());
    }
}
