<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Application\Query\Admin\GetLtiRegistrations;
use App\V2\Application\QueryHandler\Admin\GetLtiRegistrationsHandler;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use PHPUnit\Framework\TestCase;

class GetLtiRegistrationsHandlerTest extends TestCase
{
    public function testHandle(): void
    {
        $repository = $this->createMock(LtiRegistrationRepository::class);

        $registration1 = LtiRegistrationMother::create(name: 'registration-1', clientId: 'registration-1');
        $registration2 = LtiRegistrationMother::create(name: 'registration-2', clientId: 'registration-2');
        $registration3 = LtiRegistrationMother::create(name: 'registration-3', clientId: 'registration-3');

        $repository->expects($this->once())
            ->method('findBy')
            ->willReturn(new LtiRegistrationCollection([$registration1, $registration2, $registration3]));

        $handler = new GetLtiRegistrationsHandler(
            ltiRegistrationRepository: $repository
        );

        $result = $handler->handle(
            new GetLtiRegistrations(
                criteria: LtiRegistrationCriteria::createEmpty()->filterBySearchString('registration')
            )
        );
        $this->assertCount(3, $result);
    }
}
