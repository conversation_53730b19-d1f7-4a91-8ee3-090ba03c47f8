<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Billing\BillingDataMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\V2\Application\Query\GetBillingData;
use App\V2\Application\QueryHandler\GetBillingDataQueryHandler;
use App\V2\Domain\Billing\Exception\GetBillingDataQueryHandlerException;
use App\V2\Domain\Billing\Exception\BillingDataNotFoundException;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Billing\BillingDataCriteria;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetBillingDataQueryHandlerTest extends TestCase
{
    private BillingDataRepository|MockObject $billingDataRepository;
    private GetBillingDataQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->billingDataRepository = $this->createMock(BillingDataRepository::class);
        $this->handler = new GetBillingDataQueryHandler(
            billingDataRepository: $this->billingDataRepository,
        );
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testHandleSuccessfullyAsAdmin(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $adminUser = UserMother::create(
            id: 456,
            roles: [User::ROLE_ADMIN]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $adminUser,
        );

        $expectedBillingData = BillingDataMother::create(
            userId: $userId
        );

        $expectedCriteria = BillingDataCriteria::createEmpty()
            ->filterByUserId($userId);

        // Expectations
        $this->billingDataRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (BillingDataCriteria $criteria) use ($expectedCriteria): bool {
                return $criteria->getUserId()?->equals($expectedCriteria->getUserId()) ?? false;
            }))
            ->willReturn($expectedBillingData);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertSame($expectedBillingData, $result);
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testHandleSuccessfullyAsOwner(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $ownerUser = UserMother::create(
            id: 123,
            roles: [User::ROLE_USER]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $ownerUser,
        );

        $expectedBillingData = BillingDataMother::create(
            userId: $userId
        );

        $expectedCriteria = BillingDataCriteria::createEmpty()
            ->filterByUserId($userId);

        // Expectations
        $this->billingDataRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (BillingDataCriteria $criteria) use ($expectedCriteria): bool {
                return $criteria->getUserId()?->equals($expectedCriteria->getUserId()) ?? false;
            }))
            ->willReturn($expectedBillingData);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertSame($expectedBillingData, $result);
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testHandleSuccessfullyAsSuperAdmin(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $superAdminUser = UserMother::create(
            id: 789,
            roles: [User::ROLE_SUPER_ADMIN]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $superAdminUser,
        );

        $expectedBillingData = BillingDataMother::create(
            userId: $userId
        );

        // Expectations
        $this->billingDataRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($expectedBillingData);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertSame($expectedBillingData, $result);
    }

    /**
     * @throws \Exception
     */
    public function testHandleThrowsExceptionWhenUserNotAllowed(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $unauthorizedUser = UserMother::create(
            id: 456,
            roles: [User::ROLE_USER]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $unauthorizedUser,
        );

        // Expectations
        $this->billingDataRepository
            ->expects($this->never())
            ->method('findOneBy');

        // Act & Assert
        $this->expectException(GetBillingDataQueryHandlerException::class);
        $this->expectExceptionMessage('User not allowed to access this data');

        $this->handler->handle($query);
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testHandleReturnsNullWhenBillingDataNotFound(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $adminUser = UserMother::create(
            id: 456,
            roles: [User::ROLE_ADMIN]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $adminUser,
        );

        // Expectations
        $this->billingDataRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn(null);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertNull($result);
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataNotFoundException
     * @throws \Exception
     */
    public function testHandleThrowsBillingDataRepositoryException(): void
    {
        // Arrange
        $userId = IdMother::create(123);
        $adminUser = UserMother::create(
            id: 456,
            roles: [User::ROLE_ADMIN]
        );

        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $adminUser,
        );

        $repositoryException = new BillingDataRepositoryException('Database connection failed');

        // Expectations
        $this->billingDataRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willThrowException($repositoryException);

        // Act & Assert
        $this->expectException(BillingDataRepositoryException::class);
        $this->expectExceptionMessage('Database connection failed');

        $this->handler->handle($query);
    }

    /**
     * @throws GetBillingDataQueryHandlerException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    #[DataProvider('authorizationScenariosProvider')]
    public function testAuthorizationScenarios(
        User $requestedBy,
        Id $userId,
        bool $shouldBeAllowed,
        ?string $expectedExceptionMessage = null
    ): void {
        // Arrange
        $query = new GetBillingData(
            userId: $userId,
            requestedBy: $requestedBy,
        );

        if ($shouldBeAllowed) {
            $expectedBillingData = BillingDataMother::create(userId: $userId);
            $this->billingDataRepository
                ->expects($this->once())
                ->method('findOneBy')
                ->willReturn($expectedBillingData);

            // Act
            $result = $this->handler->handle($query);

            // Assert
            $this->assertSame($expectedBillingData, $result);
        } else {
            $this->billingDataRepository
                ->expects($this->never())
                ->method('findOneBy');

            // Act & Assert
            $this->expectException(GetBillingDataQueryHandlerException::class);
            if ($expectedExceptionMessage) {
                $this->expectExceptionMessage($expectedExceptionMessage);
            }

            $this->handler->handle($query);
        }
    }

    /**
     * @throws \Exception
     */
    public static function authorizationScenariosProvider(): \Generator
    {
        $userId = IdMother::create(123);

        yield 'Admin user accessing any user data' => [
            'requestedBy' => UserMother::create(id: 456, roles: [User::ROLE_ADMIN]),
            'userId' => $userId,
            'shouldBeAllowed' => true,
        ];

        yield 'Super admin user accessing any user data' => [
            'requestedBy' => UserMother::create(id: 789, roles: [User::ROLE_SUPER_ADMIN]),
            'userId' => $userId,
            'shouldBeAllowed' => true,
        ];

        yield 'User accessing own data' => [
            'requestedBy' => UserMother::create(id: 123, roles: [User::ROLE_USER]),
            'userId' => $userId,
            'shouldBeAllowed' => true,
        ];

        yield 'Regular user accessing other user data' => [
            'requestedBy' => UserMother::create(id: 456, roles: [User::ROLE_USER]),
            'userId' => $userId,
            'shouldBeAllowed' => false,
            'expectedExceptionMessage' => 'User not allowed to access this data',
        ];

        yield 'Manager user accessing other user data' => [
            'requestedBy' => UserMother::create(id: 456, roles: [User::ROLE_MANAGER]),
            'userId' => $userId,
            'shouldBeAllowed' => false,
            'expectedExceptionMessage' => 'User not allowed to access this data',
        ];
    }
}
