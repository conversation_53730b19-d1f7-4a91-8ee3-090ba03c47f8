<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Purchase\PurchasableFactory;

use App\Tests\Mother\Entity\CourseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\InvalidPurchasableItemException;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Application\Purchase\PurchasableFactory\CoursePurchasableItemFactory;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class CoursePurchasableItemFactoryTest extends TestCase
{
    private CoursePurchasableItemFactory $factory;
    private UuidGenerator $uuidGenerator;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->uuidGenerator = $this->createMock(UuidGenerator::class);
        $this->factory = new CoursePurchasableItemFactory($this->uuidGenerator);
    }

    public function testSupportsReturnsTrueForCourseType(): void
    {
        $this->assertTrue($this->factory->supports(ResourceType::Course));
    }

    public function testSupportsReturnsFalseForOtherTypes(): void
    {
        $this->assertFalse($this->factory->supports(ResourceType::Subscription));
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateWithValidCourse(): void
    {
        $courseId = 123;
        $courseName = 'Test Course';
        $courseDescription = 'Test Description';

        $course = CourseMother::create(
            id: $courseId,
            name: $courseName,
            description: $courseDescription
        );

        $price = MoneyMother::create(amount: 5000); // 50.00 EUR
        $expectedUuid = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->once())
            ->method('generate')
            ->willReturn($expectedUuid);

        $purchasableItem = $this->factory->create($course, $price);

        $this->assertEquals($expectedUuid, $purchasableItem->getId());
        $this->assertSame($courseName, $purchasableItem->getName());
        $this->assertSame($courseDescription, $purchasableItem->getDescription());
        $this->assertEquals($price, $purchasableItem->getPrice());

        $resource = $purchasableItem->getResource();
        $this->assertEquals(ResourceType::Course, $resource->getType());
        $this->assertEquals(new Id($courseId), $resource->getId());

        $this->assertInstanceOf(\DateTimeImmutable::class, $purchasableItem->getCreatedAt());
        $this->assertNull($purchasableItem->getUpdatedAt());
        $this->assertNull($purchasableItem->getDeletedAt());
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateWithCourseWithNullDescription(): void
    {
        $course = CourseMother::create(
            id: 456,
            name: 'Course without description',
            description: null
        );

        $price = MoneyMother::create();
        $expectedUuid = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->once())
            ->method('generate')
            ->willReturn($expectedUuid);

        $purchasableItem = $this->factory->create($course, $price);

        $this->assertSame('', $purchasableItem->getDescription());
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateWithDifferentCurrencies(): void
    {
        $course = CourseMother::create(id: 789, name: 'USD Course');
        $priceUSD = MoneyMother::create(amount: 7500, currency: Currency::USD());
        $expectedUuid = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->once())
            ->method('generate')
            ->willReturn($expectedUuid);

        $purchasableItem = $this->factory->create($course, $priceUSD);

        $this->assertEquals($priceUSD, $purchasableItem->getPrice());
        $this->assertEquals(Currency::USD(), $purchasableItem->getPrice()->currency());
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateWithZeroPrice(): void
    {
        $course = CourseMother::create(id: 999, name: 'Free Course');
        $freePrice = MoneyMother::create(amount: 0);
        $expectedUuid = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->once())
            ->method('generate')
            ->willReturn($expectedUuid);

        $purchasableItem = $this->factory->create($course, $freePrice);

        $this->assertTrue($purchasableItem->getPrice()->isZero());
        $this->assertSame(0, $purchasableItem->getPrice()->value());
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    public function testCreateWithInvalidSourceThrowsException(): void
    {
        $invalidSource = 'not a course';
        $price = MoneyMother::create();

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Expected Course');

        $this->factory->create($invalidSource, $price);
    }

    /**
     * @throws InvalidPurchasableItemException
     */
    public function testCreateWithNullSourceThrowsException(): void
    {
        $price = MoneyMother::create();

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Expected Course');

        $this->factory->create(null, $price);
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateTimestampIsRecent(): void
    {
        $course = CourseMother::create(id: 555, name: 'Timestamp Test Course');
        $price = MoneyMother::create();
        $expectedUuid = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->once())
            ->method('generate')
            ->willReturn($expectedUuid);

        $beforeCreation = new \DateTimeImmutable();
        $purchasableItem = $this->factory->create($course, $price);
        $afterCreation = new \DateTimeImmutable();

        $createdAt = $purchasableItem->getCreatedAt();
        $this->assertGreaterThanOrEqual($beforeCreation, $createdAt);
        $this->assertLessThanOrEqual($afterCreation, $createdAt);
    }

    /**
     * @throws InvalidPurchasableItemException
     * @throws InvalidUuidException
     */
    public function testCreateGeneratesUniqueResourceForEachCourse(): void
    {
        $course1 = CourseMother::create(id: 100, name: 'Course 1');
        $course2 = CourseMother::create(id: 200, name: 'Course 2');
        $price = MoneyMother::create();

        $uuid1 = UuidMother::create();
        $uuid2 = UuidMother::create();

        $this->uuidGenerator
            ->expects($this->exactly(2))
            ->method('generate')
            ->willReturnOnConsecutiveCalls($uuid1, $uuid2);

        $purchasableItem1 = $this->factory->create($course1, $price);
        $purchasableItem2 = $this->factory->create($course2, $price);

        $this->assertNotEquals($purchasableItem1->getResource(), $purchasableItem2->getResource());
        $this->assertEquals(new Id(100), $purchasableItem1->getResource()->getId());
        $this->assertEquals(new Id(200), $purchasableItem2->getResource()->getId());
        $this->assertEquals(ResourceType::Course, $purchasableItem1->getResource()->getType());
        $this->assertEquals(ResourceType::Course, $purchasableItem2->getResource()->getType());
    }
}
