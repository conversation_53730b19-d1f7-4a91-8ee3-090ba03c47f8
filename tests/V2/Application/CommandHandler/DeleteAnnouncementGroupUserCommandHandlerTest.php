<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use App\Repository\AnnouncementGroupRepository as LegacyAnnouncementGroupRepository;
use App\Repository\AnnouncementRepository as LegacyAnnouncementRepository;
use App\Repository\AnnouncementUserRepository as LegacyAnnouncementUserRepository;
use App\Tests\Mother\Entity\AnnouncementGroupMother as LegacyAnnouncementGroupMother;
use App\Tests\Mother\Entity\AnnouncementMother as LegacyAnnouncementMother;
use App\Tests\Mother\Entity\AnnouncementUserMother as LegacyAnnouncementUserMother;
use App\Tests\Mother\Entity\CourseMother as LegacyCourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\Command\DeleteAnnouncementGroupUserCommand;
use App\V2\Application\CommandHandler\DeleteAnnouncementGroupUserCommandHandler;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationServiceInterface;
use App\V2\Domain\Announcement\Exception\AnnouncementGroupNotFoundException;
use App\V2\Domain\Announcement\Exception\AnnouncementNotFoundException;
use App\V2\Domain\Announcement\Exception\DeleteAnnouncementGroupUserCommandHandlerException;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class DeleteAnnouncementGroupUserCommandHandlerTest extends TestCase
{
    private function getHandler(
        ?UserRepository $userRepository = null,
        ?LegacyAnnouncementRepository $legacyAnnouncementRepository = null,
        ?LegacyAnnouncementGroupRepository $legacyAnnouncementGroupRepository = null,
        ?LegacyAnnouncementUserRepository $legacyAnnouncementUserRepository = null,
        ?AnnouncementAuthorizationServiceInterface $announcementAuthorizationService = null,
    ): DeleteAnnouncementGroupUserCommandHandler {
        return new DeleteAnnouncementGroupUserCommandHandler(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            legacyAnnouncementRepository: $legacyAnnouncementRepository
                ?? $this->createMock(LegacyAnnouncementRepository::class),
            legacyAnnouncementGroupRepository: $legacyAnnouncementGroupRepository
                ?? $this->createMock(LegacyAnnouncementGroupRepository::class),
            legacyAnnouncementUserRepository: $legacyAnnouncementUserRepository
                ?? $this->createMock(UserRepository::class),
            announcementAuthorizationService: $announcementAuthorizationService
                ?? $this->createMock(AnnouncementAuthorizationServiceInterface::class),
        );
    }

    public static function provideExceptions(): \Generator
    {
        yield 'announcement not found' => [
            'announcement' => null,
            'announcementGroup' => null,
            'testUser' => null,
            'announcementUser' => null,
            'isGranted' => null,
            'expectedException' => new AnnouncementNotFoundException(),
        ];
        $announcement = LegacyAnnouncementMother::create(id: 1, course: LegacyCourseMother::create(id: 1));
        yield 'announcement group not found' => [
            'announcement' => $announcement,
            'announcementGroup' => null,
            'testUser' => null,
            'announcementUser' => null,
            'isGranted' => null,
            'expectedException' => new AnnouncementGroupNotFoundException(),
        ];

        $announcementGroup = LegacyAnnouncementGroupMother::create(id: 1, announcement: $announcement);

        yield 'user not found' => [
            'announcement' => $announcement,
            'announcementGroup' => $announcementGroup,
            'testUser' => fn () => throw new UserNotFoundException(),
            'announcementUser' => null,
            'isGranted' => null,
            'expectedException' => new UserNotFoundException(),
        ];

        yield 'announcement user not found' => [
            'announcement' => $announcement,
            'announcementGroup' => $announcementGroup,
            'testUser' => fn () => UserMother::create(id: 2),
            'announcementUser' => null,
            'isGranted' => null,
            'expectedException' => DeleteAnnouncementGroupUserCommandHandlerException::announcementUserNotFound(),
        ];

        yield 'announcement user not in group' => [
            'announcement' => $announcement,
            'announcementGroup' => $announcementGroup,
            'testUser' => fn () => UserMother::create(id: 2),
            'announcementUser' => LegacyAnnouncementUserMother::create(
                id: 1,
                announcement: $announcement,
                announcementGroup: LegacyAnnouncementGroupMother::create(id: 2)
            ),
            'isGranted' => null,
            'expectedException' => DeleteAnnouncementGroupUserCommandHandlerException::userNotInGroup(),
        ];

        yield 'not granted' => [
            'announcement' => $announcement,
            'announcementGroup' => $announcementGroup,
            'testUser' => fn () => UserMother::create(id: 2),
            'announcementUser' => LegacyAnnouncementUserMother::create(
                id: 1,
                announcement: $announcement,
                announcementGroup: $announcementGroup,
            ),
            'isGranted' => false,
            'expectedException' => UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: '<EMAIL>'
            ),
        ];
    }

    #[DataProvider('provideExceptions')]
    public function testExceptions(
        ?Announcement $announcement,
        ?AnnouncementGroup $announcementGroup,
        ?callable $testUser,
        ?AnnouncementUser $announcementUser,
        ?bool $isGranted,
        \Exception $expectedException,
    ): void {
        $userRepository = $this->createMock(UserRepository::class);
        if (null !== $testUser) {
            $userRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($testUser);
        }

        $legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        if (null !== $announcement) {
            $legacyAnnouncementRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturn(
                    $announcement
                );
        }

        $legacyAnnouncementGroupRepository = $this->createMock(LegacyAnnouncementGroupRepository::class);
        if (null !== $announcementGroup) {
            $legacyAnnouncementGroupRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturn($announcementGroup);
        }

        $legacyAnnouncementUserRepository = $this->createMock(LegacyAnnouncementUserRepository::class);
        if (null !== $announcementUser) {
            $legacyAnnouncementUserRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturn($announcementUser);
        }

        $announcementAuthorizationService = $this->createMock(AnnouncementAuthorizationServiceInterface::class);
        if (false === $isGranted) {
            $announcementAuthorizationService->expects($this->once())
                ->method('ensureUserCanManageAnnouncement')
                ->willThrowException(
                    UserNotAuthorizedException::userNotAuthorized(
                        announcement: $announcement,
                        user: '<EMAIL>'
                    )
                );
        }

        $handler = $this->getHandler(
            userRepository: $userRepository,
            legacyAnnouncementRepository: $legacyAnnouncementRepository,
            legacyAnnouncementGroupRepository: $legacyAnnouncementGroupRepository,
            legacyAnnouncementUserRepository: $legacyAnnouncementUserRepository,
            announcementAuthorizationService: $announcementAuthorizationService,
        );

        $this->expectExceptionObject($expectedException);
        $handler->handle(new DeleteAnnouncementGroupUserCommand(
            announcementId: new Id(1),
            groupId: new Id(1),
            userId: new Id(2),
            requestUser: UserMother::create(id: 1, email: '<EMAIL>'),
        ));
    }

    public function testHandle(): void
    {
        $announcement = LegacyAnnouncementMother::create(id: 1);
        $announcementGroup = LegacyAnnouncementGroupMother::create(
            id: 1,
            announcement: $announcement,
        );
        $user = UserMother::create(
            id: 1,
            roles: [User::ROLE_ADMIN]
        );

        $testUser = UserMother::create(
            id: 2,
        );

        $announcementUser = LegacyAnnouncementUserMother::create(
            id: 1,
            announcement: $announcement,
            announcementGroup: $announcementGroup,
        );

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($testUser);

        $legacyAnnouncementRepository = $this->createMock(LegacyAnnouncementRepository::class);
        $legacyAnnouncementRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(
                $announcement
            );

        $legacyAnnouncementGroupRepository = $this->createMock(LegacyAnnouncementGroupRepository::class);
        $legacyAnnouncementGroupRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcementGroup);

        $legacyAnnouncementUserRepository = $this->createMock(LegacyAnnouncementUserRepository::class);
        $legacyAnnouncementUserRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($announcementUser);

        $legacyAnnouncementUserRepository->expects($this->once())
            ->method('delete');

        $announcementAuthorizationService = $this->createMock(AnnouncementAuthorizationServiceInterface::class);
        $announcementAuthorizationService->expects($this->once())
            ->method('ensureUserCanManageAnnouncement');

        $handler = $this->getHandler(
            userRepository: $userRepository,
            legacyAnnouncementRepository: $legacyAnnouncementRepository,
            legacyAnnouncementGroupRepository: $legacyAnnouncementGroupRepository,
            legacyAnnouncementUserRepository: $legacyAnnouncementUserRepository,
            announcementAuthorizationService: $announcementAuthorizationService,
        );

        $handler->handle(new DeleteAnnouncementGroupUserCommand(
            announcementId: new Id($announcementUser->getId()),
            groupId: new Id($announcementGroup->getId()),
            userId: new Id($testUser->getId()),
            requestUser: $user,
        ));
    }
}
