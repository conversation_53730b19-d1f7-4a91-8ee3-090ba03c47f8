<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\PostLtiPlatformCommand;
use App\V2\Application\CommandHandler\PostLtiPlatformCommandHandler;
use App\V2\Domain\LTI\Exceptions\LtiPlatformNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\LTI\LtiPlatformRepository;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostLtiPlatformCommandHandlerTest extends TestCase
{
    private function getHandler(
        ?LtiRegistrationRepository $ltiRegistrationRepository = null,
        ?LtiPlatformRepository $ltiPlatformRepository = null,
        ?UuidGenerator $uuidGenerator = null,
    ): PostLtiPlatformCommandHandler {
        return new PostLtiPlatformCommandHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository
                ?? $this->createMock(LtiRegistrationRepository::class),
            ltiPlatformRepository: $ltiPlatformRepository ?? $this->createMock(LtiPlatformRepository::class),
            uuidGenerator: $uuidGenerator ?? $this->createMock(UuidGenerator::class),
        );
    }

    /**
     * @throws LtiRegistrationNotFoundException
     * @throws InfrastructureException
     * @throws Exception
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function testHandle(): void
    {
        $platformId = UuidMother::create();
        $registration = LtiRegistrationMother::create();
        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($registration);

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($platformId);

        $ltiPlatformRepository = $this->createMock(LtiPlatformRepository::class);
        $ltiPlatformRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new LtiPlatformNotFoundException());

        $ltiPlatformRepository->expects($this->once())
            ->method('put')
            ->willReturnCallback(function (LtiPlatform $platform) use ($platformId) {
                $this->assertEquals($platformId, $platform->getId());
                $this->assertEquals('Platform 1', $platform->getName());
                $this->assertEquals('Platform audience', $platform->getAudience());
                $this->assertEquals(new Url('https://example.com/authenticate'), $platform->getOidcAuthenticationUrl());
                $this->assertEquals(new Url('https://example.com/token'), $platform->getOauth2AccessTokenUrl());
                $this->assertEquals(new Url('https://example.com/jwks'), $platform->getJwksUrl());
            });

        $handler = $this->getHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            ltiPlatformRepository: $ltiPlatformRepository,
            uuidGenerator: $uuidGenerator,
        );

        $handler->handle(
            new PostLtiPlatformCommand(
                registrationId: $registration->getId(),
                name: 'Platform 1',
                audience: 'Platform audience',
                oidcAuthenticationUrl: new Url('https://example.com/authenticate'),
                oauth2AccessTokenUrl: new Url('https://example.com/token'),
                jwksUrl: new Url('https://example.com/jwks'),
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws LtiRegistrationNotFoundException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testRegistrationNotFoundException(): void
    {
        $exception = new LtiRegistrationNotFoundException();
        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException($exception);

        $handler = $this->getHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository,
        );

        $this->expectExceptionObject($exception);
        $handler->handle(
            new PostLtiPlatformCommand(
                registrationId: UuidMother::create(),
                name: 'Platform 1',
                audience: 'Platform audience',
                oidcAuthenticationUrl: new Url('https://example.com/authenticate'),
                oauth2AccessTokenUrl: new Url('https://example.com/token'),
                jwksUrl: new Url('https://example.com/jwks'),
            )
        );
    }
}
