<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\Purchase\Payment\PaymentMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\CreatePurchasePaymentCommand;
use App\V2\Application\CommandHandler\CreatePurchasePaymentCommandHandler;
use App\V2\Domain\Purchase\Exception\PurchaseNotAllowedException;
use App\V2\Domain\Purchase\Exception\PurchaseNotFoundException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\Exception\PurchaseStatusException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class CreatePurchasePaymentCommandHandlerTest extends TestCase
{
    private PurchaseRepository&MockObject $purchaseRepository;
    private PaymentManagerInterface&MockObject $paymentManager;
    private CreatePurchasePaymentCommandHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->purchaseRepository = $this->createMock(PurchaseRepository::class);
        $this->paymentManager = $this->createMock(PaymentManagerInterface::class);
        $this->handler = new CreatePurchasePaymentCommandHandler(
            purchaseRepository: $this->purchaseRepository,
            paymentManager: $this->paymentManager,
        );
    }

    private function getHandler(
        Uuid $purchaseId,
        Id $userId,
    ): CreatePurchasePaymentCommand {
        return new CreatePurchasePaymentCommand(
            purchaseId: $purchaseId,
            userId: $userId
        );
    }

    /**
     * @throws \Exception
     */
    public function testHandleMethodExists(): void
    {
        $this->assertTrue(method_exists(CreatePurchasePaymentCommandHandler::class, 'handle'));
        $this->assertTrue(\is_callable([$this->handler, 'handle']));
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleSuccessfullyCreatesPayment(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: PurchaseStatus::Pending);
        $expectedPayment = PaymentMother::create(purchaseId: $purchaseId, userId: $userId);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->once())
            ->method('createPayment')
            ->with($purchase)
            ->willReturn($expectedPayment);

        $result = $this->handler->handle($command);

        $this->assertSame($expectedPayment, $result);
        $this->assertInstanceOf(Payment::class, $result);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsPurchaseNotFoundExceptionWhenPurchaseNotExists(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willThrowException(new PurchaseNotFoundException());

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseNotFoundException::class);
        $this->handler->handle($command);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotAllowedException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsPurchaseNotAllowedExceptionWhenUserDoesNotOwnPurchase(): void
    {
        $purchaseId = UuidMother::create();
        $purchaseUserId = IdMother::create(123);
        $commandUserId = IdMother::create(456); // Different user ID
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $purchaseUserId);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $commandUserId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseNotAllowedException::class);
        $this->handler->handle($command);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsPurchaseRepositoryException(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willThrowException(new PurchaseRepositoryException('Database error'));

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseRepositoryException::class);
        $this->expectExceptionMessage('Database error');
        $this->handler->handle($command);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsCriteriaException(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willThrowException(new CriteriaException('Invalid criteria'));

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(CriteriaException::class);
        $this->expectExceptionMessage('Invalid criteria');
        $this->handler->handle($command);
    }

    /**
     * @throws \Exception
     */
    #[DataProvider('provideValidCommands')]
    public function testHandleWithValidCommands(
        Uuid $purchaseId,
        Id $userId,
        Purchase $purchase,
        Payment $expectedPayment
    ): void {
        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->once())
            ->method('createPayment')
            ->with($purchase)
            ->willReturn($expectedPayment);

        $result = $this->handler->handle($command);

        $this->assertSame($expectedPayment, $result);
        $this->assertInstanceOf(Payment::class, $result);
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function provideValidCommands(): \Generator
    {
        $purchaseId1 = UuidMother::create();
        $userId1 = IdMother::create(100);
        $purchase1 = PurchaseMother::create(id: $purchaseId1, userId: $userId1, status: PurchaseStatus::Pending);
        $expectedPayment1 = PaymentMother::create(purchaseId: $purchaseId1, userId: $userId1);

        yield 'valid command with matching user' => [
            'purchaseId' => $purchaseId1,
            'userId' => $userId1,
            'purchase' => $purchase1,
            'expectedPayment' => $expectedPayment1,
        ];

        $purchaseId2 = UuidMother::create();
        $userId2 = IdMother::create(200);
        $purchase2 = PurchaseMother::create(id: $purchaseId2, userId: $userId2, status: PurchaseStatus::Pending);
        $expectedPayment2 = PaymentMother::create(purchaseId: $purchaseId2, userId: $userId2);

        yield 'another valid command with different user' => [
            'purchaseId' => $purchaseId2,
            'userId' => $userId2,
            'purchase' => $purchase2,
            'expectedPayment' => $expectedPayment2,
        ];
    }

    public function testConstructorInitializesCorrectly(): void
    {
        $handler = new CreatePurchasePaymentCommandHandler(
            purchaseRepository: $this->purchaseRepository,
            paymentManager: $this->paymentManager,
        );

        $this->assertInstanceOf(CreatePurchasePaymentCommandHandler::class, $handler);
    }

    public function testHandlerIsReadonly(): void
    {
        $reflection = new \ReflectionClass(CreatePurchasePaymentCommandHandler::class);
        $this->assertTrue($reflection->isReadOnly());
    }

    /**
     * @throws \Exception
     */
    public function testValidatePurchaseOwnershipMethodIsPrivate(): void
    {
        $reflection = new \ReflectionClass(CreatePurchasePaymentCommandHandler::class);
        $method = $reflection->getMethod('validatePurchaseOwnership');

        $this->assertTrue($method->isPrivate());
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleCallsRepositoryWithCorrectCriteria(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: PurchaseStatus::Pending);
        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                // Verify that the criteria is created with the correct purchase ID
                return $criteria->getId() === $purchaseId;
            }))
            ->willReturn($purchase);

        $expectedPayment = PaymentMother::create(purchaseId: $purchaseId, userId: $userId);

        $this->paymentManager
            ->expects($this->once())
            ->method('createPayment')
            ->with($purchase)
            ->willReturn($expectedPayment);

        $result = $this->handler->handle($command);

        $this->assertInstanceOf(Payment::class, $result);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleReturnValueConsistency(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: PurchaseStatus::Pending);
        $expectedPayment = PaymentMother::create(purchaseId: $purchaseId, userId: $userId);
        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->once())
            ->method('createPayment')
            ->with($purchase)
            ->willReturn($expectedPayment);

        $result = $this->handler->handle($command);

        $this->assertSame($expectedPayment, $result);
        $this->assertInstanceOf(Payment::class, $result);
        $this->assertNotNull($result->getId());
        $this->assertNotEmpty($result->getPaymentIntent());
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    #[DataProvider('provideOwnershipValidationScenarios')]
    public function testOwnershipValidationScenarios(
        Id $purchaseUserId,
        Id $commandUserId,
        bool $shouldThrowException,
    ): void {
        $purchaseId = UuidMother::create();
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $purchaseUserId, status: PurchaseStatus::Pending);
        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $commandUserId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($purchase);

        if ($shouldThrowException) {
            $this->paymentManager
                ->expects($this->never())
                ->method('createPayment');

            $this->expectException(PurchaseNotAllowedException::class);
            $this->handler->handle($command);
        } else {
            $expectedPayment = PaymentMother::create(purchaseId: $purchaseId, userId: $commandUserId);

            $this->paymentManager
                ->expects($this->once())
                ->method('createPayment')
                ->with($purchase)
                ->willReturn($expectedPayment);

            $result = $this->handler->handle($command);
            $this->assertInstanceOf(Payment::class, $result);
        }
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function provideOwnershipValidationScenarios(): \Generator
    {
        $userId1 = IdMother::create(100);
        $userId2 = IdMother::create(200);

        yield 'same user ID should succeed' => [
            'purchaseUserId' => $userId1,
            'commandUserId' => $userId1,
            'shouldThrowException' => false,
        ];

        yield 'different user ID should fail' => [
            'purchaseUserId' => $userId1,
            'commandUserId' => $userId2,
            'shouldThrowException' => true,
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsPurchaseStatusExceptionWhenPurchaseIsCompleted(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: PurchaseStatus::Completed);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseStatusException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws PurchaseNotFoundException
     * @throws CriteriaException
     * @throws \Exception
     */
    public function testHandleThrowsPurchaseStatusExceptionWhenPurchaseIsCancelled(): void
    {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: PurchaseStatus::Cancelled);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with($this->callback(function (PurchaseCriteria $criteria) use ($purchaseId) {
                return $criteria->getId() === $purchaseId;
            }))
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseStatusException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws \Exception
     */
    #[DataProvider('provideInvalidPurchaseStatuses')]
    public function testHandleThrowsPurchaseStatusExceptionForInvalidStatuses(
        PurchaseStatus $invalidStatus
    ): void {
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);
        $purchase = PurchaseMother::create(id: $purchaseId, userId: $userId, status: $invalidStatus);

        $command = $this->getHandler(
            purchaseId: $purchaseId,
            userId: $userId
        );

        $this->purchaseRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->willReturn($purchase);

        $this->paymentManager
            ->expects($this->never())
            ->method('createPayment');

        $this->expectException(PurchaseStatusException::class);

        $this->handler->handle($command);
    }

    /**
     * @throws \Exception
     */
    public static function provideInvalidPurchaseStatuses(): \Generator
    {
        yield 'completed purchase' => [
            'invalidStatus' => PurchaseStatus::Completed,
        ];

        yield 'cancelled purchase' => [
            'invalidStatus' => PurchaseStatus::Cancelled,
        ];
    }

    /**
     * @throws \Exception
     */
    public function testValidatePurchaseStatusMethodIsPrivate(): void
    {
        $reflection = new \ReflectionClass(CreatePurchasePaymentCommandHandler::class);
        $method = $reflection->getMethod('validatePurchaseStatus');

        $this->assertTrue($method->isPrivate());
    }
}
