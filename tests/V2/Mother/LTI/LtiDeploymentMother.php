<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\LTI;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class LtiDeploymentMother
{
    private const string DEFAULT_NAME = 'Deployment';
    private const string DEFAULT_DEPLOYMENT_ID = 'deploymentId';

    /**
     * @throws InvalidUuidException
     */
    public static function create(
        ?Uuid $id = null,
        ?Uuid $registrationId = null,
        ?string $name = null,
        ?string $deploymentId = null,
    ): LtiDeployment {
        return new LtiDeployment(
            id: $id ?? UuidMother::create(),
            registrationId: $registrationId ?? UuidMother::create(),
            name: $name ?? self::DEFAULT_NAME,
            deploymentId: $deploymentId ?? self::DEFAULT_DEPLOYMENT_ID,
        );
    }
}
