<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\LTI;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiPlatform;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class LtiPlatformMother
{
    private const string DEFAULT_NAME = 'EasyLearning Platform';
    private const string DEFAULT_AUDIENCE = 'EasyLearning';

    /**
     * @throws InvalidUuidException
     */
    public static function create(
        ?Uuid $id = null,
        ?Uuid $registrationId = null,
        ?string $name = null,
        ?string $audience = null,
        ?Url $oidcAuthenticationUrl = null,
        ?Url $oauth2AccessTokenUrl = null,
        ?Url $jwksUrl = null,
    ): LtiPlatform {
        return new LtiPlatform(
            id: $id ?? UuidMother::create(),
            registrationId: $registrationId ?? UuidMother::create(),
            name: $name ?? self::DEFAULT_NAME,
            audience: $audience ?? self::DEFAULT_AUDIENCE,
            oidcAuthenticationUrl: $oidcAuthenticationUrl ?? new Url('https://example.com/lti1p3/oidc/authentication'),
            oauth2AccessTokenUrl: $oauth2AccessTokenUrl ?? new Url('https://example.com/lti1p3/auth/platformKey/token'),
            jwksUrl: $jwksUrl ?? new Url('https://example.com/lti1p3/.well-known/jwks/platformSet.json'),
        );
    }
}
