<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Purchase\Payment;

use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class PaymentMother
{
    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function create(
        ?Uuid $id = null,
        ?Uuid $purchaseId = null,
        ?Id $userId = null,
        ?string $paymentIntent = null,
        ?Money $amount = null,
        ?PaymentStatus $status = null,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): Payment {
        return new Payment(
            id: $id ?? UuidMother::create(),
            purchaseId: $purchaseId ?? UuidMother::create(),
            userId: $userId ?? IdMother::create(),
            paymentIntent: $paymentIntent ?? 'pi_test_' . uniqid(),
            amount: $amount ?? MoneyMother::create(),
            status: $status ?? PaymentStatus::Pending,
            createdAt: $createdAt ?? new \DateTimeImmutable(),
            updatedAt: $updatedAt,
            deletedAt: $deletedAt,
        );
    }
}
