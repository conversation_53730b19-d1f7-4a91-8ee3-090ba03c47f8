<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Course\Creator;

use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Domain\Course\Creator\Creator;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Id\Id;

class CreatorMother
{
    private const int DEFAULT_ID = 1;
    private const string DEFAULT_FIRST_NAME = 'John';
    private const string DEFAULT_LAST_NAME = 'Doe';

    public static function create(
        ?Id $id = null,
        ?Email $email = null,
        ?string $firstName = null,
        ?string $lastName = null,
    ): Creator {
        return new Creator(
            id: $id ?? new Id(self::DEFAULT_ID),
            email: $email ?? EmailMother::create(),
            firstName: $firstName ?? self::DEFAULT_FIRST_NAME,
            lastName: $lastName ?? self::DEFAULT_LAST_NAME,
        );
    }
}
