<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Course\Creator;

use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Shared\Id\Id;

class CourseCreatorMother
{
    private const int DEFAULT_USER_ID = 1;
    private const int DEFAULT_COURSE_ID = 1;

    public static function create(
        ?Id $userId = null,
        ?Id $courseId = null,
    ): CourseCreator {
        return new CourseCreator(
            userId: $userId ?? new Id(self::DEFAULT_USER_ID),
            courseId: $courseId ?? new Id(self::DEFAULT_COURSE_ID),
        );
    }
}
