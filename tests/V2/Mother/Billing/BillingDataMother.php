<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Billing;

use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Billing\BillingData;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\Uuid;

class BillingDataMother
{
    private const string DEFAULT_TIN = '12345678A';
    private const string DEFAULT_FIRST_NAME = 'John';
    private const string DEFAULT_LAST_NAME = 'Doe';
    private const string DEFAULT_ADDRESS = '123 Main Street';
    private const string DEFAULT_POSTAL_CODE = '12345';
    private const string DEFAULT_CITY = 'Madrid';
    private const string DEFAULT_COUNTRY = 'Spain';

    /**
     * @throws \Exception
     */
    public static function create(
        ?Uuid $id = null,
        ?Id $userId = null,
        ?string $tin = null,
        ?string $firstName = null,
        ?string $lastName = null,
        ?string $address = null,
        ?string $postalCode = null,
        ?string $city = null,
        ?string $country = null,
        ?array $metadata = null,
    ): BillingData {
        return new BillingData(
            id: $id ?? UuidMother::create(),
            userId: $userId ?? IdMother::create(),
            tin: $tin ?? self::DEFAULT_TIN,
            firstName: $firstName ?? self::DEFAULT_FIRST_NAME,
            lastName: $lastName ?? self::DEFAULT_LAST_NAME,
            address: $address ?? self::DEFAULT_ADDRESS,
            postalCode: $postalCode ?? self::DEFAULT_POSTAL_CODE,
            city: $city ?? self::DEFAULT_CITY,
            country: $country ?? self::DEFAULT_COUNTRY,
            metadata: $metadata ?? [],
        );
    }
}
