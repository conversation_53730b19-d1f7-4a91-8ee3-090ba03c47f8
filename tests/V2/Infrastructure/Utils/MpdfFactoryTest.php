<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Utils;

use App\V2\Infrastructure\Utils\MpdfFactory;
use PHPUnit\Framework\TestCase;

class MpdfFactoryTest extends TestCase
{
    private function getFactory(
        string $cacheDir = '/tmp',
    ): MpdfFactory {
        return new MpdfFactory(cacheDir: $cacheDir);
    }

    public function testDefaultConstructorParams(): void
    {
        $factory = $this->getFactory();
        $this->assertEquals([
            'mode' => '',
            'format' => 'A4',
            'default_font_size' => 0,
            'default_font' => '',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'orientation' => 'P',
        ], $factory->getDefaultConstructorParams());
    }
}
