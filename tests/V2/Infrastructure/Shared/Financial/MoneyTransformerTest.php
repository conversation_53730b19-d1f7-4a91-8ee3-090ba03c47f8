<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Shared\Financial;

use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Financial\Money;
use App\V2\Infrastructure\Shared\Financial\MoneyTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class MoneyTransformerTest extends TestCase
{
    /**
     * @throws InvalidCurrencyCodeException
     */
    #[DataProvider('validPayloadProvider')]
    public function testValidPayload(
        array $payload,
        Money $expected,
    ) {
        $money = MoneyTransformer::fromPayload($payload);

        $this->assertEquals($expected, $money);
    }

    public static function validPayloadProvider(): \Generator
    {
        yield 'EUR' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'EUR',
            ],
            'expected' => Money::create(1000, Currency::EUR()),
        ];

        yield 'USD' => [
            'payload' => [
                'price_amount' => 2500,
                'price_currency' => 'USD',
            ],
            'expected' => Money::create(2500, Currency::USD()),
        ];
    }

    #[DataProvider('invalidPayloadProvider')]
    public function testInvalidPayload(
        array $payload,
        string $expectedException,
    ): void {
        $this->expectException($expectedException);

        MoneyTransformer::fromPayload($payload);
    }

    public static function invalidPayloadProvider(): \Generator
    {
        yield 'invalid currency code' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => 'GBP',
            ],
            'expectedException' => InvalidCurrencyCodeException::class,
        ];

        yield 'empty price currency' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => '',
            ],
            'expectedException' => InvalidCurrencyCodeException::class,
        ];

        yield 'empty price amount' => [
            'payload' => [
                'price_amount' => '',
                'price_currency' => 'EUR',
            ],
            'expectedException' => \TypeError::class,
        ];

        yield 'null price amount' => [
            'payload' => [
                'price_amount' => null,
                'price_currency' => 'EUR',
            ],
            'expectedException' => \TypeError::class,
        ];

        yield 'null price currency' => [
            'payload' => [
                'price_amount' => 1000,
                'price_currency' => null,
            ],

            'expectedException' => \TypeError::class,
        ];

        yield 'invalid price amount type' => [
            'payload' => [
                'price_amount' => '1000',
                'price_currency' => 'EUR',
            ],
            'expectedException' => \TypeError::class,
        ];
    }
}
