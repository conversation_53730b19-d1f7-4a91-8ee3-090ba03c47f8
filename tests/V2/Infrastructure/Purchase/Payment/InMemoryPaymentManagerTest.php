<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase\Payment;

use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Persistence\Purchase\Payment\InMemoryPaymentRepository;
use App\V2\Infrastructure\Purchase\Payment\InMemoryPaymentManager;
use App\V2\Infrastructure\Shared\Uuid\RamseyUuidGenerator;
use PHPUnit\Framework\TestCase;

class InMemoryPaymentManagerTest extends TestCase
{
    private PaymentRepository $paymentRepository;
    private PaymentManagerInterface $paymentManager;

    protected function setUp(): void
    {
        $this->paymentRepository = new InMemoryPaymentRepository();
        $this->paymentManager = new InMemoryPaymentManager(
            $this->paymentRepository,
            new RamseyUuidGenerator()
        );
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     */
    public function testCreatePayment(): void
    {
        $purchase = PurchaseMother::create();

        $payment = $this->paymentManager->createPayment($purchase);

        $this->assertNotNull($payment->getId());
        $this->assertEquals($purchase->getId(), $payment->getPurchaseId());
        $this->assertEquals($purchase->getUserId(), $payment->getUserId());
        $this->assertEquals($purchase->getAmount(), $payment->getAmount());
        $this->assertEquals(PaymentStatus::Pending, $payment->getStatus());
        $this->assertStringStartsWith('pi_test_', $payment->getPaymentIntent());
    }

    /**
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testCreatePaymentStoresInRepository(): void
    {
        $purchase = PurchaseMother::create();

        $payment = $this->paymentManager->createPayment($purchase);

        // Verify the payment was stored in the repository
        $storedPayment = $this->paymentRepository->findOneBy(
            PaymentCriteria::createById($payment->getId())
        );

        $this->assertEquals($payment, $storedPayment);
    }

    /**
     * @throws PaymentRepositoryException
     * @throws InvalidUuidException
     */
    public function testCreatePaymentGeneratesUniquePaymentIntents(): void
    {
        $purchase1 = PurchaseMother::create();
        $purchase2 = PurchaseMother::create();

        $payment1 = $this->paymentManager->createPayment($purchase1);
        $payment2 = $this->paymentManager->createPayment($purchase2);

        $this->assertNotEquals($payment1->getPaymentIntent(), $payment2->getPaymentIntent());
        $this->assertStringStartsWith('pi_test_', $payment1->getPaymentIntent());
        $this->assertStringStartsWith('pi_test_', $payment2->getPaymentIntent());
    }
}
