<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Purchase\Payment;

use App\Tests\V2\Mother\Purchase\Payment\PaymentMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Financial\CurrencyCode;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Purchase\Payment\PaymentTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PaymentTransformerTest extends TestCase
{
    #[DataProvider('validPaymentProvider')]
    public function testToArrayWithValidInputs(
        Payment $payment,
        array $expectedArray,
    ): void {
        $result = PaymentTransformer::toArray($payment);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    public function testToArrayMethodExists(): void
    {
        $this->assertTrue(method_exists(PaymentTransformer::class, 'toArray'));
        $this->assertTrue(\is_callable([PaymentTransformer::class, 'toArray']));
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testToArrayReturnValueConsistency(Payment $payment): void
    {
        $result1 = PaymentTransformer::toArray($payment);
        $result2 = PaymentTransformer::toArray($payment);

        $this->assertIsArray($result1);
        $this->assertIsArray($result2);
        $this->assertEquals($result1, $result2);
    }

    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        Payment $payment,
        string $expectedId,
        string $expectedPurchaseId,
        string $expectedPaymentIntent,
        int $expectedAmount,
        string $expectedCurrencyCode,
        string $expectedStatus,
    ): void {
        $result = PaymentTransformer::toArray($payment);

        $this->assertSame($expectedId, $result['id']);
        $this->assertSame($expectedPurchaseId, $result['purchase_id']);
        $this->assertSame($expectedPaymentIntent, $result['payment_intent']);
        $this->assertSame($expectedAmount, $result['amount']);
        $this->assertSame($expectedCurrencyCode, $result['currency_code']);
        $this->assertSame($expectedStatus, $result['status']);
    }

    #[DataProvider('edgeCasesProvider')]
    public function testEdgeCases(Payment $payment, array $expectedArray): void
    {
        $result = PaymentTransformer::toArray($payment);

        $this->assertIsArray($result);
        $this->assertEquals($expectedArray, $result);
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function validPaymentProvider(): \Generator
    {
        $paymentId = UuidMother::create();
        $purchaseId = UuidMother::create();
        $userId = IdMother::create(123);

        $pendingPayment = PaymentMother::create(
            id: $paymentId,
            purchaseId: $purchaseId,
            userId: $userId,
            paymentIntent: 'pi_test_pending_123',
            amount: MoneyMother::create(amount: 2500, currency: new Currency(CurrencyCode::EUR)),
            status: PaymentStatus::Pending
        );

        yield 'pending payment with EUR currency' => [
            'payment' => $pendingPayment,
            'expectedArray' => [
                'id' => $paymentId->value(),
                'purchase_id' => $purchaseId->value(),
                'payment_intent' => 'pi_test_pending_123',
                'amount' => 2500,
                'currency_code' => 'EUR',
                'status' => 'pending',
            ],
        ];

        $paidPaymentId = UuidMother::create();
        $paidPurchaseId = UuidMother::create();
        $paidUserId = IdMother::create(456);

        $paidPayment = PaymentMother::create(
            id: $paidPaymentId,
            purchaseId: $paidPurchaseId,
            userId: $paidUserId,
            paymentIntent: 'pi_test_paid_456',
            amount: MoneyMother::create(amount: 5000, currency: new Currency(CurrencyCode::USD)),
            status: PaymentStatus::Paid
        );

        yield 'paid payment with USD currency' => [
            'payment' => $paidPayment,
            'expectedArray' => [
                'id' => $paidPaymentId->value(),
                'purchase_id' => $paidPurchaseId->value(),
                'payment_intent' => 'pi_test_paid_456',
                'amount' => 5000,
                'currency_code' => 'USD',
                'status' => 'paid',
            ],
        ];

        $failedPaymentId = UuidMother::create();
        $failedPurchaseId = UuidMother::create();
        $failedUserId = IdMother::create(789);

        $failedPayment = PaymentMother::create(
            id: $failedPaymentId,
            purchaseId: $failedPurchaseId,
            userId: $failedUserId,
            paymentIntent: 'pi_test_failed_789',
            amount: MoneyMother::create(amount: 1000, currency: new Currency(CurrencyCode::EUR)),
            status: PaymentStatus::Failed
        );

        yield 'failed payment' => [
            'payment' => $failedPayment,
            'expectedArray' => [
                'id' => $failedPaymentId->value(),
                'purchase_id' => $failedPurchaseId->value(),
                'payment_intent' => 'pi_test_failed_789',
                'amount' => 1000,
                'currency_code' => 'EUR',
                'status' => 'failed',
            ],
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'returns consistent array for pending payment' => [
            'payment' => PaymentMother::create(
                status: PaymentStatus::Pending,
                amount: MoneyMother::create(amount: 1500, currency: new Currency(CurrencyCode::EUR))
            ),
        ];

        yield 'returns consistent array for paid payment' => [
            'payment' => PaymentMother::create(
                status: PaymentStatus::Paid,
                amount: MoneyMother::create(amount: 3000, currency: new Currency(CurrencyCode::USD))
            ),
        ];

        yield 'returns consistent array for failed payment' => [
            'payment' => PaymentMother::create(
                status: PaymentStatus::Failed,
                amount: MoneyMother::create(amount: 750, currency: new Currency(CurrencyCode::EUR))
            ),
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function dataTransformationProvider(): \Generator
    {
        $paymentId = UuidMother::create();
        $purchaseId = UuidMother::create();
        $userId = new Id(100);

        yield 'pending payment transformation' => [
            'payment' => PaymentMother::create(
                id: $paymentId,
                purchaseId: $purchaseId,
                userId: $userId,
                paymentIntent: 'pi_test_transformation_pending',
                amount: MoneyMother::create(amount: 2000, currency: new Currency(CurrencyCode::EUR)),
                status: PaymentStatus::Pending
            ),
            'expectedId' => $paymentId->value(),
            'expectedPurchaseId' => $purchaseId->value(),
            'expectedPaymentIntent' => 'pi_test_transformation_pending',
            'expectedAmount' => 2000,
            'expectedCurrencyCode' => 'EUR',
            'expectedStatus' => 'pending',
        ];

        $paidPaymentId = UuidMother::create();
        $paidPurchaseId = UuidMother::create();
        $paidUserId = new Id(200);

        yield 'paid payment transformation' => [
            'payment' => PaymentMother::create(
                id: $paidPaymentId,
                purchaseId: $paidPurchaseId,
                userId: $paidUserId,
                paymentIntent: 'pi_test_transformation_paid',
                amount: MoneyMother::create(amount: 7500, currency: new Currency(CurrencyCode::USD)),
                status: PaymentStatus::Paid
            ),
            'expectedId' => $paidPaymentId->value(),
            'expectedPurchaseId' => $paidPurchaseId->value(),
            'expectedPaymentIntent' => 'pi_test_transformation_paid',
            'expectedAmount' => 7500,
            'expectedCurrencyCode' => 'USD',
            'expectedStatus' => 'paid',
        ];

        $failedPaymentId = UuidMother::create();
        $failedPurchaseId = UuidMother::create();
        $failedUserId = new Id(300);

        yield 'failed payment transformation' => [
            'payment' => PaymentMother::create(
                id: $failedPaymentId,
                purchaseId: $failedPurchaseId,
                userId: $failedUserId,
                paymentIntent: 'pi_test_transformation_failed',
                amount: MoneyMother::create(amount: 500, currency: new Currency(CurrencyCode::EUR)),
                status: PaymentStatus::Failed
            ),
            'expectedId' => $failedPaymentId->value(),
            'expectedPurchaseId' => $failedPurchaseId->value(),
            'expectedPaymentIntent' => 'pi_test_transformation_failed',
            'expectedAmount' => 500,
            'expectedCurrencyCode' => 'EUR',
            'expectedStatus' => 'failed',
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws \Exception
     */
    public static function edgeCasesProvider(): \Generator
    {
        // Test with minimum amount
        $minAmountId = UuidMother::create();
        $minAmountPurchaseId = UuidMother::create();
        $minAmountUserId = IdMother::create(1);

        $minAmountPayment = PaymentMother::create(
            id: $minAmountId,
            purchaseId: $minAmountPurchaseId,
            userId: $minAmountUserId,
            paymentIntent: 'pi_test_min_amount',
            amount: MoneyMother::create(amount: 1, currency: new Currency(CurrencyCode::EUR)),
            status: PaymentStatus::Pending
        );

        yield 'payment with minimum amount' => [
            'payment' => $minAmountPayment,
            'expectedArray' => [
                'id' => $minAmountId->value(),
                'purchase_id' => $minAmountPurchaseId->value(),
                'payment_intent' => 'pi_test_min_amount',
                'amount' => 1,
                'currency_code' => 'EUR',
                'status' => 'pending',
            ],
        ];

        // Test with large amount
        $largeAmountId = UuidMother::create();
        $largeAmountPurchaseId = UuidMother::create();
        $largeAmountUserId = IdMother::create(999999);

        $largeAmountPayment = PaymentMother::create(
            id: $largeAmountId,
            purchaseId: $largeAmountPurchaseId,
            userId: $largeAmountUserId,
            paymentIntent: 'pi_test_large_amount_999999',
            amount: MoneyMother::create(amount: 999999999, currency: new Currency(CurrencyCode::USD)),
            status: PaymentStatus::Paid
        );

        yield 'payment with large amount' => [
            'payment' => $largeAmountPayment,
            'expectedArray' => [
                'id' => $largeAmountId->value(),
                'purchase_id' => $largeAmountPurchaseId->value(),
                'payment_intent' => 'pi_test_large_amount_999999',
                'amount' => 999999999,
                'currency_code' => 'USD',
                'status' => 'paid',
            ],
        ];
    }
}
