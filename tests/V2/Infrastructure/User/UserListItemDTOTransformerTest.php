<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\Tests\V2\Mother\User\PaginatedUserListItemDtoCollectionMother;
use App\Tests\V2\Mother\User\UserListItemDtoCollectionMother;
use App\Tests\V2\Mother\User\UserListItemDtoMother;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\User\UserListItemDTOTransformer;
use PHPUnit\Framework\TestCase;

class UserListItemDTOTransformerTest extends TestCase
{
    /**
     * Test that fromDTOToArray correctly transforms a UserListItemDTO to an array.
     */
    public function testFromDTOToArray(): void
    {
        // Arrange
        $dto = UserListItemDtoMother::create(
            id: 123,
            avatar: 'avatar.jpg',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            roles: ['ROLE_USER', 'ROLE_ADMIN'],
            isActive: true,
            points: 100,
            editable: true,
            deletable: true,
            allowLoginAs: false,
        );

        // Act
        $result = UserListItemDTOTransformer::fromDTOToArray($dto);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('avatar', $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('first_name', $result);
        $this->assertArrayHasKey('last_name', $result);
        $this->assertArrayHasKey('roles', $result);
        $this->assertArrayHasKey('is_active', $result);
        $this->assertArrayHasKey('points', $result);
        $this->assertArrayHasKey('actions', $result);

        $this->assertEquals(123, $result['id']);
        $this->assertEquals('avatar.jpg', $result['avatar']);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('John', $result['first_name']);
        $this->assertEquals('Doe', $result['last_name']);
        $this->assertEquals(['ROLE_USER', 'ROLE_ADMIN'], $result['roles']);
        $this->assertTrue($result['is_active']);
        $this->assertEquals(100, $result['points']);

        $this->assertIsArray($result['actions']);
        $this->assertArrayHasKey('editable', $result['actions']);
        $this->assertArrayHasKey('deletable', $result['actions']);
        $this->assertArrayHasKey('allow_login_as', $result['actions']);
        $this->assertTrue($result['actions']['editable']);
        $this->assertTrue($result['actions']['deletable']);
        $this->assertFalse($result['actions']['allow_login_as']);
    }

    /**
     * Test that fromDTOCollectionToArray correctly transforms a UserListItemDTOCollection to an array.
     *
     * @throws CollectionException
     */
    public function testFromDTOCollectionToArray(): void
    {
        // Arrange
        $collection = UserListItemDtoCollectionMother::createWithCount(3);

        // Act
        $result = UserListItemDTOTransformer::fromDTOCollectionToArray($collection);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(3, $result);

        foreach ($result as $index => $item) {
            $this->assertIsArray($item);
            $this->assertArrayHasKey('id', $item);
            $this->assertEquals($index + 1, $item['id']);
            $this->assertArrayHasKey('email', $item);
            $this->assertEquals('user' . ($index + 1) . '@example.com', $item['email']);
            $this->assertArrayHasKey('first_name', $item);
            $this->assertEquals('User ' . ($index + 1), $item['first_name']);
            $this->assertArrayHasKey('actions', $item);
        }
    }

    public function testFromPaginatedDTOToMinimalArray(): void
    {
        $paginatedCollection = PaginatedUserListItemDtoCollectionMother::createWithCount(3, 10);
        $result = UserListItemDTOTransformer::fromDTOCollectionToMinimalArray(
            $paginatedCollection->getCollection()
        );

        $this->assertIsArray($result);

        foreach ($result as $index => $item) {
            $this->assertIsArray($item);
            $this->assertArrayHasKey('id', $item);
            $this->assertEquals($index + 1, $item['id']);
            $this->assertArrayHasKey('email', $item);
            $this->assertEquals('user' . ($index + 1) . '@example.com', $item['email']);
            $this->assertArrayHasKey('first_name', $item);
            $this->assertEquals('User ' . ($index + 1), $item['first_name']);
        }
    }

    /**
     * Test that fromPaginatedDTOCollectionToMinimalArray correctly
     * transforms a PaginatedUserListItemDTOCollection to a minimal array.
     */
    public function testFromPaginatedDTOCollectionToMinimalArray(): void
    {
        // Arrange
        $paginatedCollection = PaginatedUserListItemDtoCollectionMother::createWithCount(3, 10);

        // Act
        $result = UserListItemDTOTransformer::fromPaginatedDTOCollectionToMinimalArray($paginatedCollection);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('users', $result);
        $this->assertArrayHasKey('total', $result);

        $this->assertIsArray($result['users']);
        $this->assertCount(3, $result['users']);
        $this->assertEquals(10, $result['total']);

        // Verify each user has only minimal fields (id, email, name, lastName)
        foreach ($result['users'] as $index => $item) {
            $this->assertIsArray($item);
            $this->assertArrayHasKey('id', $item);
            $this->assertEquals($index + 1, $item['id']);
            $this->assertArrayHasKey('email', $item);
            $this->assertEquals('user' . ($index + 1) . '@example.com', $item['email']);
            $this->assertArrayHasKey('first_name', $item);
            $this->assertEquals('User ' . ($index + 1), $item['first_name']);
            $this->assertArrayHasKey('last_name', $item);

            // Verify it doesn't have full fields (avatar, roles, isActive, points, actions)
            $this->assertArrayNotHasKey('avatar', $item);
            $this->assertArrayNotHasKey('roles', $item);
            $this->assertArrayNotHasKey('isActive', $item);
            $this->assertArrayNotHasKey('points', $item);
            $this->assertArrayNotHasKey('actions', $item);
        }
    }
}
