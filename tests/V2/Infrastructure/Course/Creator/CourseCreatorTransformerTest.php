<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Course\Creator;

use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\Tests\V2\Mother\Course\Creator\CreatorMother;
use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\Creator;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Course\Creator\CourseCreatorTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class CourseCreatorTransformerTest extends TestCase
{
    #[DataProvider('provideFromCreatorToArray')]
    public function testFromCreatorToArray(Creator $creator, array $result): void
    {
        $this->assertEquals($result, CourseCreatorTransformer::fromCreatorToArray($creator));
    }

    public static function provideFromCreatorToArray(): \Generator
    {
        yield 'creator 1' => [
            'creator' => CreatorMother::create(
                id: new Id(1),
                email: EmailMother::create(email: '<EMAIL>'),
                firstName: 'test',
                lastName: 'transform'
            ),
            'result' => [
                'id' => 1,
                'name' => 'test',
                'lastName' => 'transform',
                'email' => '<EMAIL>',
            ],
        ];
    }

    #[DataProvider('provideFromCollectionToArray')]
    public function testFromCollectionToArray(CourseCreatorCollection $collection, array $expectedResult): void
    {
        $this->assertEquals($expectedResult, CourseCreatorTransformer::fromCollectionToArray($collection));
    }

    public static function provideFromCollectionToArray(): \Generator
    {
        yield 'no course creator' => [
            'collection' => new CourseCreatorCollection([]),
            'expectedResult' => [],
        ];

        yield 'one course creator' => [
            'collection' => new CourseCreatorCollection([
                CourseCreatorMother::create(
                    userId: new Id(1),
                    courseId: new Id(1)
                )->setCreator(
                    CreatorMother::create(
                        id: new Id(1),
                        email: EmailMother::create(email: '<EMAIL>'),
                        firstName: 'test',
                        lastName: 'transform'
                    )
                ),
            ]),
            'expectedResult' => [
                [
                    'id' => 1,
                    'name' => 'test',
                    'lastName' => 'transform',
                    'email' => '<EMAIL>',
                ],
            ],
        ];

        yield 'course creator when no creator is defined' => [
            'collection' => new CourseCreatorCollection([
                CourseCreatorMother::create(
                    userId: new Id(1),
                    courseId: new Id(1)
                ),
                CourseCreatorMother::create(
                    userId: new Id(2),
                    courseId: new Id(1)
                )->setCreator(
                    CreatorMother::create(
                        id: new Id(2),
                        email: EmailMother::create(email: '<EMAIL>'),
                        firstName: 'test2',
                        lastName: 'transform'
                    )
                ),
            ]),
            'expectedResult' => [
                [
                    'id' => 1,
                ],
                [
                    'id' => 2,
                    'name' => 'test2',
                    'lastName' => 'transform',
                    'email' => '<EMAIL>',
                ],
            ],
        ];

        yield 'two course creators' => [
            'collection' => new CourseCreatorCollection([
                CourseCreatorMother::create(
                    userId: new Id(1),
                    courseId: new Id(1)
                )->setCreator(
                    CreatorMother::create(
                        id: new Id(1),
                        email: EmailMother::create(email: '<EMAIL>'),
                        firstName: 'test1',
                        lastName: 'transform'
                    )
                ),
                CourseCreatorMother::create(
                    userId: new Id(2),
                    courseId: new Id(1)
                )->setCreator(
                    CreatorMother::create(
                        id: new Id(2),
                        email: EmailMother::create(email: '<EMAIL>'),
                        firstName: 'test2',
                        lastName: 'transform'
                    )
                ),
            ]),
            'expectedResult' => [
                [
                    'id' => 1,
                    'name' => 'test1',
                    'lastName' => 'transform',
                    'email' => '<EMAIL>',
                ],
                [
                    'id' => 2,
                    'name' => 'test2',
                    'lastName' => 'transform',
                    'email' => '<EMAIL>',
                ],
            ],
        ];
    }
}
