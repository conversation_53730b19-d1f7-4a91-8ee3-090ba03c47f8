<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class UuidValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('provideValid')]
    public function testValid(string $uuid): void
    {
        $this->expectNotToPerformAssertions();
        UuidValidator::validateUuid($uuid);
    }

    /**
     * @throws InvalidUuidException
     */
    public static function provideValid(): \Generator
    {
        yield 'valid uuid' => [
            'uuid' => UuidMother::create()->value(),
        ];

        yield 'other valid uuid' => [
            'uuid' => UuidMother::create()->value(),
        ];
    }

    #[DataProvider('provideInvalid')]
    public function testInvalid(string $uuid, array $violations): void
    {
        try {
            UuidValidator::validateUuid($uuid);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalid(): \Generator
    {
        yield 'invalid uuid' => [
            'uuid' => 'invalid uuid',
            'violations' => [
                '[uuid]' => 'This is not a valid UUID.',
            ],
        ];

        yield 'another invalid uuid' => [
            'uuid' => '123414',
            'violations' => [
                '[uuid]' => 'This is not a valid UUID.',
            ],
        ];
    }
}
