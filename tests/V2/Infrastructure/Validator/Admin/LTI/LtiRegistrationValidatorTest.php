<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin\LTI;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiRegistrationValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class LtiRegistrationValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('providePostRegistrationValidation')]
    public function testPostRegistrationValidation(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        LtiRegistrationValidator::validatePostRegistration($payload);
    }

    public static function providePostRegistrationValidation(): \Generator
    {
        yield 'valid data 1' => [
            'payload' => [
                'name' => 'Test Name',
                'client_id' => 'Client ID 1',
            ],
        ];

        yield 'valid data 2' => [
            'payload' => [
                'name' => 'Test Name',
                'client_id' => 'Client ID 1',
            ],
        ];
    }

    #[DataProvider('provideInvalidPostRegistrationValidation')]
    public function testInvalidPostRegistrationValidation(array $payload, array $violations): void
    {
        try {
            LtiRegistrationValidator::validatePostRegistration($payload);
            $this->fail('Expected exception not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidPostRegistrationValidation(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[client_id]' => 'This field is missing.',
            ],
        ];

        yield 'only name' => [
            'payload' => [
                'name' => 'Test Name',
            ],
            'violations' => [
                '[client_id]' => 'This field is missing.',
            ],
        ];

        yield 'only client_id' => [
            'payload' => [
                'client_id' => 'Test ClientId',
            ],
            'violations' => [
                '[name]' => 'This field is missing.',
            ],
        ];

        yield 'name is not string' => [
            'payload' => [
                'name' => 1,
                'client_id' => 'Test ClientId',
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];

        yield 'client id is not string' => [
            'payload' => [
                'name' => 'Test name',
                'client_id' => 1,
            ],
            'violations' => [
                '[client_id]' => 'This value should be of type string.',
            ],
        ];

        yield 'empty name and client_id' => [
            'payload' => [
                'name' => '',
                'client_id' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[client_id]' => 'This value should not be blank.',
            ],
        ];
    }

    /**
     * @throws ValidatorException
     */
    #[DataProvider('provideValidateGetRegistrations')]
    public function testValidateGetRegistrations(array $payload): void
    {
        $this->expectNotToPerformAssertions();
        LtiRegistrationValidator::validateGetRegistrations($payload);
    }

    /**
     * @throws InvalidUuidException
     */
    public static function provideValidateGetRegistrations(): \Generator
    {
        yield 'valid id' => [
            'payload' => [
                'id' => UuidMother::create()->value(),
            ],
        ];

        yield 'only name' => [
            'payload' => [
                'name' => 'Test Name',
            ],
        ];

        yield 'only client_id' => [
            'payload' => [
                'client_id' => 'Test Client id',
            ],
        ];
    }

    #[DataProvider('provideInvalidGetRegistrationsPayload')]
    public function testInvalidGetRegistrationsPayload(array $payload, array $violations): void
    {
        try {
            LtiRegistrationValidator::validateGetRegistrations($payload);
            $this->fail('Expected exception not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidGetRegistrationsPayload(): \Generator
    {
        yield 'empty id' => [
            'payload' => [
                'id' => 'This value should not be blank.',
            ],
            'violations' => [
                '[id]' => 'This is not a valid UUID.',
            ],
        ];

        yield 'invalid id' => [
            'payload' => [
                'id' => 'invalid-uuid',
            ],
            'violations' => [
                '[id]' => 'This is not a valid UUID.',
            ],
        ];

        yield 'invalid type id' => [
            'payload' => [
                'id' => 1,
            ],
            'violations' => [
                '[id]' => ['This is not a valid UUID.', 'This value should be of type string.'],
            ],
        ];

        yield 'empty name' => [
            'payload' => [
                'name' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
            ],
        ];

        yield 'invalid type name' => [
            'payload' => [
                'name' => 1,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];

        yield 'empty client id' => [
            'payload' => [
                'client_id' => '',
            ],
            'violations' => [
                '[client_id]' => 'This value should not be blank.',
            ],
        ];

        yield 'invalid type client id' => [
            'payload' => [
                'client_id' => 1,
            ],
            'violations' => [
                '[client_id]' => 'This value should be of type string.',
            ],
        ];
    }

    /**
     * @throws ValidatorException
     */
    #[DataProvider('provideValidateGetRegistration')]
    public function testValidateGetRegistration(array $payload)
    {
        $this->expectNotToPerformAssertions();
        LtiRegistrationValidator::validateGetRegistration($payload);
    }

    public static function provideValidateGetRegistration(): \Generator
    {
        yield 'platform' => [
            'payload' => [
                'platform' => 'true',
            ],
        ];

        yield 'tool' => [
            'payload' => [
                'tool' => 'true',
            ],
        ];

        yield 'deployments' => [
            'payload' => [
                'deployments' => 'true',
            ],
        ];

        yield 'all' => [
            'payload' => [
                'platform' => 'true',
                'tool' => 'true',
                'deployments' => 'true',
            ],
        ];
    }

    #[DataProvider('provideInvalidValidateGetRegistration')]
    public function testInvalidValidateGetRegistration(array $payload, array $violations): void
    {
        try {
            LtiRegistrationValidator::validateGetRegistration($payload);
            $this->fail('Expected exception not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidValidateGetRegistration(): \Generator
    {
        yield 'invalid choices' => [
            'payload' => [
                'platform' => 'one',
                'tool' => 'two',
                'deployments' => 'three',
            ],
            'violations' => [
                '[platform]' => 'The value you selected is not a valid choice.',
                '[tool]' => 'The value you selected is not a valid choice.',
                '[deployments]' => 'The value you selected is not a valid choice.',
            ],
        ];

        yield 'invalid type' => [
            'payload' => [
                'platform' => true,
                'tool' => false,
                'deployments' => true,
            ],
            'violations' => [
                '[platform]' => [
                    'The value you selected is not a valid choice.',
                ],
                '[tool]' => [
                    'The value you selected is not a valid choice.',
                ],
                '[deployments]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];
    }
}
