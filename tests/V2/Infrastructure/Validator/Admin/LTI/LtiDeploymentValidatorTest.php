<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin\LTI;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiDeploymentValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class LtiDeploymentValidatorTest extends ValidatorTestCase
{
    public function testPostValidate(): void
    {
        $this->expectNotToPerformAssertions();

        LtiDeploymentValidator::validatePostLtiDeployment([
            'name' => 'Deployment 1',
            'deployment_id' => 'deployment-1',
        ]);
    }

    #[DataProvider('providePostInvalid')]
    public function testPostInvalid(array $payload, array $violations)
    {
        try {
            LtiDeploymentValidator::validatePostLtiDeployment($payload);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function providePostInvalid(): \Generator
    {
        yield 'empty body' => [
            'payload' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[deployment_id]' => 'This field is missing.',
            ],
        ];

        yield 'empty values' => [
            'payload' => [
                'name' => '',
                'deployment_id' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[deployment_id]' => 'This value should not be blank.',
            ],
        ];

        yield 'wrong datatype' => [
            'payload' => [
                'name' => 1,
                'deployment_id' => 1,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
                '[deployment_id]' => 'This value should be of type string.',
            ],
        ];
    }
}
