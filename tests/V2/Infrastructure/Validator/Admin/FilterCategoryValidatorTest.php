<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\FilterCategoryValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class FilterCategoryValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('provideValidateGetFilterCategories')]
    public function testValidateGetFilterCategories(array $data): void
    {
        $this->expectNotToPerformAssertions();
        FilterCategoryValidator::validateGetFilterCategories($data);
    }

    public static function provideValidateGetFilterCategories(): \Generator
    {
        yield 'empty body' => [
            'data' => [],
        ];

        yield 'with name' => [
            'data' => [
                'name' => 'Value',
            ],
        ];

        yield 'with parent id' => [
            'data' => [
                'parent_id' => '1',
            ],
        ];
    }

    #[DataProvider('provideInvalidValidateGetFilterCategories')]
    public function testInvalidValidateGetFilterCategories(array $data, array $violations): void
    {
        try {
            FilterCategoryValidator::validateGetFilterCategories($data);
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function provideInvalidValidateGetFilterCategories(): \Generator
    {
        yield 'empty name' => [
            'data' => [
                'name' => '',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
            ],
        ];
        yield 'invalid name type' => [
            'data' => [
                'name' => 1212,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];

        yield 'parent id less than 1' => [
            'data' => [
                'parent_id' => '0',
            ],
            'violations' => [
                '[parent_id]' => 'parent_id must be greater than 0.',
            ],
        ];

        yield 'parent id as string' => [
            'data' => [
                'parent_id' => 'a',
            ],
            'violations' => [
                '[parent_id]' => 'This value should be of type digit.',
            ],
        ];
    }
}
