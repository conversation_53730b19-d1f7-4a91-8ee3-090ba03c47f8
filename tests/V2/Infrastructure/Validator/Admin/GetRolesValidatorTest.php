<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\GetRolesValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetRolesValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('roleValidatorSuccessProvider')]
    public function testRolesValidatorSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        GetRolesValidator::validateGetRolesRequest($payload);
    }

    public static function roleValidatorSuccessProvider(): \Generator
    {
        yield 'payload with the correct value' => [
            [
                'allowed_to_manage' => 'true',
            ],
        ];
    }

    #[DataProvider('roleValidatorFailProvider')]
    public function testRolesValidatorFail(array $payload, array $violations): void
    {
        try {
            GetRolesValidator::validateGetRolesRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function roleValidatorFailProvider(): \Generator
    {
        yield 'payload with the false value' => [
            [
                'allowed_to_manage' => 'false',
            ],
            [
                '[allowed_to_manage]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];

        yield 'payload with wrong value' => [
            [
                'allowed_to_manage' => 'letters',
            ],
            [
                '[allowed_to_manage]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];
        yield 'payload with wrong value and symbols' => [
            [
                'allowed_to_manage' => 'symbols!@#$%^&*()_+-=[]{}|;:\'",.<>?/',
            ],
            [
                '[allowed_to_manage]' => [
                    'The value you selected is not a valid choice.',
                ],
            ],
        ];
    }
}
