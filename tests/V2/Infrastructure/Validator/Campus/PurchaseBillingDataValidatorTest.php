<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Campus;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Campus\PurchaseBillingDataValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class PurchaseBillingDataValidatorTest extends ValidatorTestCase
{
    /**
     * Test basic functionality - validator correctly validates purchase billing data.
     *
     * @throws ValidatorException
     */
    #[DataProvider('validateSuccessProvider')]
    public function testValidateSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        PurchaseBillingDataValidator::validatePurchaseBillingData($payload);
    }

    public static function validateSuccessProvider(): \Generator
    {
        yield 'Valid complete billing data' => [
            [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
                'save_as_default' => true,
            ],
        ];

        yield 'Valid billing data with 3-letter country code' => [
            [
                'tin' => 'B12345678',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'address' => '456 Oak Avenue',
                'postal_code' => '67890',
                'city' => 'Barcelona',
                'country' => 'ESP',
                'metadata' => ['company' => 'Test Corp'],
                'save_as_default' => false,
            ],
        ];

        yield 'Valid billing data without optional save_as_default' => [
            [
                'tin' => 'X1234567L',
                'first_name' => 'Carlos',
                'last_name' => 'García',
                'address' => '789 Pine Street',
                'postal_code' => '54321',
                'city' => 'Valencia',
                'country' => 'ES',
                'metadata' => [],
            ],
        ];

        yield 'Valid billing data with maximum length fields' => [
            [
                'tin' => str_repeat('A', 50),
                'first_name' => str_repeat('B', 100),
                'last_name' => str_repeat('C', 100),
                'address' => str_repeat('D', 255),
                'postal_code' => str_repeat('E', 20),
                'city' => str_repeat('F', 100),
                'country' => 'US',
                'metadata' => ['company' => 'Test Company', 'department' => 'IT'],
                'save_as_default' => true,
            ],
        ];

        yield 'Valid billing data with complex metadata' => [
            [
                'tin' => '98765432B',
                'first_name' => 'María',
                'last_name' => 'López',
                'address' => 'Calle Mayor 123',
                'postal_code' => '28001',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [
                    'company' => 'Example S.L.',
                    'department' => 'IT',
                    'notes' => 'Special billing requirements',
                ],
                'save_as_default' => false,
            ],
        ];

        yield 'Valid billing data with save_as_default true' => [
            [
                'tin' => '87654321C',
                'first_name' => 'Ana',
                'last_name' => 'Martín',
                'address' => 'Avenida Principal 456',
                'postal_code' => '08001',
                'city' => 'Barcelona',
                'country' => 'ES',
                'metadata' => [],
                'save_as_default' => true,
            ],
        ];
    }

    /**
     * Test comprehensive validation coverage and error handling.
     */
    #[DataProvider('validateFailProvider')]
    public function testValidateFail(array $payload, array $violations): void
    {
        try {
            PurchaseBillingDataValidator::validatePurchaseBillingData($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function validateFailProvider(): \Generator
    {
        yield 'Empty body' => [
            [],
            [
                '' => 'Body cannot be empty',
                '[tin]' => 'This field is missing.',
                '[first_name]' => 'This field is missing.',
                '[last_name]' => 'This field is missing.',
                '[address]' => 'This field is missing.',
                '[postal_code]' => 'This field is missing.',
                '[city]' => 'This field is missing.',
                '[country]' => 'This field is missing.',
                '[metadata]' => 'This field is missing.',
            ],
        ];

        yield 'Missing required tin field' => [
            [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            [
                '[tin]' => 'This field is missing.',
            ],
        ];

        yield 'Empty tin field' => [
            [
                'tin' => '',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            [
                '[tin]' => 'This value should not be blank.',
            ],
        ];

        yield 'Invalid tin type' => [
            [
                'tin' => 123,
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            [
                '[tin]' => 'This value should be of type string.',
            ],
        ];

        yield 'Tin too long' => [
            [
                'tin' => str_repeat('A', 51),
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            [
                '[tin]' => 'This value is too long. It should have 50 characters or less.',
            ],
        ];

        yield 'Invalid country format - lowercase' => [
            [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'es',
                'metadata' => [],
            ],
            [
                '[country]' => 'Country must be a valid ISO country code (2-3 uppercase letters)',
            ],
        ];

        yield 'Invalid metadata type' => [
            [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => 'invalid',
            ],
            [
                '[metadata]' => 'This value should be of type array.',
            ],
        ];

        yield 'Invalid save_as_default type' => [
            [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
                'save_as_default' => 'true',
            ],
            [
                '[save_as_default]' => 'This value should be of type bool.',
            ],
        ];
    }
}
