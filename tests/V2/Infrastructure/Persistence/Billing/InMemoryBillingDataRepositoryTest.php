<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Billing;

use App\Tests\V2\Domain\Billing\BillingDataRepositoryTestCase;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Infrastructure\Persistence\Billing\InMemoryBillingDataRepository;

class InMemoryBillingDataRepositoryTest extends BillingDataRepositoryTestCase
{
    protected function getRepository(): BillingDataRepository
    {
        return new InMemoryBillingDataRepository();
    }
}
