<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase\Payment;

use App\Tests\V2\Domain\Purchase\Payment\PaymentRepositoryTestCase;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Infrastructure\Persistence\Purchase\Payment\InMemoryPaymentRepository;

class InMemoryPaymentRepositoryTest extends PaymentRepositoryTestCase
{
    protected function getRepository(): PaymentRepository
    {
        return new InMemoryPaymentRepository();
    }
}
