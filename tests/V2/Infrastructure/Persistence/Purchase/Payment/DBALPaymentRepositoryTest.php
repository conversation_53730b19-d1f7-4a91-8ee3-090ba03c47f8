<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Purchase\Payment;

use App\Tests\V2\Domain\Purchase\Payment\PaymentRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Infrastructure\Persistence\Purchase\Payment\DBALPaymentRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALPaymentRepositoryTest extends PaymentRepositoryTestCase
{
    private const string TABLE_NAME = 'payment';
    private Connection $connection;

    /**
     * @throws Exception
     * @throws SchemaException
     */
    protected function getRepository(): PaymentRepository
    {
        [$this->connection] = DBALConnectionFactory::create();

        $this->createTable();

        return new DBALPaymentRepository(
            $this->connection,
            self::TABLE_NAME
        );
    }

    /**
     * @throws SchemaException
     * @throws Exception
     */
    private function createTable(): void
    {
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('purchase_id', 'string', ['length' => 36]);
        $table->addColumn('user_id', 'string', ['length' => 36]);
        $table->addColumn('payment_intent', 'string', ['length' => 255]);
        $table->addColumn('amount', 'integer');
        $table->addColumn('currency_code', 'string', ['length' => 3]);
        $table->addColumn('status', 'string', ['length' => 50]);
        $table->addColumn('created_at', 'datetime');
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);
        $table->setPrimaryKey(['id']);
        $table->addIndex(['purchase_id'], 'idx_payment_purchase_id');
        $table->addIndex(['user_id'], 'idx_payment_user_id');
        $table->addIndex(['payment_intent'], 'idx_payment_payment_intent');
        $table->addIndex(['status'], 'idx_payment_status');
        $table->addIndex(['amount'], 'idx_payment_amount');
        $table->addIndex(['created_at'], 'idx_payment_created_at');
        $table->addIndex(['deleted_at'], 'idx_payment_deleted_at');

        $this->connection->createSchemaManager()->createTable($table);
    }
}
