
### Login
POST {{host}}/api/login
Content-Type: application/json

{
    "email": "{{username}}",
    "password": "{{password}}"
}

> {%
    client.global.clearAll();
    client.test("Request executed successfully", function () {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === 200, "Response status is not 200");
    });
    client.test("Response contains valid data", function () {
        client.assert(response.body.data.token !== undefined, "Token is not defined");
        client.assert(response.body.data.refreshToken !== undefined, "Refresh token is not defined");
        client.assert(response.body.data.hasOwnProperty("user"), "User data is not defined");
    });
    client.global.set("auth_token", response.body.data.token);
    client.global.set("username", response.body.data.user.email);
    client.global.set("firstName", response.body.data.user.firstName);
    client.global.set("lastName", response.body.data.user.lastName);
%}

### Get profile
GET {{host}}/api/profile
Content-Type: application/json
Authorization: Bearer {{auth_token}}

> {%
    client.test("Request executed successfully", function () {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === 200, "Response status is not 200");
    });
    client.test("Check user data", function () {
        client.assert(response.body.data.email === client.global.get("username"), "Email is not correct");
        client.assert(response.body.data.firstName === client.global.get("firstName"), "Name is not correct");
        client.assert(response.body.data.lastName === client.global.get("lastName"), "Last name is not correct");
    });
%}

### Logout
GET {{host}}/api/logout
Content-Type: application/json
Authorization: Bearer {{auth_token}}

> {%
    client.test("Request executed successfully", function () {
        client.assert(response.status === 200, "Response status is not 200");
        client.assert(response.body.status === 200, "Response status is not 200");
        client.assert(response.body.data === "User successfully logout", "Logout message is not correct");
    });
%}