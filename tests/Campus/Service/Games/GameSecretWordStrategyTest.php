<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\SecretWord;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameSecretWordStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new SecretWord($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'questionId' => 1,
                'time' => 10,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 1,
            ],
        ];

        $attempts = [
            [
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 4,
            ],
            [
                'questionId' => 2,
                'time' => 5,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 1,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ok with 1 red letter. With 0 previous attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.96,
        ];

        yield 'result ok with 1 red letter. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 4,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.86,
        ];

        $answers = [
            [
                'questionId' => 1,
                'time' => 10,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 3,
            ],
        ];

        $attempts = [
            [
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 4,
            ],
            [
                'questionId' => 2,
                'time' => 5,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 1,
            ],            [
                'questionId' => 3,
                'time' => 5,
                'correct' => true,
                'corrects' => 4,
                'incorrects' => 4,
            ],
        ];

        yield 'result ok with 3 red letter. With 3 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 4,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.76,
        ];
    }
}
