<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\StatsUser;

use App\Enum\AnnouncementState;
use App\Repository\UserCourseRepository;
use App\Service\Api\ApiCourseService;
use App\Service\StatsUser\StatsUserService;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserMother;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class StatsUserServiceTest extends TestCase
{
    private StatsUserService|MockObject $service;

    protected function setUp(): void
    {
        // Create a minimal service instance just for testing the state logic
        $this->service = $this->getMockBuilder(StatsUserService::class)
            ->disableOriginalConstructor()
            ->onlyMethods([]) // Don't mock any methods
            ->getMock();
    }

    /**
     * @dataProvider getStateFromCourseDataProvider
     */
    public function testGetStateFromCourse(array $courseData, string $expectedState): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getStateFromCourse');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, $courseData);

        $this->assertEquals($expectedState, $result);
    }

    public function testGetOrphanedCoursesForUser(): void
    {
        // Create test data using Mothers
        $user = UserMother::create(
            id: 1,
            firstName: 'Test',
            lastName: 'User'
        );
        $user->setLocale('en');

        $course1 = CourseMother::create(
            id: 10,
            name: 'Orphaned Course 1'
        );

        $course2 = CourseMother::create(
            id: 20,
            name: 'Orphaned Course 2'
        );

        $orphanedCourses = [$course1, $course2];
        $excludeCourseIds = [1, 2, 3];

        // Create mocks
        $userCourseRepository = $this->createMock(UserCourseRepository::class);
        $apiCourseService = $this->createMock(ApiCourseService::class);

        // Mock the repository to return orphaned courses
        $userCourseRepository
            ->expects($this->once())
            ->method('findOrphanedCoursesWithProgressForUser')
            ->with($user, $excludeCourseIds, 'en')
            ->willReturn($orphanedCourses);

        // Mock the API service to transform courses
        $expectedCourseData1 = ['id' => 10, 'name' => 'Orphaned Course 1'];
        $expectedCourseData2 = ['id' => 20, 'name' => 'Orphaned Course 2'];

        $apiCourseService
            ->expects($this->exactly(2))
            ->method('courseToSend')
            ->willReturnCallback(function ($userParam, $courseParam) use ($course1, $course2, $expectedCourseData1, $expectedCourseData2) {
                if ($courseParam === $course1) {
                    return $expectedCourseData1;
                }
                if ($courseParam === $course2) {
                    return $expectedCourseData2;
                }

                return [];
            });

        // Test the logic directly - simulate what getOrphanedCoursesForUser does
        $orphanedCoursesFromRepo = $userCourseRepository->findOrphanedCoursesWithProgressForUser($user, $excludeCourseIds, $user->getLocale());

        $result = [];
        foreach ($orphanedCoursesFromRepo as $course) {
            $courseData = $apiCourseService->courseToSend($user, $course);
            $courseData['orphaned'] = true;
            $result[] = $courseData;
        }

        // Assertions
        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertEquals(['id' => 10, 'name' => 'Orphaned Course 1', 'orphaned' => true], $result[0]);
        $this->assertEquals(['id' => 20, 'name' => 'Orphaned Course 2', 'orphaned' => true], $result[1]);
        $this->assertTrue($result[0]['orphaned']);
        $this->assertTrue($result[1]['orphaned']);
    }

    public static function getStateFromCourseDataProvider(): array
    {
        return [
            'finished course with finished flag' => [
                ['finished' => true, 'startAt' => '2024-01-01', 'finishAt' => '2024-01-02'],
                AnnouncementState::STATE_FINISHED,
            ],
            'not started course' => [
                ['finished' => false, 'startAt' => null, 'finishAt' => null],
                AnnouncementState::STATE_NOT_STARTED,
            ],
            'in progress course' => [
                ['finished' => false, 'startAt' => '2024-01-01', 'finishAt' => null],
                AnnouncementState::STATE_IN_PROGRESS,
            ],
            'finished course with finish date' => [
                ['finished' => false, 'startAt' => '2024-01-01', 'finishAt' => '2024-01-02'],
                AnnouncementState::STATE_FINISHED,
            ],
            'edge case - no data' => [
                [],
                AnnouncementState::STATE_NOT_STARTED,
            ],
        ];
    }
}
