<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\DateFormatter;

use App\Entity\User;
use App\Service\DateFormatter\DateFormatterService;
use App\Service\SettingsService;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Security;

class DateFormatterServiceTest extends TestCase
{
    private Security|MockObject $security;
    private ParameterBagInterface|MockObject $parameterBag;
    private SettingsService|MockObject $settingsService;
    private DateFormatterService $dateFormatterService;

    protected function setUp(): void
    {
        $this->security = $this->createMock(Security::class);
        $this->parameterBag = $this->createMock(ParameterBagInterface::class);
        $this->settingsService = $this->createMock(SettingsService::class);

        // Set default parameter bag behavior
        $this->parameterBag->method('get')
            ->with('app.defaultLanguage', 'es')
            ->willReturn('es');

        // Set default settings service behavior - only return the 4 languages actually supported in EasyLearning
        $this->settingsService->method('get')
            ->willReturnCallback(function ($key) {
                if ('app.languages' === $key) {
                    return ['es', 'en', 'fr', 'pt']; // Only the 4 real EasyLearning languages
                }

                return null;
            });

        $this->dateFormatterService = new DateFormatterService($this->security, $this->parameterBag, $this->settingsService);
    }

    /**
     * Test formatDate with different format styles.
     *
     * @dataProvider formatStyleProvider
     */
    public function testFormatDateWithDifferentStyles(string $format): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatDate($testDate, $format, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    public static function formatStyleProvider(): \Generator
    {
        yield 'Short format' => ['short'];
        yield 'Medium format' => ['medium'];
        yield 'Long format' => ['long'];
        yield 'Full format' => ['full'];
    }

    /**
     * Test formatDate with different locales.
     *
     * @dataProvider localeProvider
     */
    public function testFormatDateWithDifferentLocales(string $locale): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $format = 'short';

        // Act
        $result = $this->dateFormatterService->formatDate($testDate, $format, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
        $this->assertMatchesRegularExpression('/\d/', $result, 'Should contain numeric date elements');
    }

    public static function localeProvider(): \Generator
    {
        yield 'Spanish' => ['es'];
        yield 'English' => ['en'];
        yield 'French' => ['fr'];
        yield 'Portuguese' => ['pt'];
    }

    /**
     * Test formatDateTime with different date and time formats.
     */
    public function testFormatDateTime(): void
    {
        // Arrange
        $testDateTime = new \DateTimeImmutable('2024-01-15 14:30:00');
        $dateFormat = 'short';
        $timeFormat = 'short';
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatDateTime($testDateTime, $dateFormat, $timeFormat, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
        $this->assertMatchesRegularExpression('/\d/', $result, 'Should contain numeric elements');
    }

    /**
     * Test formatWithPattern with custom ICU pattern.
     */
    public function testFormatWithPattern(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $pattern = 'dd/MM/yyyy';
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatWithPattern($testDate, $pattern, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertEquals('15/01/2024', $result);
    }

    /**
     * Test formatWithPattern with different patterns.
     *
     * @dataProvider patternProvider
     */
    public function testFormatWithPatternDifferentPatterns(string $pattern, string $expectedRegex): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatWithPattern($testDate, $pattern, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertMatchesRegularExpression($expectedRegex, $result);
    }

    public static function patternProvider(): \Generator
    {
        yield 'Date only' => ['dd/MM/yyyy', '/^\d{2}\/\d{2}\/\d{4}$/'];
        yield 'Date with time' => ['dd/MM/yyyy HH:mm', '/^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/'];
        yield 'Month year' => ['MM/yyyy', '/^\d{2}\/\d{4}$/'];
        yield 'Day month' => ['dd MMMM', '/^\d{2} \w+$/'];
    }

    /**
     * Test formatRelativeDate.
     */
    public function testFormatRelativeDate(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatRelativeDate($testDate, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test getUserLocale when user is available.
     */
    public function testGetUserLocaleWithUser(): void
    {
        // Arrange
        $user = $this->createMock(User::class);
        $user->method('getLocaleCampus')->willReturn('fr');

        $this->security->method('getUser')->willReturn($user);

        // Act
        $result = $this->dateFormatterService->formatDate(new \DateTimeImmutable(), 'short');

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test getUserLocale when no user is available (fallback to default).
     */
    public function testGetUserLocaleWithoutUser(): void
    {
        // Arrange
        $this->security->method('getUser')->willReturn(null);

        // Act
        $result = $this->dateFormatterService->formatDate(new \DateTimeImmutable(), 'short');

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test getLocaleMap with different locales.
     *
     * @dataProvider localeMapProvider
     */
    public function testGetLocaleMap(string $inputLocale): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Act
        $result = $this->dateFormatterService->formatDate($testDate, 'short', $inputLocale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
        // The exact format depends on IntlDateFormatter, so we just verify it's a valid date string
        $this->assertMatchesRegularExpression('/\d/', $result);
    }

    public static function localeMapProvider(): \Generator
    {
        yield 'Spanish to es_ES' => ['es'];
        yield 'English to en_US' => ['en'];
        yield 'French to fr_FR' => ['fr'];
        yield 'Portuguese to pt_PT' => ['pt'];
    }

    /**
     * Test default format parameter.
     */
    public function testDefaultFormatParameter(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $locale = 'es';

        // Act - calling without format parameter should use 'medium' as default
        $result = $this->dateFormatterService->formatDate($testDate, locale: $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test invalid format parameter fallback.
     */
    public function testInvalidFormatParameterFallback(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $locale = 'es';
        $invalidFormat = 'invalid_format';

        // Act
        $result = $this->dateFormatterService->formatDate($testDate, $invalidFormat, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
        // Should fallback to medium format
    }

    /**
     * Test formatDateTime with different time formats.
     *
     * @dataProvider timeFormatProvider
     */
    public function testFormatDateTimeWithDifferentTimeFormats(string $timeFormat): void
    {
        // Arrange
        $testDateTime = new \DateTimeImmutable('2024-01-15 14:30:45');
        $dateFormat = 'short';
        $locale = 'es';

        // Act
        $result = $this->dateFormatterService->formatDateTime($testDateTime, $dateFormat, $timeFormat, $locale);

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    public static function timeFormatProvider(): \Generator
    {
        yield 'Short time' => ['short'];
        yield 'Medium time' => ['medium'];
        yield 'Long time' => ['long'];
        yield 'Full time' => ['full'];
    }

    /**
     * Test that different locales produce different outputs.
     */
    public function testDifferentLocalesProduceDifferentOutputs(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');
        $format = 'medium';

        // Act
        $spanishResult = $this->dateFormatterService->formatDate($testDate, $format, 'es');
        $englishResult = $this->dateFormatterService->formatDate($testDate, $format, 'en');

        // Assert
        $this->assertIsString($spanishResult);
        $this->assertIsString($englishResult);
        $this->assertNotEquals($spanishResult, $englishResult, 'Different locales should produce different formats');
    }

    /**
     * Test consistency - same input should always produce same output.
     */
    public function testConsistency(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');
        $format = 'short';
        $locale = 'es';

        // Act
        $result1 = $this->dateFormatterService->formatDate($testDate, $format, $locale);
        $result2 = $this->dateFormatterService->formatDate($testDate, $format, $locale);

        // Assert
        $this->assertEquals($result1, $result2, 'Same input should always produce same output');
    }

    /**
     * Test getUserLocale method with user having locale.
     */
    public function testGetUserLocaleWithUserLocale(): void
    {
        // Arrange
        $user = $this->createMock(User::class);
        $user->method('getLocale')->willReturn('fr');

        $this->security->method('getUser')->willReturn($user);

        // Act
        $result = $this->dateFormatterService->getUserLocale();

        // Assert
        $this->assertEquals('fr', $result);
    }

    /**
     * Test getUserLocale method with user having null locale (fallback to default).
     */
    public function testGetUserLocaleWithUserNullLocale(): void
    {
        // Arrange
        $user = $this->createMock(User::class);
        $user->method('getLocale')->willReturn(null);

        $this->security->method('getUser')->willReturn($user);

        // Act
        $result = $this->dateFormatterService->getUserLocale();

        // Assert
        $this->assertEquals('es', $result); // Should fallback to default
    }

    /**
     * Test getUserLocale method with no user (fallback to default).
     */
    public function testGetUserLocaleWithNoUser(): void
    {
        // Arrange
        $this->security->method('getUser')->willReturn(null);

        // Act
        $result = $this->dateFormatterService->getUserLocale();

        // Assert
        $this->assertEquals('es', $result); // Should fallback to default
    }

    /**
     * Test getDefaultLocale method.
     */
    public function testGetDefaultLocale(): void
    {
        // Act
        $result = $this->dateFormatterService->getDefaultLocale();

        // Assert
        $this->assertEquals('es', $result);
    }

    /**
     * Test getDefaultLocale with different parameter value.
     */
    public function testGetDefaultLocaleWithDifferentParameter(): void
    {
        // Arrange
        $parameterBag = $this->createMock(ParameterBagInterface::class);
        $parameterBag->method('has')
            ->with('app.defaultLanguage')
            ->willReturn(true);
        $parameterBag->method('get')
            ->with('app.defaultLanguage')
            ->willReturn('en');

        $settingsService = $this->createMock(SettingsService::class);
        $settingsService->method('get')
            ->with('app.languages')
            ->willReturn(['es', 'en', 'fr', 'pt']);

        $service = new DateFormatterService($this->security, $parameterBag, $settingsService);

        // Act
        $result = $service->getDefaultLocale();

        // Assert
        $this->assertEquals('en', $result);
    }

    /**
     * Test getMonthName method with valid inputs.
     *
     * @dataProvider validMonthProvider
     */
    public function testGetMonthNameValid(int $monthNumber): void
    {
        // Act
        $result = $this->dateFormatterService->getMonthName($monthNumber, 'es');

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test getMonthName method with invalid inputs (should throw exceptions).
     *
     * @dataProvider invalidMonthProvider
     */
    public function testGetMonthNameInvalid(int $monthNumber): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Month number must be between 1 and 12, got: $monthNumber");

        // Act
        $this->dateFormatterService->getMonthName($monthNumber, 'es');
    }

    public static function validMonthProvider(): \Generator
    {
        yield 'Valid month 1' => [1];
        yield 'Valid month 6' => [6];
        yield 'Valid month 12' => [12];
    }

    public static function invalidMonthProvider(): \Generator
    {
        yield 'Invalid month 0' => [0];
        yield 'Invalid month 13' => [13];
        yield 'Invalid month -1' => [-1];
    }

    /**
     * Test getDayName method with valid inputs.
     *
     * @dataProvider validDayProvider
     */
    public function testGetDayNameValid(int $dayNumber): void
    {
        // Act
        $result = $this->dateFormatterService->getDayName($dayNumber, 'es');

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
    }

    /**
     * Test getDayName method with invalid inputs (should throw exceptions).
     *
     * @dataProvider invalidDayProvider
     */
    public function testGetDayNameInvalid(int $dayNumber): void
    {
        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Day number must be between 1 and 7 (ISO-8601), got: $dayNumber");

        // Act
        $this->dateFormatterService->getDayName($dayNumber, 'es');
    }

    public static function validDayProvider(): \Generator
    {
        yield 'Valid day 1 (Monday)' => [1];
        yield 'Valid day 4 (Thursday)' => [4];
        yield 'Valid day 7 (Sunday)' => [7];
    }

    public static function invalidDayProvider(): \Generator
    {
        yield 'Invalid day 0' => [0];
        yield 'Invalid day 8' => [8];
        yield 'Invalid day -1' => [-1];
    }

    /**
     * Test getDayName with abbreviated option.
     */
    public function testGetDayNameAbbreviated(): void
    {
        // Act
        $fullName = $this->dateFormatterService->getDayName(1, 'es', false);
        $abbreviatedName = $this->dateFormatterService->getDayName(1, 'es', true);

        // Assert
        $this->assertIsString($fullName);
        $this->assertIsString($abbreviatedName);
        $this->assertNotEmpty($fullName);
        $this->assertNotEmpty($abbreviatedName);
        // Abbreviated should typically be shorter than full name
        $this->assertLessThanOrEqual(\strlen($fullName), \strlen($abbreviatedName));
    }

    /**
     * Test formatDateRange method.
     */
    public function testFormatDateRange(): void
    {
        // Arrange
        $startDate = new \DateTimeImmutable('2024-01-15');
        $endDate = new \DateTimeImmutable('2024-01-20');

        // Act
        $result = $this->dateFormatterService->formatDateRange($startDate, $endDate, 'short', 'es');

        // Assert
        $this->assertIsString($result);
        $this->assertNotEmpty($result);
        $this->assertStringContainsString(' - ', $result, 'Date range should contain separator');
    }

    /**
     * Test caching functionality for months.
     */
    public function testMonthNameCaching(): void
    {
        // Act - Call the same month twice
        $result1 = $this->dateFormatterService->getMonthName(3, 'es');
        $result2 = $this->dateFormatterService->getMonthName(3, 'es');

        // Assert
        $this->assertEquals($result1, $result2, 'Cached results should be identical');
        $this->assertIsString($result1);
        $this->assertNotEmpty($result1);
    }

    /**
     * Test caching functionality for days.
     */
    public function testDayNameCaching(): void
    {
        // Act - Call the same day twice
        $result1 = $this->dateFormatterService->getDayName(2, 'es');
        $result2 = $this->dateFormatterService->getDayName(2, 'es');

        // Assert
        $this->assertEquals($result1, $result2, 'Cached results should be identical');
        $this->assertIsString($result1);
        $this->assertNotEmpty($result1);
    }

    /**
     * Test that different locales produce different month names.
     */
    public function testMonthNameDifferentLocales(): void
    {
        // Act
        $spanishMonth = $this->dateFormatterService->getMonthName(1, 'es');
        $englishMonth = $this->dateFormatterService->getMonthName(1, 'en');

        // Assert
        $this->assertIsString($spanishMonth);
        $this->assertIsString($englishMonth);
        $this->assertNotEquals($spanishMonth, $englishMonth, 'Different locales should produce different month names');
    }

    /**
     * Test that different locales produce different day names.
     */
    public function testDayNameDifferentLocales(): void
    {
        // Act
        $spanishDay = $this->dateFormatterService->getDayName(1, 'es');
        $englishDay = $this->dateFormatterService->getDayName(1, 'en');

        // Assert
        $this->assertIsString($spanishDay);
        $this->assertIsString($englishDay);
        $this->assertNotEquals($spanishDay, $englishDay, 'Different locales should produce different day names');
    }

    /**
     * Test locale mapping functionality.
     */
    public function testLocaleMapping(): void
    {
        // Test that the service properly maps short locales to full locales
        // We can't directly test the private method, but we can test its effects

        $testDate = new \DateTimeImmutable('2024-01-15');

        // These should work with short locale codes (only EasyLearning supported languages)
        $result1 = $this->dateFormatterService->formatDate($testDate, 'short', 'es');
        $result2 = $this->dateFormatterService->formatDate($testDate, 'short', 'en');
        $result3 = $this->dateFormatterService->formatDate($testDate, 'short', 'fr');
        $result4 = $this->dateFormatterService->formatDate($testDate, 'short', 'pt');

        $this->assertIsString($result1);
        $this->assertIsString($result2);
        $this->assertIsString($result3);
        $this->assertIsString($result4);
        $this->assertNotEmpty($result1);
        $this->assertNotEmpty($result2);
        $this->assertNotEmpty($result3);
        $this->assertNotEmpty($result4);
    }

    /**
     * Test edge cases for formatWithPattern.
     */
    public function testFormatWithPatternEdgeCases(): void
    {
        $testDate = new \DateTimeImmutable('2024-01-15 14:30:00');

        // Test empty pattern (should not crash)
        $result1 = $this->dateFormatterService->formatWithPattern($testDate, '', 'es');
        $this->assertIsString($result1);

        // Test complex pattern
        $result2 = $this->dateFormatterService->formatWithPattern($testDate, 'EEEE, d \'de\' MMMM \'de\' yyyy', 'es');
        $this->assertIsString($result2);
        $this->assertNotEmpty($result2);
    }

    /**
     * Test that service handles DateTime and DateTimeImmutable objects.
     */
    public function testDateTimeObjectTypes(): void
    {
        // Arrange
        $dateTime = new \DateTime('2024-01-15 14:30:00');
        $dateTimeImmutable = new \DateTimeImmutable('2024-01-15 14:30:00');

        // Act
        $result1 = $this->dateFormatterService->formatDate($dateTime, 'short', 'es');
        $result2 = $this->dateFormatterService->formatDate($dateTimeImmutable, 'short', 'es');

        // Assert
        $this->assertIsString($result1);
        $this->assertIsString($result2);
        $this->assertEquals($result1, $result2, 'DateTime and DateTimeImmutable should produce same result');
    }

    /**
     * Test locale validation with invalid locale (should throw exception).
     */
    public function testLocaleValidationWithInvalidLocale(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Locale 'invalid_locale' is not supported");

        // Act - Use invalid locale (should throw exception)
        $this->dateFormatterService->formatDate($testDate, 'short', 'invalid_locale');
    }

    /**
     * Test locale validation with valid locale.
     */
    public function testLocaleValidationWithValidLocale(): void
    {
        // Arrange
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Act - Use valid locale
        $result = $this->dateFormatterService->formatDate($testDate, 'short', 'en');

        // Assert - Should use the provided locale
        $this->assertIsString($result);
        $this->assertNotEmpty($result);

        // Should be different from default locale
        $defaultResult = $this->dateFormatterService->formatDate($testDate, 'short', 'es');
        $this->assertNotEquals($defaultResult, $result, 'Valid locale should produce different result than default');
    }

    /**
     * Test locale validation with custom available languages.
     */
    public function testLocaleValidationWithCustomAvailableLanguages(): void
    {
        // Arrange
        $customSettingsService = $this->createMock(SettingsService::class);
        $customSettingsService->method('get')
            ->with('app.languages')
            ->willReturn(['es', 'en']); // Only Spanish and English available

        $service = new DateFormatterService($this->security, $this->parameterBag, $customSettingsService);
        $testDate = new \DateTimeImmutable('2024-01-15');

        // Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("Locale 'fr' is not supported");

        // Act - Try to use French (not in available languages, should throw exception)
        $service->formatDate($testDate, 'short', 'fr');
    }
}
