<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Diploma\Strategy;

use App\Entity\Course;
use App\Service\Diploma\Strategy\DiplomaBase;
use App\V2\Domain\Course\Diploma\DiplomaConfig;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

/**
 * Unit test for DiplomaBase strategy methods.
 * Tests the protected methods through a testable subclass to avoid reflection.
 */
class DiplomaBaseTest extends TestCase
{
    private TestableDiplomaBase $diplomaBase;

    protected function setUp(): void
    {
        // Create testable DiplomaBase - no dependencies needed for testing protected methods
        $this->diplomaBase = new TestableDiplomaBase();
    }

    #[DataProvider('courseDurationConfigurationProvider')]
    public function testShouldShowCourseDuration(
        bool $showDurationConfig,
        bool $expectedResult
    ): void {
        // Given: A course with diploma configuration
        $course = new Course();
        $diplomaConfig = new DiplomaConfig($showDurationConfig);
        $course->setDiplomaConfig($diplomaConfig);

        // When: Checking if duration should be shown for course diplomas
        $result = $this->diplomaBase->shouldShowCourseDurationPublic($course);

        // Then: Result matches expected behavior
        $this->assertSame($expectedResult, $result);
    }

    public static function courseDurationConfigurationProvider(): array
    {
        return [
            'Course diploma - show duration enabled' => [
                'showDurationConfig' => true,
                'expectedResult' => true,
            ],
            'Course diploma - show duration disabled' => [
                'showDurationConfig' => false,
                'expectedResult' => false,
            ],
        ];
    }

    public function testShouldShowCourseDurationWithNullConfig(): void
    {
        // Given: A course without diploma configuration
        $course = new Course();
        // No setDiplomaConfig() call - remains null

        // When: Checking if duration should be shown for course diplomas
        $result = $this->diplomaBase->shouldShowCourseDurationPublic($course);

        // Then: Should return false when no config exists
        $this->assertFalse($result);
    }
}

/**
 * Testable version of DiplomaBase that exposes protected methods as public.
 * This avoids using reflection in unit tests while maintaining encapsulation in production code.
 */
class TestableDiplomaBase extends DiplomaBase
{
    public function __construct()
    {
        // No parent constructor call - we don't need dependencies for testing protected methods
    }

    public function shouldShowCourseDurationPublic(Course $course): bool
    {
        return $this->shouldShowCourseDuration($course);
    }
}
