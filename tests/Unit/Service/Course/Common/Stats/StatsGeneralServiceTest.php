<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service\Course\Common\Stats;

use App\Entity\UserCourseChapter;
use App\Repository\UserCourseChapterRepository;
use App\Service\Course\Common\Stats\StatsGeneralService;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Service\StatsUser\ResultGameService;
use App\Tests\Mother\Entity\ChapterMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class StatsGeneralServiceTest extends TestCase
{
    private StatsGeneralService $service;
    private EntityManagerInterface $entityManager;
    private ResultGameService $resultGameService;
    private UserCourseChapterRepository $repository;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->resultGameService = $this->createMock(ResultGameService::class);
        $this->repository = $this->createMock(UserCourseChapterRepository::class);

        $this->entityManager
            ->method('getRepository')
            ->with(UserCourseChapter::class)
            ->willReturn($this->repository);

        $this->service = new StatsGeneralService(
            $this->entityManager,
            $this->resultGameService
        );
    }

    public function testCalculateChapterProgressMetricsWithNoUsers(): void
    {
        $chapter = ChapterMother::create();
        $chapterQueryParams = new ChapterQueryParams($chapter, false, []);

        $allUsersInChapters = true;
        $result = $this->service->calculateChapterProgressMetrics($chapterQueryParams, $allUsersInChapters);

        $expected = [
            'time' => 0,
            'inProgress' => 0,
            'finished' => 0,
            'allUsersInChapters' => false,
        ];

        $this->assertEquals($expected, $result);
    }

    /**
     * @throws Exception
     */
    #[DataProvider('calculateChapterProgressMetricsDataProvider')]
    public function testCalculateChapterProgressMetricsWithUsers(
        bool $allUsersInChapters,
        array $timeByChapterResult,
        array $totalUserFinishedResult,
        mixed $totalUserInProgressResult,
        array $users,
        array $expected
    ): void {
        $chapter = ChapterMother::create();
        $chapterQueryParams = new ChapterQueryParams($chapter, false, $users);

        $this->repository
            ->method('getTimeByChapter')
            ->with($chapterQueryParams)
            ->willReturn($timeByChapterResult);

        $this->repository
            ->method('getTotalUserFinishedChapters')
            ->with($chapterQueryParams)
            ->willReturn($totalUserFinishedResult);

        $this->repository
            ->method('chapterGetTotalUsersStarted')
            ->with($chapterQueryParams)
            ->willReturn($totalUserInProgressResult);

        $this->repository
            ->method('findUsersInChapterAndFinishedCourse')
            ->with($chapterQueryParams)
            ->willReturn($allUsersInChapters);

        $result = $this->service->calculateChapterProgressMetrics($chapterQueryParams, $allUsersInChapters);

        $this->assertEquals($expected, $result);
    }

    public static function calculateChapterProgressMetricsDataProvider(): \Generator
    {
        yield 'All users in chapters' => [
            'allUsersInChapters' => true,
            'timeByChapterResult' => ['totalTime' => 3600],
            'totalUserFinishedResult' => ['finished' => 3],
            'totalUserInProgressResult' => 2,
            'users' => [1, 2, 3, 4, 5],
            'expected' => [
                'time' => 3600,
                'inProgress' => 40,
                'finished' => 60,
                'allUsersInChapters' => true,
            ],
        ];

        yield 'No users in chapters' => [
            'allUsersInChapters' => false,
            'timeByChapterResult' => ['totalTime' => 0],
            'totalUserFinishedResult' => ['finished' => 0],
            'totalUserInProgressResult' => 0,
            'users' => [],
            'expected' => [
                'time' => 0,
                'inProgress' => 0,
                'finished' => 0,
                'allUsersInChapters' => false,
            ],
        ];

        yield 'Null values' => [
            'allUsersInChapters' => false,
            'timeByChapterResult' => ['totalTime' => null],
            'totalUserFinishedResult' => ['finished' => null],
            'totalUserInProgressResult' => null,
            'users' => [1, 2, 3, 4, 5],
            'expected' => [
                'time' => 0,
                'inProgress' => 0,
                'finished' => 0,
                'allUsersInChapters' => false,
            ],
        ];

        yield 'Values with string numbers' => [
            'allUsersInChapters' => true,
            'timeByChapterResult' => ['totalTime' => '3600'],
            'totalUserFinishedResult' => ['finished' => '3'],
            'totalUserInProgressResult' => '2',
            'users' => [1, 2, 3, 4, 5],
            'expected' => [
                'time' => 3600,
                'inProgress' => 40,
                'finished' => 60,
                'allUsersInChapters' => true,
            ],
        ];
    }
}
