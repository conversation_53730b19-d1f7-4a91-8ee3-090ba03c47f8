<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Repository\ChapterRepository;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\CourseMother;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class ChapterRepositoryTest extends TestCase
{
    private ChapterRepository $repository;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = $this->createMock(ChapterRepository::class);
    }

    /**
     * @throws Exception
     */
    public function testGetCourseChapterPaginated()
    {
        $course = CourseMother::create();
        $chapter1 = ChapterMother::create(course: $course);
        $chapter2 = ChapterMother::create(course: $course);

        /*$this->repository->method('loadCourseChaptersPaginated')->willReturn(
            [
                'chapters' => [$chapter1, $chapter2],
                'totalPages' => 1,
                'totalItems' => 2,
            ]
        );*/

        $result = $this->repository->loadCourseChaptersPaginated($course);
        $this->assertEquals(2, \count($result['chapters']));
        $this->assertEquals(1, $result['totalPages']);
        $this->assertEquals(2, $result['totalItems']);
    }

    /**
     * @throws Exception
     */
    public function testGetCourseChapterPaginatedWithChapterInactive()
    {
        $course = CourseMother::create();
        $chapter1 = ChapterMother::create(course: $course);
        $chapter2 = ChapterMother::create(active: false, course: $course);

        $this->repository->method('loadCourseChaptersPaginated')->willReturn(
            [
                'chapters' => [$chapter1],
                'totalPages' => 1,
                'totalItems' => 2,
            ]
        );

        $result = $this->repository->loadCourseChaptersPaginated($course);
        $this->assertEquals(1, \count($result['chapters']));
        $this->assertEquals(1, $result['totalPages']);
        $this->assertEquals(2, $result['totalItems']);
    }
}
