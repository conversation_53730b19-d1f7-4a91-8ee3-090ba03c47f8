<?php

declare(strict_types=1);

namespace App\Tests\Entity;

use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UserTest extends TestCase
{
    #[DataProvider('isCreatorDataProvider')]
    public function testIsCreator(array $userRoles, bool $expectedResponse): void
    {
        $user = UserMother::create(roles: $userRoles);

        $this->assertEquals($expectedResponse, $user->isCreator());
    }

    public static function isCreatorDataProvider(): \Generator
    {
        yield 'user with creator role' => [
            'userRoles' => [User::ROLE_CREATOR],
            'expectedResponse' => true,
        ];
        yield 'user with admin role' => [
            'userRoles' => [User::ROLE_ADMIN],
            'expectedResponse' => true,
        ];
        yield 'user with super admin role' => [
            'userRoles' => [User::ROLE_SUPER_ADMIN],
            'expectedResponse' => true,
        ];
        yield 'user with manager role' => [
            'userRoles' => [User::ROLE_MANAGER],
            'expectedResponse' => false,
        ];
        yield 'user with manager and creator role' => [
            'userRoles' => [User::ROLE_MANAGER, User::ROLE_CREATOR],
            'expectedResponse' => true,
        ];
        yield 'user with manager and admin role' => [
            'userRoles' => [User::ROLE_MANAGER, User::ROLE_ADMIN],
            'expectedResponse' => true,
        ];
        yield 'user with user role' => [
            'userRoles' => [User::ROLE_USER],
            'expectedResponse' => false,
        ];
        yield 'user with tutor role' => [
            'userRoles' => [User::ROLE_TUTOR],
            'expectedResponse' => false,
        ];
    }

    public function testLocaleCampusIsDefinedWhenSettingLocaleIfIsNotDefined(): void
    {
        $user = new User();
        $this->assertNull($user->getLocaleCampus());

        $user->setLocale('es');
        $this->assertEquals('es', $user->getLocaleCampus());

        $user->setLocaleCampus('eus');
        $this->assertEquals('eus', $user->getLocaleCampus());

        $user->setLocale('en');
        $this->assertEquals('eus', $user->getLocaleCampus());

        $user = (new User())
            ->setLocale('es')
            ->setLocaleCampus('eus');
        $this->assertEquals('eus', $user->getLocaleCampus());
        $this->assertEquals('es', $user->getLocale());
    }

    public function testLocaleIsDefinedWhenSettingLocaleCampusIfIsNotDefined(): void
    {
        $user = new User();
        $this->assertEquals('es', $user->getLocale(), 'es is the default locale');

        $user->setLocaleCampus('en');
        $this->assertEquals('en', $user->getLocale());

        $user->setLocale('eus');
        $this->assertEquals('eus', $user->getLocale());

        $user->setLocaleCampus('en');
        $this->assertEquals('eus', $user->getLocale());

        $user = (new User())
            ->setLocaleCampus('eus')
            ->setLocale('es');
        $this->assertEquals('eus', $user->getLocaleCampus());
        $this->assertEquals('es', $user->getLocale());
    }
}
