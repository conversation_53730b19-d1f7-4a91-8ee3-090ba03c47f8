<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\HealthCheck;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendHealthCheckEndpoints;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use Symfony\Component\HttpFoundation\Response;

class HealthCheckFunctionalTest extends FunctionalTestCase
{
    public function testHealthCheckHealthy(): void
    {
        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendHealthCheckEndpoints::healthCheckEndpoint(),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
    }

    /**
     * @throws Exception
     */
    public function testHealthCheckFail(): void
    {
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository
            ->method('countBy')
            ->willThrowException(new \Exception('Database connection failed'));

        static::getContainer()->set(UserRepository::class, $userRepository);

        $userToken = $this->loginAndGetToken();

        $this->makeRequest(
            method: 'GET',
            uri: BackendHealthCheckEndpoints::healthCheckEndpoint(),
            bearerToken: $userToken,
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
