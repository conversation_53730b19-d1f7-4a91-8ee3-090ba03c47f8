<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Course\Creator;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class DeleteCourseCreatorControllerFunctionalTest extends FunctionalTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use CourseCreatorFixtureTrait;

    private array $usersIds = [];

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->usersIds);
        parent::tearDown();
    }

    public function testBadRequestInvalidCourseId(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(courseId: -1, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testBadRequestInvalidUserId(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(courseId: 1, userId: -1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testCourseNotFound(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(courseId: 9999, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Course not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUserNotFound(): void
    {
        $course = $this->createAndGetCourse();
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(courseId: $course->getId(), userId: 9999),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testCourseCreatorNotFound(): void
    {
        $course = $this->createAndGetCourse();

        // Create a user with ROLE_CREATOR but don't add them to the course
        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(
                courseId: $course->getId(),
                userId: $creator->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Course creator not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessfulDeleteAsAdmin(): void
    {
        $course = $this->createAndGetCourse();

        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        // Add the creator to the course first
        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($creator->getId()),
            courseId: new Id($course->getId()),
        );

        $userToken = $this->loginAndGetToken(); // Default user is admin

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(
                courseId: $course->getId(),
                userId: $creator->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessfulDeleteAsCourseOwner(): void
    {
        $courseOwner = $this->createAndGetUser(
            firstName: 'Course',
            lastName: 'Owner',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $courseOwner->getId();

        $course = $this->createAndGetCourse(createdBy: $courseOwner);

        $creator = $this->createAndGetUser(
            firstName: 'Creator',
            lastName: 'User',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $creator->getId();

        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($creator->getId()),
            courseId: new Id($course->getId()),
        );

        // Login as the course owner
        $ownerToken = $this->loginAndGetToken(
            email: $courseOwner->getEmail(),
            password: self::DEFAULT_USER_PASSWORD
        );

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(
                courseId: $course->getId(),
                userId: $creator->getId()
            ),
            bearerToken: $ownerToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testForbiddenForNonOwnerCreator(): void
    {
        // Create a course creator who will own the course
        $courseOwner = $this->createAndGetUser(
            firstName: 'Course',
            lastName: 'Owner',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $courseOwner->getId();

        // Create course with the course owner as creator
        $course = $this->createAndGetCourse(createdBy: $courseOwner);

        // Create another creator who is NOT the course owner
        $notOwnerCreator = $this->createAndGetUser(
            firstName: 'Not Owner',
            lastName: 'Creator',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $notOwnerCreator->getId();

        // Create a third creator to be deleted from the course
        $targetCreator = $this->createAndGetUser(
            firstName: 'Target',
            lastName: 'Creator',
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );
        $this->usersIds[] = $targetCreator->getId();

        // Add the target creator to the course
        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($targetCreator->getId()),
            courseId: new Id($course->getId()),
        );

        // Login as the non-owner creator
        $notOwnerToken = $this->loginAndGetToken(
            email: $notOwnerCreator->getEmail(),
            password: self::DEFAULT_USER_PASSWORD
        );

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(
                courseId: $course->getId(),
                userId: $targetCreator->getId()
            ),
            bearerToken: $notOwnerToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User '
            . (string) $notOwnerCreator
            . ' is not authorized to perform this action for course '
            . (string) $course,
            $content['message']
        );
    }

    public function testUnauthorizedWithoutToken(): void
    {
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseCreatorEndpoint(courseId: 1, userId: 1),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }
}
