<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Admin\Manager;

use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminManagerEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseManagerFixtureTrait;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetManagerCoursesFunctionalTest extends FunctionalTestCase
{
    use CourseManagerFixtureTrait;
    private User $managerUser;
    private User $nonManagerUser;
    private Course $course1;
    private Course $course2;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws CollectionException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->managerUser = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Manager',
            roles: [User::ROLE_USER, User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->nonManagerUser = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Non Manager',
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
        );

        $this->course1 = $this->createAndGetCourse(
            name: 'Test Course 1',
        );
        $this->course2 = $this->createAndGetCourse(
            name: 'Test Course 2',
        );

        // Sync with in-memory repository
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->managerUser->getId()),
            courseId: new Id($this->course1->getId())
        );
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->managerUser->getId()),
            courseId: new Id($this->course2->getId())
        );
    }

    public function testGetManagerCoursesSuccessWithCourses(): void
    {
        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->managerUser->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(2, $responseData['data']);

        foreach ($responseData['data'] as $courseData) {
            $this->assertArrayHasKey('id', $courseData);
            $this->assertIsInt($courseData['id']);
        }

        $courseIds = array_column($responseData['data'], 'id');
        $this->assertContains($this->course1->getId(), $courseIds);
        $this->assertContains($this->course2->getId(), $courseIds);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testGetManagerCoursesSuccessWithNoCourses(): void
    {
        $managerWithNoCourses = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'With no courses',
            roles: [User::ROLE_USER, User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($managerWithNoCourses->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertCount(0, $responseData['data']);
        $this->assertEquals([], $responseData['data']);

        $this->hardDeleteUsersByIds([
            $managerWithNoCourses->getId(),
        ]);
    }

    public function testGetManagerCoursesRequiresAuthentication(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->managerUser->getId())
            // No token
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    public function testGetManagerCoursesUserNotFound(): void
    {
        $userToken = $this->loginAndGetToken();
        $nonExistentUserId = 99999;

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($nonExistentUserId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User not found', $responseData['message']);
    }

    public function testGetManagerCoursesUserNotManager(): void
    {
        $userToken = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->nonManagerUser->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('User is not a manager', $responseData['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws Exception
     * @throws ORMException
     */
    public function testGetManagerCoursesForbiddenUser(): void
    {
        $regularUser = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
        );

        $token = $this->loginAndGetToken(email: $regularUser->getEmail());

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($this->managerUser->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('Forbidden', $responseData['message']);

        $this->hardDeleteUsersByIds([$regularUser->getId()]);
    }

    #[DataProvider('invalidUserIdProvider')]
    public function testGetManagerCoursesIdValidation(int $invalidUserId, string $description): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: AdminManagerEndpoints::getManagerCoursesEndpoint($invalidUserId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('Validation', $responseData['message']);
    }

    public static function invalidUserIdProvider(): array
    {
        return [
            'negative ID' => [
                'invalidUserId' => -1,
                'description' => 'Negative ID should return validation error',
            ],
            'zero ID' => [
                'invalidUserId' => 0,
                'description' => 'Zero ID should return validation error',
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds([
            $this->managerUser->getId(),
            $this->nonManagerUser->getId(),
        ]);

        $this->truncateEntities([
            Course::class,
        ]);

        parent::tearDown();
    }
}
