<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\LTI;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\LtiEndpoints;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Functional\V2\Fixtures\LtiRegistrationFixtureTrait;
use App\Tests\Functional\V2\Fixtures\LtiToolFixtureTrait;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostLtiToolControllerFunctionalTest extends FunctionalTestCase
{
    use LtiToolFixtureTrait;
    use LtiRegistrationFixtureTrait;
    use UserHelperTrait;

    private array $userIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $superAdminUser = $this->createAndGetUser(
            roles: ['ROLE_SUPER_ADMIN'],
            email: '<EMAIL>',
        );

        $this->userIds[] = $superAdminUser->getId();
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    public function testPostLtiTool(): void
    {
        /** @var LtiToolRepository $ltiToolRepository */
        $ltiToolRepository = $this->client->getContainer()->get('App\V2\Domain\LTI\LtiToolRepository');
        $registration1 = $this->setAndGetLtiRegistrationInRepository(
            name: 'Registration 1',
            clientId: 'registration-1'
        );

        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint($registration1->getId()->value()),
            body: [
                'name' => 'Tool 1',
                'audience' => 'audience',
                'oidc_initiation_url' => 'https://example.com/oidc_initiation',
                'launch_url' => 'https://example.com/launch',
                'deep_linking_url' => 'https://example.com/deep_linking',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());

        $result1 = $ltiToolRepository->findBy(
            LtiToolCriteria::createEmpty()->filterByRegistrationId($registration1->getId())
        );

        $this->assertCount(1, $result1);

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint($registration1->getId()->value()),
            body: [
                'name' => 'Tool 1 Updated',
                'audience' => 'audience updated',
                'oidc_initiation_url' => 'https://example.com/oidc_initiation',
                'launch_url' => 'https://example.com/launch',
                'deep_linking_url' => 'https://example.com/deep_linking',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());
        $result2 = $ltiToolRepository->findBy(
            LtiToolCriteria::createEmpty()->filterByRegistrationId($registration1->getId())
        );

        $this->assertCount(1, $result2);

        $this->assertEquals($result1->all()[0]->getId(), $result2->all()[0]->getId());
        $this->assertNotEquals($result1->all()[0]->getName(), $result2->all()[0]->getName());
    }

    #[DataProvider('provideBadRequest')]
    public function testBadRequest(array $body, array $violations): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint(UuidMother::create()->value()),
            body: $body,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertIsArray($content['metadata']);
        $metadata = $content['metadata'];
        $this->assertArrayHasKey('violations', $metadata);
        $this->assertEquals($violations, $metadata['violations']);
    }

    public static function provideBadRequest(): \Generator
    {
        yield 'empty body' => [
            'body' => [],
            'violations' => [
                '' => 'Body cannot be empty',
                '[name]' => 'This field is missing.',
                '[audience]' => 'This field is missing.',
                '[oidc_initiation_url]' => 'This field is missing.',
                '[launch_url]' => 'This field is missing.',
                '[deep_linking_url]' => 'This field is missing.',
                '[jwks_url]' => 'This field is missing.',
            ],
        ];

        yield 'invalid or empty values' => [
            'body' => [
                'name' => '',
                'audience' => '',
                'oidc_initiation_url' => 'https//example.com/oidc_initiation',
                'launch_url' => 'https:example.com/launch',
                'deep_linking_url' => '/deep_linking',
                'jwks_url' => 'example.com/jwks',
            ],
            'violations' => [
                '[name]' => 'This value should not be blank.',
                '[audience]' => 'This value should not be blank.',
                '[oidc_initiation_url]' => 'This value is not a valid URL.',
                '[launch_url]' => 'This value is not a valid URL.',
                '[deep_linking_url]' => 'This value is not a valid URL.',
                '[jwks_url]' => 'This value is not a valid URL.',
            ],
        ];
    }

    public function testInvalidRegistrationId(): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint('invalid-uuid'),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);

        $this->assertArrayHasKey('metadata', $content);
        $this->assertIsArray($content['metadata']);
        $metadata = $content['metadata'];
        $this->assertArrayHasKey('violations', $metadata);
        $this->assertEquals([
            '[uuid]' => 'This is not a valid UUID.',
        ], $metadata['violations']);
    }

    public function testRegistrationNotFound(): void
    {
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint(UuidMother::create()->value()),
            body: [
                'name' => 'Tool 1 Updated',
                'audience' => 'audience updated',
                'oidc_initiation_url' => 'https://example.com/oidc_initiation',
                'launch_url' => 'https://example.com/launch',
                'deep_linking_url' => 'https://example.com/deep_linking',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('LTI registration could not be found.', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidUuidException
     */
    public function testForbidden(): void
    {
        $user = $this->createAndGetUser(
            roles: ['ROLE_ADMIN'],
            email: '<EMAIL>',
        );
        $this->userIds[] = $user->getId();

        $token = $this->loginAndGetToken(email: $user->getEmail());

        $response = $this->makeRequest(
            method: 'POST',
            uri: LtiEndpoints::postLtiToolEndpoint(UuidMother::create()->value()),
            body: [
                'name' => 'Tool 1',
                'audience' => 'audience',
                'oidc_initiation_url' => 'https://example.com/oidc_initiation',
                'launch_url' => 'https://example.com/launch',
                'deep_linking_url' => 'https://example.com/deep_linking',
                'jwks_url' => 'https://example.com/jwks',
            ],
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->hardDeleteUsersByIds($this->userIds);
        parent::tearDown();
    }
}
