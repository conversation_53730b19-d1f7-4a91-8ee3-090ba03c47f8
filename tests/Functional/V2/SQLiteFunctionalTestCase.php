<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2;

use App\Tests\Functional\FunctionalTestCase;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\ORM\EntityManagerInterface;

abstract class SQLiteFunctionalTestCase extends FunctionalTestCase
{
    protected EntityManagerInterface $em;

    protected function setUp(): void
    {
        parent::setUp();

        $this->em = $this->getSQLiteEntityManager();
    }

    protected function getConnection(): Connection
    {
        return $this->em->getConnection();
    }

    /**
     * @throws DBALException
     */
    protected function getSchema(): Schema
    {
        return $this->getConnection()
            ->createSchemaManager()
            ->introspectSchema()
        ;
    }

    private function getSQLiteEntityManager(): EntityManagerInterface
    {
        return $this->client->getContainer()
            ->get('doctrine')
            ->getManager('sqlite');
    }
}
