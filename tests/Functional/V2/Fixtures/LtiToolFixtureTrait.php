<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\LTI\LtiToolMother;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

trait LtiToolFixtureTrait
{
    /**
     * @throws InvalidUuidException
     */
    private function setAndGetLtiToolInRepository(
        ?Uuid $id = null,
        ?Uuid $registrationId = null,
        ?string $name = null,
        ?string $audience = null,
        ?Url $oidcInitiationUrl = null,
        ?Url $launchUrl = null,
        ?Url $deepLinkingUrl = null,
        ?Url $jwksUrl = null,
    ): LtiTool {
        $tool = LtiToolMother::create(
            id: $id,
            registrationId: $registrationId,
            name: $name,
            audience: $audience,
            oidcInitiationUrl: $oidcInitiationUrl,
            launchUrl: $launchUrl,
            deepLinkingUrl: $deepLinkingUrl,
            jwksUrl: $jwksUrl,
        );

        $this->client->getContainer()->get('App\V2\Domain\LTI\LtiToolRepository')
            ->put($tool);

        return $tool;
    }
}
