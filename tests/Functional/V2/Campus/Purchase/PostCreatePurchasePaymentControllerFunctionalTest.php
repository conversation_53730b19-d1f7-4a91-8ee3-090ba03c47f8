<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostCreatePurchasePaymentControllerFunctionalTest extends FunctionalTestCase
{
    use PurchasableItemHelperTrait;

    private const string VALID_UUID_1 = '550e8400-e29b-41d4-a716-************';
    private const string INVALID_UUID = 'invalid-uuid';

    private User $testUser;
    private PurchaseRepository $purchaseRepository;
    private array $additionalUserIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->testUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'test.payment.intent.' . uniqid() . '@example.com',
        );

        $this->purchaseRepository = $this->client->getContainer()
            ->get('App\V2\Domain\Purchase\PurchaseRepository');
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $userIdsToDelete = [];

        if (!empty($this->testUser)) {
            $userIdsToDelete[] = $this->testUser->getId();
        }

        if (!empty($this->additionalUserIds)) {
            $userIdsToDelete = array_merge($userIdsToDelete, $this->additionalUserIds);
        }

        if (!empty($userIdsToDelete)) {
            $this->hardDeleteUsersByIds($userIdsToDelete);
        }

        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws PurchaseRepositoryException
     */
    public function testSuccessfulPaymentIntentCreationAsOwner(): void
    {
        // Create a user and their own purchase
        $user = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'user.payment.test.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $user->getId();

        $userToken = $this->loginAndGetTokenForUser($user);

        // Create a purchase for the user
        $purchase = PurchaseMother::create(
            userId: new Id($user->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($purchaseId),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('purchase_id', $data);
        $this->assertEquals($purchaseId, $data['purchase_id']);
        $this->assertArrayHasKey('payment_intent', $data);
        $this->assertIsString($data['payment_intent']);
        $this->assertArrayHasKey('amount', $data);
        $this->assertEquals(2000, $data['amount']);
        $this->assertArrayHasKey('currency_code', $data);
        $this->assertEquals('EUR', $data['currency_code']);
        $this->assertArrayHasKey('status', $data);
        $this->assertEquals('pending', $data['status']);
    }

    #[DataProvider('invalidUuidProvider')]
    public function testInvalidUuidValidation(string $invalidUuid): void
    {
        // Use regular user since endpoint now requires ROLE_USER
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($invalidUuid),
            bearerToken: $userToken
        );

        // For empty string, we might get 500 due to routing issues
        if ('' === $invalidUuid) {
            $this->assertContains($response->getStatusCode(), [
                Response::HTTP_BAD_REQUEST,
                Response::HTTP_INTERNAL_SERVER_ERROR,
                Response::HTTP_NOT_FOUND,
            ]);
        } else {
            $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
            $this->assertJson($response->getContent());

            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('error', $responseData);
            $this->assertEquals(1, $responseData['error']);
            $this->assertArrayHasKey('message', $responseData);
            $this->assertEquals('Validation failed', $responseData['message']);
        }
    }

    /**
     * @throws InvalidUuidException
     */
    public function testNonExistentPurchase(): void
    {
        // Use regular user since endpoint now requires ROLE_USER
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $nonExistentPurchaseId = UuidMother::create()->value();

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($nonExistentPurchaseId),
            bearerToken: $userToken
        );

        // Should return an error for a non-existent purchase
        $this->assertContains($response->getStatusCode(), [
            Response::HTTP_NOT_FOUND,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            Response::HTTP_BAD_REQUEST,
        ]);
    }

    public function testUnauthenticatedAccess(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint(self::VALID_UUID_1),
        );

        // The security configuration returns 401 for unauthenticated access to protected endpoints
        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws PurchaseRepositoryException
     */
    public function testUserCannotAccessOtherUsersPurchases(): void
    {
        // Create another user
        $otherUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'other.user.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $otherUser->getId();

        // Create a purchase for the other user
        $otherUserPurchase = PurchaseMother::create(
            userId: new Id($otherUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1500, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($otherUserPurchase);

        // Try to access with a testUser token
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($otherUserPurchase->getId()->value()),
            bearerToken: $userToken
        );

        // Should be forbidden since the user doesn't own the purchase
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testAdminCannotAccessOtherUsersPurchases(): void
    {
        // Create admin user
        $adminUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: 'admin.access.test.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $adminUser->getId();

        // Create another regular user
        $otherUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'other.user.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $otherUser->getId();

        $adminToken = $this->loginAndGetTokenForUser($adminUser);

        // Create a purchase for the other user
        $otherUserPurchase = PurchaseMother::create(
            userId: new Id($otherUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1500, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($otherUserPurchase);

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($otherUserPurchase->getId()->value()),
            bearerToken: $adminToken
        );

        // Even admin cannot create payment intent for another user's purchase due to ownership validation
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testAdminCanAccessOwnPurchases(): void
    {
        // Create admin user
        $adminUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: 'admin.test.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $adminUser->getId();

        $adminToken = $this->loginAndGetTokenForUser($adminUser);

        // Create a purchase for the admin user
        $purchase = PurchaseMother::create(
            userId: new Id($adminUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000, currency: Currency::USD())
        );
        $this->purchaseRepository->put($purchase);

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($purchase->getId()->value()),
            bearerToken: $adminToken
        );

        // Admin should have access to their own purchases
        $this->assertContains($response->getStatusCode(), [
            Response::HTTP_OK,
            Response::HTTP_UNPROCESSABLE_ENTITY,
        ]);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testRegularUserCanAccessOwnPurchases(): void
    {
        // Use the test user
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase for the test user
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 2500, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->getPaymentIntentEndpoint($purchase->getId()->value()),
            bearerToken: $userToken
        );

        // Regular user should have access to their own purchases
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals($purchase->getId()->value(), $responseData['data']['purchase_id']);
    }

    public static function invalidUuidProvider(): \Generator
    {
        yield 'completely invalid uuid' => ['invalid-uuid'];
        yield 'empty string' => [''];
        yield 'numeric string' => ['123456'];
        yield 'partial uuid' => ['550e8400-e29b-41d4'];
        yield 'uuid with invalid characters' => ['550e8400-e29b-41d4-a716-44665544000g'];
    }

    private function getPaymentIntentEndpoint(string $purchaseId): string
    {
        return '/api/v2/payments/intent/' . $purchaseId;
    }
}
