<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Campus\CampusBillingDataEndpoints;
use App\Tests\Functional\V2\Fixtures\BillingDataFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetBillingDataControllerFunctionalTest extends FunctionalTestCase
{
    use BillingDataFixtureTrait;

    /**
     * Test successful retrieval of billing data by an admin user.
     */
    public function testGetBillingDataSuccessAsAdmin(): void
    {
        $userId = new Id(self::DEFAULT_USER_ID);
        $billingData = $this->setAndGetBillingDataInRepository(
            userId: $userId,
            tin: '12345678A',
            firstName: 'John',
            lastName: 'Doe',
            address: '123 Main St',
            postalCode: '12345',
            city: 'Madrid',
            country: 'Spain',
            metadata: ['company' => 'Test Company']
        );

        $adminToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(self::DEFAULT_USER_ID),
            bearerToken: $adminToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertEquals($billingData->getId()->value(), $data['id']);
        $this->assertEquals($billingData->getUserId()->value(), $data['user_id']);
        $this->assertEquals('12345678A', $data['tin']);
        $this->assertEquals('John', $data['first_name']);
        $this->assertEquals('Doe', $data['last_name']);
        $this->assertEquals('123 Main St', $data['address']);
        $this->assertEquals('12345', $data['postal_code']);
        $this->assertEquals('Madrid', $data['city']);
        $this->assertEquals('Spain', $data['country']);
        $this->assertEquals(['company' => 'Test Company'], $data['metadata']);
    }

    /**
     * Test successful retrieval of billing data by the same user.
     *
     * @throws \Exception
     */
    public function testGetBillingDataSuccessAsSameUser(): void
    {
        $userId = new Id(self::DEFAULT_USER_ID);
        $billingData = $this->setAndGetBillingDataInRepository(
            userId: $userId,
            tin: '87654321B',
            firstName: 'Jane',
            lastName: 'Smith'
        );

        $userToken = $this->loginAndGetToken(
            email: self::DEFAULT_USER_EMAIL,
            password: self::DEFAULT_USER_PASSWORD
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(self::DEFAULT_USER_ID),
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];
        $this->assertEquals($billingData->getId()->value(), $data['id']);
        $this->assertEquals($billingData->getUserId()->value(), $data['user_id']);
        $this->assertEquals('87654321B', $data['tin']);
        $this->assertEquals('Jane', $data['first_name']);
        $this->assertEquals('Smith', $data['last_name']);
    }

    /**
     * Test that a non-admin user cannot access another user's billing data.
     */
    public function testGetBillingDataForbiddenForDifferentUser(): void
    {
        $otherUserId = new Id(999);
        $this->setAndGetBillingDataInRepository(
            userId: $otherUserId,
            tin: '11111111C'
        );

        // Create a non-admin user with unique email
        $uniqueEmail = 'nonadmin' . time() . '@test.com';
        $nonAdminUser = $this->createAndGetUser(
            roles: ['ROLE_USER'],
            email: $uniqueEmail,
            password: '12345678'
        );

        $nonAdminToken = $this->loginAndGetToken(
            email: $uniqueEmail,
            password: '12345678'
        );

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(999),
            bearerToken: $nonAdminToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * Test billing data didn't found scenario.
     */
    public function testGetBillingDataNotFound(): void
    {
        $adminToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(999),
            bearerToken: $adminToken
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('BillingData not found', $responseData['message']);
    }

    /**
     * Test authentication is required.
     */
    public function testGetBillingDataRequiresAuthentication(): void
    {
        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(self::DEFAULT_USER_ID)
            // No bearer token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * Test validation errors for invalid user ID.
     */
    #[DataProvider('invalidUserIdProvider')]
    public function testGetBillingDataValidationErrors(
        int $userId,
        int $expectedStatusCode,
    ): void {
        $adminToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint($userId),
            bearerToken: $adminToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('Validation failed', $responseData['message']);
    }

    /**
     * Data provider for invalid user ID scenarios.
     */
    public static function invalidUserIdProvider(): array
    {
        return [
            'negative user ID' => [
                'userId' => -1,
                'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            ],
            'zero user ID' => [
                'userId' => 0,
                'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
            ],
        ];
    }

    /**
     * Test response structure and data types.
     */
    public function testGetBillingDataResponseStructure(): void
    {
        $userId = new Id(self::DEFAULT_USER_ID);
        $this->setAndGetBillingDataInRepository(
            userId: $userId,
            metadata: ['key1' => 'value1', 'key2' => 123]
        );

        $adminToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'GET',
            uri: CampusBillingDataEndpoints::getBillingDataEndpoint(self::DEFAULT_USER_ID),
            bearerToken: $adminToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);

        $data = $responseData['data'];

        // Verify all required fields are present
        $requiredFields = [
            'id',
            'user_id',
            'tin',
            'first_name',
            'last_name',
            'address',
            'postal_code',
            'city',
            'country',
            'metadata',
        ];
        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $data, "Field '$field' is missing from response");
        }

        // Verify data types
        $this->assertIsString($data['id']);
        $this->assertIsInt($data['user_id']);
        $this->assertIsString($data['tin']);
        $this->assertIsString($data['first_name']);
        $this->assertIsString($data['last_name']);
        $this->assertIsString($data['address']);
        $this->assertIsString($data['postal_code']);
        $this->assertIsString($data['city']);
        $this->assertIsString($data['country']);
        $this->assertIsArray($data['metadata']);
    }
}
