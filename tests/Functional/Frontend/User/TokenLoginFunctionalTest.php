<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\User;

use App\Entity\UserLogin;
use App\Entity\UserToken;
use App\Repository\UserTokenRepository;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use App\Tests\Mother\Entity\UserTokenMother;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class TokenLoginFunctionalTest extends FunctionalTestCase
{
    public function testNoTokenProvided(): void
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'token' => '',
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            'No email or token has been provided'
        );
    }

    public function testTokenNotFound(): void
    {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'token' => 'token',
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            'No se ha podido acceder a través de SSO. Si aún no se ha registrado y activado su usuario en la plataforma, deberá hacerlo para poder habilitar este tipo de acceso SSO.'
        );
    }

    /**
     * @dataProvider invalidTokenDataProvider
     *
     * @throws ORMException
     */
    public function testAuthenticationException(
        bool $revoked,
        bool $used,
        \DateTimeImmutable $validUntil,
        int $tokenType,
        string $expectedMessage
    ): void {
        $em = $this->getEntityManager();

        $defaultUser = $this->getDefaultUser();

        $userToken = UserTokenMother::create(
            $defaultUser,
            $tokenType,
            'token',
            $revoked,
            $used,
            null,
            $validUntil
        );

        /** @var UserTokenRepository $userTokenRepository */
        $userTokenRepository = $this->getRepository(UserToken::class);
        $userTokens = $userTokenRepository->findAll();
        $this->assertEmpty($userTokens);

        $em->persist($userToken);
        $em->flush();

        $userTokens = $userTokenRepository->findAll();
        $this->assertNotEmpty($userTokens);

        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'token' => 'token',
        ]);

        $this->assertManagedErrorResponse(
            $response,
            Response::HTTP_UNAUTHORIZED,
            $expectedMessage
        );
    }

    public static function invalidTokenDataProvider(): \Generator
    {
        yield 'Revoked' => [
            'revoked' => true,
            'used' => false,
            'validUntil' => new \DateTimeImmutable('+1 day'),
            'tokenType' => UserToken::TYPE_OTP,
            'expectedMessage' => 'Invalid Token'
        ];

        yield 'Used' => [
            'revoked' => false,
            'used' => true,
            'validUntil' => new \DateTimeImmutable('+1 day'),
            'tokenType' => UserToken::TYPE_OTP,
            'expectedMessage' => 'Invalid Token'
        ];

        yield 'Expired' => [
            'revoked' => false,
            'used' => false,
            'validUntil' => new \DateTimeImmutable('-1 day'),
            'tokenType' => UserToken::TYPE_OTP,
            'expectedMessage' => 'Invalid Token'
        ];

        yield 'Invalid Token Type' => [
            'revoked' => false,
            'used' => false,
            'validUntil' => new \DateTimeImmutable('+1 day'),
            'tokenType' => UserToken::TYPE_ANNOUNCEMENT_INSPECTOR,
            'expectedMessage' => 'Token not allowed'
        ];
    }

    /**
     * @dataProvider tokenLoginOkDataProvider
     *
     * @throws NotSupported
     * @throws ORMException
     */
    public function testTokenLoginOk(
        ?int $tokenType,
        array $extra,
        bool $expectedTokenRemoved
    ): void {
        $em = $this->getEntityManager();

        $defaultUser = $this->getDefaultUser();

        $userToken = UserTokenMother::create(
            $defaultUser,
            $tokenType,
            'token',
            false,
            false,
            null,
            new \DateTimeImmutable('+1 day'),
            $extra
        );

        /** @var UserTokenRepository $userTokenRepository */
        $userTokenRepository = $this->getRepository(UserToken::class);
        $userTokens = $userTokenRepository->findAll();
        $this->assertEmpty($userTokens);

        $em->persist($userToken);
        $em->flush();

        $userTokens = $userTokenRepository->findAll();
        $this->assertNotEmpty($userTokens);

        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'token' => 'token',
        ]);

        if (Response::HTTP_INTERNAL_SERVER_ERROR === $response->getStatusCode()) {
            $this->fail($response->getContent());
        }

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('token', $responseData);
        $this->assertArrayHasKey('refreshToken', $responseData);
        $this->assertArrayHasKey('level', $responseData);

        $this->assertArrayHasKey('user', $responseData);
        $this->assertArrayHasKey('id', $responseData['user']);
        $this->assertEquals(self::DEFAULT_USER_ID, $responseData['user']['id']);
        $this->assertArrayHasKey('email', $responseData['user']);
        $this->assertEquals(self::DEFAULT_USER_EMAIL, $responseData['user']['email']);
        $this->assertArrayHasKey('firstName', $responseData['user']);
        $this->assertArrayHasKey('lastName', $responseData['user']);
        $this->assertArrayHasKey('extra', $responseData['user']);
        $this->assertArrayHasKey('points', $responseData['user']);
        $this->assertArrayHasKey('avatar', $responseData['user']);
        $this->assertArrayHasKey('locale', $responseData['user']);
        $this->assertArrayHasKey('dataAvatar', $responseData['user']);
        $this->assertArrayHasKey('fullName', $responseData['user']);
        $this->assertArrayHasKey('policies', $responseData['user']);
        $this->assertArrayHasKey('finishedCourses', $responseData['user']);
        $this->assertArrayHasKey('timeSpent', $responseData['user']);
        $this->assertArrayHasKey('itineraries', $responseData['user']);

        $userTokens = $userTokenRepository->findAll();

        if ($expectedTokenRemoved) {
            $this->assertEmpty($userTokens);
        } else {
            $this->assertNotEmpty($userTokens);
        }

        $tokenParts = explode('.', $responseData['token']);
        $this->assertCount(3, $tokenParts);

        $payload = json_decode(base64_decode($tokenParts[1]), true);
        $this->assertArrayHasKey('exp', $payload);
        $this->assertArrayHasKey('iat', $payload);
        $this->assertArrayHasKey('roles', $payload);
        $this->assertArrayHasKey('username', $payload);
        $this->assertEquals(self::DEFAULT_USER_EMAIL, $payload['username']);

        if (!empty($extra)) {
            foreach ($extra as $key => $value) {
                $this->assertArrayHasKey($key, $payload);
                $this->assertEquals($value, $payload[$key]);
            }
        }
    }

    public static function tokenLoginOkDataProvider(): \Generator
    {
        yield 'OTP' => [
            'tokenType' => UserToken::TYPE_OTP,
            'extra' => [],
            'expectedTokenRemoved' => true
        ];

        yield 'Inspector Impersonate User' => [
            'tokenType' => UserToken::TYPE_INSPECTOR_IMPERSONATE_USER,
            'extra' => ['key' => 'value'],
            'expectedTokenRemoved' => false
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([UserToken::class, UserLogin::class]);

        parent::tearDown();
    }
}
