<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class UploadMaterialsAnnouncementFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    private User $testUser;
    private array $tempFiles = [];

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(email: '<EMAIL>');
    }

    #[DataProvider('providerAnnouncementMaterialsUploadAuthorization')]
    public function testUploadMaterialAnnouncement(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode
    ): void {
        $this->testUser->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);
        $user = $this->getDefaultUser();

        $course = $this->createAndGetCourse(createdBy: $isCreatorAnnouncement ? $this->testUser : $user);
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $isCreatorAnnouncement ? $this->testUser : $user,
        );

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            $this->updateSettingValue(value: '1', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);
                $this->createAndGetAnnouncementTutor(
                    announcement: $announcement,
                    announcementGroup: $announcementGroup,
                    tutor: $this->testUser
                );
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'material_test_');
        $this->tempFiles[] = $tempFile; // Trackear el archivo
        file_put_contents($tempFile, 'test content');

        // Create a mock file using the temporary file
        $file = new UploadedFile(
            $tempFile,
            'test.png',
            'application/png',
            null,
            true
        );
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // El tipo 4 es material tipo imagen
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::uploadMaterialsAnnouncementEndpoint($announcement->getId()),
            queryParams: ['type' => 4],
            files: ['file' => $file],
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerAnnouncementMaterialsUploadAuthorization(): \Generator
    {
        yield 'Super Admin can upload materials' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can upload materials' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can upload materials if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can upload materials if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator cannot upload materials' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Tutor cannot upload materials' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];
        yield 'Tutor can upload materials  if the announcement is shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Inspector cannot upload materials' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'User cannot upload materials' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: '0',
        );
        $this->truncateEntities([
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->testUser->getId(),
        ]);

        // Cleanup temporary files
        foreach ($this->tempFiles as $tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }

        parent::tearDown();
    }
}
