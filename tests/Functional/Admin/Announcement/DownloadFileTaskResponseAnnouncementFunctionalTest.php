<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\FilesTask;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\TaskEndpoints;
use App\Tests\Functional\HelperTrait\FileTaskHelperTrait;
use App\Tests\Functional\HelperTrait\TaskCourseHelperTrait;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class DownloadFileTaskResponseAnnouncementFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use FileTaskHelperTrait;
    use TaskCourseHelperTrait;

    private User $testUser;

    private array $tempFiles = [];

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(email: '<EMAIL>');
    }

    #[DataProvider('providerAnnouncementFileTaskSetDownloadableAuthorization')]
    public function testSetDownloadableFileTaskAnnouncement(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode
    ): void {
        $this->testUser->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);
        $user = $this->getDefaultUser();

        $course = $this->createAndGetCourse(createdBy: $isCreatorAnnouncement ? $this->testUser : $user);
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $isCreatorAnnouncement ? $this->testUser : $user,
        );
        $courseTask = $this->createAndGetTaskCourse(course: $course, announcement: $announcement);
        $fileTask = $this->createAndGetFileTask(taskCourse: $courseTask, isDownloadable: true);

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            $this->updateSettingValue(value: '1', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);
                $this->createAndGetAnnouncementTutor(
                    announcement: $announcement,
                    announcementGroup: $announcementGroup,
                    tutor: $this->testUser
                );
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $response = $this->makeRequest(
            method: 'PATCH',
            uri: TaskEndpoints::downloadableTaskFileEndpoint($fileTask->getId()),
            body: ['downloadable' => !$fileTask->getIsDownload()],
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerAnnouncementFileTaskSetDownloadableAuthorization(): \Generator
    {
        yield 'Super Admin can set downloadable task file' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can set downloadable task file' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can set downloadable task file if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can set downloadable task file if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator cannot set downloadable task file' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 500,
        ];
        yield 'Tutor cannot set downloadable task file' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 500,
        ];
        yield 'Tutor can set downloadable task file if the announcement is shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Inspector cannot set downloadable task file' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'User cannot set downloadable task file' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: '0',
        );
        $this->truncateEntities([
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            Course::class,
            CourseCategory::class,
            TaskCourse::class,
            FilesTask::class,
        ]);

        // Cleanup temporary files
        foreach ($this->tempFiles as $tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }

        $this->hardDeleteUsersByIds([
            $this->testUser->getId(),
        ]);

        parent::tearDown();
    }
}
