<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\TaskCourseHelperTrait;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class UploadFileAnnouncementTaskCourseFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use TaskCourseHelperTrait;

    private ?User $testUser1 = null;
    private ?User $testUser2 = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser1 = $this->createAndGetUser(email: '<EMAIL>');
        $this->testUser2 = $this->createAndGetUser(
            roles: ['ROLE_USER', 'ROLE_MANAGER'],
            email: '<EMAIL>'
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    #[DataProvider('providerUploadFileAnnouncementTaskAuthorization')]
    public function testUploadFileAnnouncementTaskAuthorization(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode,
    ): void {
        $this->updateSettingValue(value: 'true', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);

        $this->testUser1->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser1);

        $announcementCreator = $isCreatorAnnouncement ? $this->testUser1 : $this->testUser2;

        $course = $this->createAndGetCourse(createdBy: $announcementCreator);

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $announcementCreator,
        );

        $taskCourse = $this->createAndGetTaskCourse(
            course: $course,
            announcement: $announcement,
            createdBy: $announcementCreator
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $this->createAndGetAnnouncementTutor(announcement: $announcement, announcementGroup: $announcementGroup, tutor: $this->testUser1);
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser1->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }

        $token = $this->loginAndGetTokenForUser($this->testUser1);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::uploadFileAnnouncementTaskEndpoint($taskCourse->getId()),
            bearerToken: $token,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerUploadFileAnnouncementTaskAuthorization(): \Generator
    {
        yield 'Super Admin can upload file announcement task' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_OK,
        ];

        yield 'Admin can upload file announcement task' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_OK,
        ];

        yield 'Manager can upload file announcement task if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_OK,
        ];

        yield 'Manager can upload file announcement task if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => Response::HTTP_OK,
        ];

        yield 'Creator cannot update announcement task' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_FORBIDDEN,
        ];

        yield 'Tutor can upload file announcement task if announcement is shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => Response::HTTP_OK,
        ];

        yield 'Tutor cannot upload file announcement task if announcement is not shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_FORBIDDEN,
        ];

        yield 'Support cannot upload file announcement task' => [
            'roles' => [User::ROLE_SUPPORT],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_FORBIDDEN,
        ];

        yield 'Inspector cannot upload file announcement task' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_FOUND,
        ];

        yield 'User cannot upload file announcement task' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => Response::HTTP_FOUND,
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            AnnouncementManager::class,
            TaskCourse::class,
            UserLogin::class,
            UserManage::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->testUser1->getId(),
            $this->testUser2->getId(),
        ]);

        parent::tearDown();
    }
}
