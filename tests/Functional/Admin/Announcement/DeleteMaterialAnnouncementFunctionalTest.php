<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\MaterialCourse;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\MaterialCourseHelperTrait;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class DeleteMaterialAnnouncementFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use MaterialCourseHelperTrait;
    private User $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(email: '<EMAIL>');
    }

    #[DataProvider('providerAnnouncementMaterialsDeleteAuthorization')]
    public function testDeleteMaterialAnnouncement(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $isCreatorMaterial,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode
    ): void {
        $this->testUser->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);
        $user = $this->getDefaultUser();

        $course = $this->createAndGetCourse(createdBy: $isCreatorAnnouncement ? $this->testUser : $user);
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $isCreatorAnnouncement ? $this->testUser : $user,
        );

        $materialCourse = $this->createAndGetMaterialCourse(
            course: $course,
            announcement: $announcement,
            createdBy: $isCreatorMaterial ? $this->testUser : $user
        );

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            $this->updateSettingValue(value: '1', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);
                $this->createAndGetAnnouncementTutor(
                    announcement: $announcement,
                    announcementGroup: $announcementGroup,
                    tutor: $this->testUser
                );
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }
        $userToken = $this->loginAndGetTokenForUser($this->testUser);
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteMaterialsAnnouncementEndpoint($materialCourse->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerAnnouncementMaterialsDeleteAuthorization(): \Generator
    {
        yield 'Super Admin can delete materials' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Admin can delete materials' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can delete materials if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];
        yield 'Manager can delete materials if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Creator cannot delete materials' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 500,
        ];
        yield 'Tutor cannot delete materials' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 500,
        ];
        yield 'Tutor cannot delete materials if the announcement is shared with him but is not the material creator' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 500,
        ];
        yield 'Tutor can delete materials if the announcement is shared with him and is the material creator' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => true,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];
        yield 'Inspector cannot delete materials' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
        yield 'User cannot delete materials' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'isCreatorMaterial' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: '0',
        );
        $this->truncateEntities([
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            Course::class,
            CourseCategory::class,
            MaterialCourse::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->testUser->getId(),
        ]);

        parent::tearDown();
    }
}
