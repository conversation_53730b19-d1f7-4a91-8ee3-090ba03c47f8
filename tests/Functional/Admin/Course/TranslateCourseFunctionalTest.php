<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class TranslateCourseFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;

    private User $creator1;
    private User $creator2;
    private User $manager1;
    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->creator1 = $this->createAndGetUser(roles: [User::ROLE_CREATOR], email: '<EMAIL>');
        $this->creator2 = $this->createAndGetUser(roles: [User::ROLE_CREATOR], email: '<EMAIL>');
        $this->manager1 = $this->createAndGetUser(roles: [User::ROLE_MANAGER], email: '<EMAIL>');
        $this->admin = $this->createAndGetUser(roles: [User::ROLE_ADMIN], email: '<EMAIL>');
    }

    public function testAsAdmin(): void
    {
        $course = $this->createAndGetCourse(createdBy: $this->creator1);
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::translateCourseV1Endpoint(courseId: $course->getId()),
            body: [
                'language' => 'pt',
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testAsTutor(): void
    {
        $course = $this->createAndGetCourse(createdBy: $this->creator1);
        $course2 = $this->createAndGetCourse(name: 'course 2', createdBy: $this->creator2);
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Try to translate its own created course
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::translateCourseV1Endpoint(courseId: $course->getId()),
            body: [
                'language' => 'pt',
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        // Try to translate not shared course
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::translateCourseV1Endpoint(courseId: $course2->getId()),
            body: [
                'language' => 'pt',
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User <EMAIL> is not allowed to manage the content of this course.',
            $content['message']
        );

        // Share the course with the creator and try to translate it
        $this->setAndGetCourseCreatorInRepository(
            userId: new Id($this->creator1->getId()),
            courseId: new Id($course2->getId()),
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::translateCourseV1Endpoint(courseId: $course2->getId()),
            body: [
                'language' => 'pt',
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public function testAsManager(): void
    {
        $course = $this->createAndGetCourse(createdBy: $this->creator1);
        $token = $this->loginAndGetToken(email: '<EMAIL>');
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminCourseEndpoints::translateCourseV1Endpoint(courseId: $course->getId()),
            body: [
                'language' => 'pt',
            ],
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals(
            'User <EMAIL> is not allowed to manage the content of this course.',
            $content['message']
        );
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
        ]);
        $this->hardDeleteUsersByIds([
            $this->creator1->getId(), $this->creator2->getId(), $this->manager1->getId(), $this->admin->getId(),
        ]);
        parent::tearDown();
    }
}
