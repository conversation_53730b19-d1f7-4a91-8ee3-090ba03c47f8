<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\FilesTask;
use App\Entity\TaskCourse;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create File Task.
 */
trait FileTaskHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetFileTask(
        ?TaskCourse $taskCourse = null,
        string $fileName = 'testing.pdf',
        string $typeMaterial = '1',
        string $urlMaterial = '',
        bool $isDownloadable = false,
    ): FilesTask {
        $em = $this->getEntityManager();
        $fileTask = new FilesTask();
        $fileTask->setTypeMaterial($typeMaterial)
            ->setFilename($fileName)
            ->setUrlMaterial($urlMaterial)
            ->setIsDownload($isDownloadable)
            ->setTaskCourse($taskCourse);

        $em->persist($fileTask);
        $em->flush();

        return $fileTask;
    }
}
