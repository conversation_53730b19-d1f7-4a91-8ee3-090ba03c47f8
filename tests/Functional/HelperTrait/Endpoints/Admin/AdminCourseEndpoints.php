<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class AdminCourseEndpoints
{
    public static function preDataEndpoint(): string
    {
        return '/course/pre-data';
    }

    public static function coursesEndpoint(): string
    {
        return '/api/v1/courses';
    }

    public static function coursesDetailEndpoint(int $courseId): string
    {
        return "/api/v1/courses/{$courseId}";
    }

    public static function adminGetCourseEndpoint(int $courseId): string
    {
        return "/course/{$courseId}";
    }

    public static function adminCourseEndpoint(): string
    {
        return '/course';
    }

    public static function adminUpdateCourseEndpoint(int $courseId): string
    {
        return "/course/update/{$courseId}";
    }

    public static function coursesDeleteEndPoint(int $courseId): string
    {
        return "/api/v1/courses/{$courseId}";
    }

    public static function coursesUsersStatsEndPoint(int $courseId): string
    {
        return "/api/v1/course-stats/{$courseId}/users";
    }

    public static function coursesPublishEndPoint(int $courseId): string
    {
        return "/api/v1/courses/{$courseId}/active";
    }

    public static function getChaptersCourse(int $courseId): string
    {
        return "/api/v1/courses/$courseId/chapters";
    }

    public static function getCourseAnnouncementsEndpoint(int $courseId): string
    {
        return "/api/v1/courses/$courseId/announcements";
    }

    public static function getCourseCreatorsEndpoint(int $courseId): string
    {
        return "/api/v2/admin/courses/$courseId/creators";
    }

    public static function putCourseCreatorEndpoint(int $courseId, int $userId): string
    {
        return "/api/v2/admin/courses/$courseId/creators/$userId";
    }

    public static function deleteCourseCreatorEndpoint(int $courseId, int $userId): string
    {
        return "/api/v2/admin/courses/$courseId/creators/$userId";
    }

    public static function translateCourseV1Endpoint(int $courseId): string
    {
        return \sprintf('/admin/api/v1/courses/%d/translate', $courseId);
    }
}
