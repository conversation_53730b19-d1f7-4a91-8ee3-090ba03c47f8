<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Campus;

class CampusPurchaseEndpoints
{
    public static function purchasesEndpoint(): string
    {
        return '/api/v2/purchases';
    }

    public static function getPurchaseEndpoint(string $purchaseId): string
    {
        return self::purchasesEndpoint() . '/' . $purchaseId;
    }

    public static function patchPurchaseBillingDataEndpoint(string $purchaseId): string
    {
        return self::getPurchaseEndpoint($purchaseId) . '/billing-data';
    }
}
