<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\FilesManager;
use App\Entity\Task;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Tests\Mother\Entity\TaskMother;
use App\Tests\Mother\Entity\ZipFileTaskMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait TaskHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetTask(
        ?string $taskName = null,
        ?string $type = null,
        ?string $status = null,
        array $params = [],
        ?\DateTimeInterface $createdAt = null,
        ?\DateTimeInterface $finishedAt = null,
        ?\DateTimeInterface $startedAt = null,
        ?User $createdBy = null,
    ): Task {
        $em = $this->getEntityManager();

        $task = TaskMother::create(
            taskName: $taskName,
            type: $type,
            status: $status,
            params: $params,
            createdBy: $createdBy,
            createdAt: $createdAt,
            finishedAt: $finishedAt,
            startedAt: $startedAt,
        );

        $em->persist($task);
        $em->flush();

        return $task;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetZipFileTask(
        ?string $type = null,
        ?string $entityId = null,
        ?array $params = [],
        ?string $task = null,
        ?string $status = null,
        ?string $filename = null,
        ?string $originalName = null,
        ?FilesManager $filesManager = null,
        ?\DateTimeInterface $startedAt = null,
        ?\DateTimeInterface $finishedAt = null,
        ?\DateTimeInterface $createdAt = null,
        ?User $createdBy = null,
    ): ZipFileTask {
        $em = $this->getEntityManager();
        $zipFileTask = ZipFileTaskMother::create(
            type: $type,
            entityId: $entityId,
            params: $params,
            task: $task,
            status: $status,
            filename: $filename,
            originalName: $originalName,
            startedAt: $startedAt,
            finishedAt: $finishedAt,
            filesManager: $filesManager,
            createdAt: $createdAt,
            createdBy: $createdBy,
        );

        $em->persist($zipFileTask);
        $em->flush();

        return $zipFileTask;
    }
}
