<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\Season;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class DeleteEntitiesTest extends FunctionalTestCase
{
    private ?TokenStorageInterface $tokenStorage = null;
    private ?EntityManagerInterface $entityManager = null;
    private ?User $defaultUser = null;
    private ?Course $testCourse = null;
    private ?Chapter $testChapter = null;
    private ?UserCourse $testUserCourse = null;
    private ?UserCourseChapter $testUserCourseChapter = null;
    private ?Season $testSeason = null;
    private const TEST_DEBUG_MODE = false;

    protected function setUp(): void
    {
        parent::setUp();
        self::$debugMode = self::TEST_DEBUG_MODE;
        $this->tokenStorage = $this->client->getContainer()->get(TokenStorageInterface::class);
        $this->entityManager = $this->getEntityManager();

        // Clean up entities that may exist from previous tests
        $this->truncateEntities([UserCourseChapter::class, UserCourse::class, Chapter::class, Season::class, Course::class]);

        // Get the default user
        $this->defaultUser = $this->getDefaultUser();

        // Set the user in the security token for DeletedByListener to use
        $token = new UsernamePasswordToken($this->defaultUser, null, 'main', $this->defaultUser->getRoles());
        $this->tokenStorage->setToken($token);

        // Temporarily activate the DeletedByListener
        $this->activateDeletedByListener();

        // Create test entities
        $this->createTestEntities();
    }



    private function createTestEntities(): void
    {
        // Create a test course
        $this->testCourse = new Course();
        $this->testCourse->setName('Test course for deletion');
        $this->testCourse->setCode('TEST-DEL-' . uniqid());
        $this->testCourse->setLocale('es');
        $this->testCourse->setCreatedBy($this->defaultUser);
        $this->testCourse->setOpen(true);
        $this->testCourse->setIsNew(false);
        $this->entityManager->persist($this->testCourse);

        // Create a test season
        $this->testSeason = new Season();
        $this->testSeason->setName('Test season');
        $this->testSeason->setCourse($this->testCourse);
        $this->testSeason->setSort(1);
        $this->testSeason->setType(Season::TYPE_SEQUENTIAL);
        $this->entityManager->persist($this->testSeason);

        // Create a test chapter
        $this->testChapter = new Chapter();
        $this->testChapter->setTitle('Test chapter for deletion');
        $this->testChapter->setCourse($this->testCourse);
        $this->testChapter->setPosition(1);

        // Get an existing chapter type
        $chapterType = $this->entityManager->getRepository(ChapterType::class)->findOneBy([]);
        if (!$chapterType) {
            $this->fail('No ChapterType found in the database');
        }

        $this->testChapter->setType($chapterType);
        $this->testChapter->setSeason($this->testSeason);
        $this->entityManager->persist($this->testChapter);

        // Create a test UserCourse
        $this->testUserCourse = new UserCourse();
        $this->testUserCourse->setUser($this->defaultUser);
        $this->testUserCourse->setCourse($this->testCourse);
        $this->testUserCourse->setStartedAt(new \DateTime());
        $this->entityManager->persist($this->testUserCourse);

        // Create a test UserCourseChapter
        $this->testUserCourseChapter = new UserCourseChapter();
        $this->testUserCourseChapter->setUserCourse($this->testUserCourse);
        $this->testUserCourseChapter->setChapter($this->testChapter);
        $this->testUserCourseChapter->setStartedAt(new \DateTime());
        $this->testUserCourseChapter->setUpdatedAt(new \DateTime());
        $this->entityManager->persist($this->testUserCourseChapter);

        // Save all entities
        $this->entityManager->flush();
    }

    public function testSetSecurityToken(): void
    {
        // Verify that the security token has been set correctly
        $this->assertNotNull($this->tokenStorage->getToken(), 'The security token must be set');
        $this->assertSame($this->defaultUser, $this->tokenStorage->getToken()->getUser(), 'The token user must be the default user');

        // Verify that the token user has the expected ID
        $this->assertEquals(self::DEFAULT_USER_ID, $this->defaultUser->getId(), 'The user ID must be the expected one');
    }

    public function testDeleteCourse(): void
    {
        $this->log("Starting testDeleteCourse()");

        // Verify that the course exists
        $courseId = $this->testCourse->getId();
        $this->assertNotNull($courseId, 'The course must have an ID');

        // Delete the course
        $this->entityManager->remove($this->testCourse);
        $this->entityManager->flush();

        // Verify that the course has been deleted and that the user who deleted it has been correctly set
        $this->verifyDeletedBy(Course::class, 'course', $courseId);
    }

    public function testDeleteUserCourse(): void
    {
        $this->log("Starting testDeleteUserCourse()");

        // Verify that the UserCourse exists
        $userCourseId = $this->testUserCourse->getId();
        $this->assertNotNull($userCourseId, 'The UserCourse must have an ID');

        // Delete the UserCourse
        $this->entityManager->remove($this->testUserCourse);
        $this->entityManager->flush();

        // Verify that the UserCourse has been deleted and that the user who deleted it has been correctly set
        $this->verifyDeletedBy(UserCourse::class, 'user_course', $userCourseId);
    }

    public function testDeleteUserCourseChapter(): void
    {
        $this->log("Starting testDeleteUserCourseChapter()");

        // Verify that the UserCourseChapter exists
        $userCourseChapterId = $this->testUserCourseChapter->getId();
        $this->assertNotNull($userCourseChapterId, 'The UserCourseChapter must have an ID');

        // Delete the UserCourseChapter
        $this->entityManager->remove($this->testUserCourseChapter);
        $this->entityManager->flush();

        // Verify that the UserCourseChapter has been deleted and that the user who deleted it has been correctly set
        $this->verifyDeletedBy(UserCourseChapter::class, 'user_course_chapter', $userCourseChapterId);
    }

    /**
     * Helper method to verify that the user who deleted the entity has been correctly set
     */
    private function verifyDeletedBy(string $entityClass, string $tableName, int $entityId): void
    {
        // Verify that the entity has been deleted (soft delete)
        $this->entityManager->clear();
        $deletedEntity = $this->entityManager->find($entityClass, $entityId);
        $this->assertNull($deletedEntity, 'The entity should have been deleted (soft delete)');

        // Verify that the user who deleted the entity has been correctly set
        $connection = $this->entityManager->getConnection();
        $sql = "SELECT * FROM $tableName WHERE id = :id AND deleted_at IS NOT NULL";
        $stmt = $connection->prepare($sql);
        $stmt->bindValue('id', $entityId);
        $result = $stmt->executeQuery()->fetchAssociative();
        $this->log("SQL query result: " . json_encode($result));

        // Verify that the user who deleted the entity is the default user
        $this->assertNotNull($result, 'The entity should exist in the database (soft delete)');
        $this->log("deleted_by_id in the database: " . ($result['deleted_by_id'] ?? 'null'));
        $this->log("Default user ID: {$this->defaultUser->getId()}");
        $this->assertEquals($this->defaultUser->getId(), $result['deleted_by_id'], 'The user who deleted the entity should be the default user');
    }

    protected function tearDown(): void
    {
        // Deactivate the DeletedByListener
        $this->deactivateDeletedByListener();

        // Clean up all entities created for the tests
        $this->truncateEntities([UserCourseChapter::class, UserCourse::class, Chapter::class, Season::class, Course::class]);

        parent::tearDown();
    }
}
