<?php

declare(strict_types=1);

namespace App\Tests\Functional\Mock;

use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\RawMessage;

class InMemoryMailer implements MailerInterface
{
    /**
     * @var RawMessage[]
     */
    private array $messages = [];

    /**
     * {@inheritDoc}
     */
    public function send(RawMessage $message, ?Envelope $envelope = null): void
    {
        $this->messages[] = $message;
    }

    /**
     * @return RawMessage[]
     */
    public function getMessages(): array
    {
        return $this->messages;
    }
}
