<?php

declare(strict_types=1);

namespace App\Tests\Unit\Repository;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Repository\UserCourseChapterRepository;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Tests\Mother\Entity\ChapterMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\UserCourseMother;
use App\Tests\Mother\Entity\UserMother;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UserCourseChapterRepositoryTest extends TestCase
{
    private UserCourseChapterRepository $repository;
    private ChapterQueryParams $chapterQueryParams;
    private Course $course;
    private Chapter $chapter;
    private User $user1;
    private User $user2;
    private UserCourse $userCourse1;
    private UserCourse $userCourse2;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = $this->createMock(UserCourseChapterRepository::class);
        $this->course = CourseMother::create();
        $this->chapter = ChapterMother::create(course: $this->course);
        $this->user1 = UserMother::create(id: 1);
        $this->user2 = UserMother::create(id: 2);
        $this->userCourse1 = UserCourseMother::create(user: $this->user1, course: $this->course, timeSpent: 10);
        $this->userCourse2 = UserCourseMother::create(user: $this->user2, course: $this->course, timeSpent: 20);
        $this->chapterQueryParams = new ChapterQueryParams($this->chapter, false, []);
    }

    /**
     * @throws Exception
     */
    public function testGetTimeByChapter()
    {
        $chapterTimeSpent = $this->userCourse1->getTimeSpent() + $this->userCourse2->getTimeSpent();

        $this->repository->method('getTimeByChapter')->willReturn(['totalTime' => $chapterTimeSpent]);

        $result = $this->repository->getTimeByChapter($this->chapterQueryParams);

        $this->assertEquals(['totalTime' => $chapterTimeSpent], $result);
    }

    /**
     * @throws Exception
     */
    public function testGetTotalUserFinishedChapters()
    {
        $chapterTimeSpent = $this->userCourse1->getTimeSpent() + $this->userCourse2->getTimeSpent();
        $this->repository->method('getTotalUserFinishedChapters')->willReturn(['finished' => 2, 'totalTimeSpent' => $chapterTimeSpent]);

        $result = $this->repository->getTotalUserFinishedChapters($this->chapterQueryParams);

        $this->assertEquals(['finished' => 2, 'totalTimeSpent' => $chapterTimeSpent], $result);
    }

    public function testChapterGetTotalUsersStarted()
    {
        $this->repository->method('chapterGetTotalUsersStarted')->willReturn(2);

        $result = $this->repository->chapterGetTotalUsersStarted($this->chapterQueryParams);

        $this->assertEquals(2, $result);
    }

    public function testFindUsersInChapterAndFinishedCourse()
    {
        $this->repository->method('findUsersInChapterAndFinishedCourse')->willReturn(true);

        $result = $this->repository->findUsersInChapterAndFinishedCourse($this->chapterQueryParams);

        $this->assertTrue($result);
    }
}
