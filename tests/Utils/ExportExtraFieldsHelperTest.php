<?php

declare(strict_types=1);

namespace App\Tests\Utils;

use App\Utils\ExportExtraFieldsHelper;
use PHPUnit\Framework\TestCase;

class ExportExtraFieldsHelperTest extends TestCase
{
    /**
     * @dataProvider  userExportExtraFieldsValuesProvider
     */
    public function testUserExportExtraFieldsValues(
        array $userExtraFields,
        array $userExtraFieldTranslatedHeaders,
        string $locale,
        array $expectedValues,
        $expectedException = null
    ): void {
        if ($expectedException) {
            $this->expectException($expectedException);
        }

        $result = ExportExtraFieldsHelper::getExportExtraFieldsValues($this->getExtraFields(), $userExtraFields, $userExtraFieldTranslatedHeaders, $locale);

        if (!$expectedException) {
            $this->assertEquals($expectedValues, $result);
        }
    }

    public static function userExportExtraFieldsValuesProvider(): \Generator
    {
        yield 'User export' => [
            'userExtraFields' => [
                'input_select' => 1,
                'input_text' => 'Input text',
            ],
            'userExtraFieldTranslatedHeaders' => [
                'input_select' => 'Input select',
                'input_text' => 'Input text',
            ],
            'locale' => 'en',
            'expectedValues' => [
                'Input select' => 'Option 1',
                'Input text' => 'Input text',
            ],
        ];

        yield 'User export with non int input value. Exception expected ' => [
            'userExtraFields' => [
                'input_select' => 'Uno',
                'input_text' => 'Input text',
            ],
            'userExtraFieldTranslatedHeaders' => [
                'input_select' => 'Input select',
                'input_text' => 'Input text',
            ],
            'locale' => 'en',
            'expectedValues' => [
                'Input select' => 'Option 1',
                'Input text' => 'Input text',
            ],
            'expectedException' => \UnexpectedValueException::class,
        ];
    }

    /**
     * @dataProvider  userExportExtraFieldsHeadersProvider
     */
    public function testUserExportExtraFieldsHeaders(
        string $locale,
        array $expectedValues
    ): void {
        $result = ExportExtraFieldsHelper::getExportHeaders($this->getExtraFields(), $locale);
        $this->assertEquals($expectedValues, $result);
    }

    public static function userExportExtraFieldsHeadersProvider(): \Generator
    {
        yield 'User export headers in english' => [
            'locale' => 'en',
            'expectedValues' => [
                'input_select' => 'Input select',
                'input_text' => 'Input text',
            ],
        ];

        yield 'User export headers spanish' => [
            'locale' => 'es',
            'expectedValues' => [
                'input_select' => 'Selector de entrada',
                'input_text' => 'Texto de entrada',
            ],
        ];
    }

    protected function getExtraFields(): array
    {
        return [
            [
                'name' => 'input_select',
                'required' => true,
                'label' => [
                    'default' => 'Input select',
                    'translations' => [
                        [
                            'language' => 'es',
                            'value' => 'Selector de entrada',
                        ],
                        [
                            'language' => 'en',
                            'value' => 'Input select',
                        ],
                    ],
                ],
                'type' => 'select',
                'options' => [
                    [
                        'value' => 1,
                        'name' => [
                            'default' => 'Option 1',
                            'translations' => [
                                [
                                    'language' => 'es',
                                    'value' => "Opci\u00f3n 1",
                                ],
                                [
                                    'language' => 'en',
                                    'value' => 'Option 1',
                                ],
                            ],
                        ],
                    ],
                    [
                        'value' => 2,
                        'name' => [
                            'default' => 'Option 2',
                            'translations' => [
                                [
                                    'language' => 'es',
                                    'value' => "Opci\u00f3n 2",
                                ],
                                [
                                    'language' => 'en',
                                    'value' => 'Option 2',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'name' => 'input_text',
                'required' => true,
                'label' => [
                    'default' => 'Input text',
                    'translations' => [
                        [
                            'language' => 'es',
                            'value' => 'Texto de entrada',
                        ],
                        [
                            'language' => 'en',
                            'value' => 'Input text',
                        ]],
                ],
                'type' => 'text',
            ],
        ];
    }
}
